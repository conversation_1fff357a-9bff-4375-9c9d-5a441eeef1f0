import 'package:equatable/equatable.dart';
import '../../../../core/models/patient_model.dart';

abstract class PatientsEvent extends Equatable {
  const PatientsEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllPatients extends PatientsEvent {}

class LoadPremiumPatients extends PatientsEvent {}

class SearchPatients extends PatientsEvent {
  final String query;

  const SearchPatients({required this.query});

  @override
  List<Object?> get props => [query];
}

class CreatePatient extends PatientsEvent {
  final PatientModel patient;

  const CreatePatient({required this.patient});

  @override
  List<Object?> get props => [patient];
}

class UpdatePatient extends PatientsEvent {
  final PatientModel patient;

  const UpdatePatient({required this.patient});

  @override
  List<Object?> get props => [patient];
}

class DeletePatient extends PatientsEvent {
  final String patientId;

  const DeletePatient({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class UpgradeToPremium extends PatientsEvent {
  final String patientId;

  const UpgradeToPremium({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class DowngradeFromPremium extends PatientsEvent {
  final String patientId;

  const DowngradeFromPremium({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class RefreshPatients extends PatientsEvent {}
