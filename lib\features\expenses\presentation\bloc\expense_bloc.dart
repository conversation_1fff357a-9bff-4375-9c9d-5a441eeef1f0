import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iihc_admin/core/models/expense_model.dart';
import '../../data/repositories/expense_repository.dart';
import 'expense_event.dart';
import 'expense_state.dart';

class ExpenseBloc extends Bloc<ExpenseEvent, ExpenseState> {
  final ExpenseRepository _expenseRepository;
  static const int _pageSize = 20;

  ExpenseBloc({required ExpenseRepository expenseRepository})
      : _expenseRepository = expenseRepository,
        super(ExpenseInitial()) {
    on<LoadExpenses>(_onLoadExpenses);
    on<CreateExpense>(_onCreateExpense);
    on<UpdateExpense>(_onUpdateExpense);
    on<DeleteExpense>(_onDeleteExpense);
    on<SearchExpenses>(_onSearchExpenses);
    on<FilterExpensesByCategory>(_onFilterExpensesByCategory);
    on<ClearExpenseFilters>(_onClearExpenseFilters);
  }

  Future<void> _onLoadExpenses(
    LoadExpenses event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Loading expenses - Page: ${event.page}', name: 'ExpenseBloc');
      
      if (event.page == 1) {
        emit(ExpenseLoading());
      }

      final expenses = await _expenseRepository.getAllExpenses(
        page: event.page,
        limit: _pageSize,
        searchQuery: event.searchQuery,
        category: event.category,
        dateFilter: event.dateFilter,
      );

      final hasReachedMax = expenses.length < _pageSize;

      if (event.page == 1) {
        emit(ExpenseLoaded(
          expenses: expenses,
          hasReachedMax: hasReachedMax,
          currentPage: event.page,
          searchQuery: event.searchQuery,
          categoryFilter: event.category,
          dateFilter: event.dateFilter,
        ));
      } else {
        final currentState = state;
        if (currentState is ExpenseLoaded) {
          emit(currentState.copyWith(
            expenses: [...currentState.expenses, ...expenses],
            hasReachedMax: hasReachedMax,
            currentPage: event.page,
          ));
        }
      }

      developer.log('Loaded ${expenses.length} expenses', name: 'ExpenseBloc');
    } catch (e) {
      developer.log('Error loading expenses: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }

  Future<void> _onCreateExpense(
    CreateExpense event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Creating expense: ${event.expense.title}', name: 'ExpenseBloc');
      
      final createdExpense = await _expenseRepository.createExpense(event.expense);
      
      emit(ExpenseCreated(createdExpense));
      
      // Reload expenses
      add(const LoadExpenses());
      
      developer.log('Expense created successfully', name: 'ExpenseBloc');
    } catch (e) {
      developer.log('Error creating expense: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }

  Future<void> _onUpdateExpense(
    UpdateExpense event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Updating expense: ${event.expense.id}', name: 'ExpenseBloc');
      
      final updatedExpense = await _expenseRepository.updateExpense(event.expense);
      
      emit(ExpenseUpdated(updatedExpense));
      
      // Reload expenses
      add(const LoadExpenses());
      
      developer.log('Expense updated successfully', name: 'ExpenseBloc');
    } catch (e) {
      developer.log('Error updating expense: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }

  Future<void> _onDeleteExpense(
    DeleteExpense event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Deleting expense: ${event.expenseId}', name: 'ExpenseBloc');
      
      await _expenseRepository.deleteExpense(event.expenseId);
      
      emit(ExpenseDeleted(event.expenseId));
      
      // Reload expenses
      add(const LoadExpenses());
      
      developer.log('Expense deleted successfully', name: 'ExpenseBloc');
    } catch (e) {
      developer.log('Error deleting expense: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }

  Future<void> _onSearchExpenses(
    SearchExpenses event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Searching expenses: ${event.query}', name: 'ExpenseBloc');
      
      final currentState = state;
      if (currentState is ExpenseLoaded) {
        add(LoadExpenses(
          searchQuery: event.query.isEmpty ? null : event.query,
          category: currentState.categoryFilter,
          dateFilter: currentState.dateFilter,
        ));
      } else {
        add(LoadExpenses(
          searchQuery: event.query.isEmpty ? null : event.query,
        ));
      }
    } catch (e) {
      developer.log('Error searching expenses: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }

  Future<void> _onFilterExpensesByCategory(
    FilterExpensesByCategory event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Filtering expenses by category: ${event.category?.arabicName ?? 'All'}', name: 'ExpenseBloc');
      
      final currentState = state;
      if (currentState is ExpenseLoaded) {
        add(LoadExpenses(
          searchQuery: currentState.searchQuery,
          category: event.category,
          dateFilter: currentState.dateFilter,
        ));
      } else {
        add(LoadExpenses(
          category: event.category,
        ));
      }
    } catch (e) {
      developer.log('Error filtering expenses: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }

  Future<void> _onClearExpenseFilters(
    ClearExpenseFilters event,
    Emitter<ExpenseState> emit,
  ) async {
    try {
      developer.log('Clearing expense filters', name: 'ExpenseBloc');
      
      add(const LoadExpenses());
    } catch (e) {
      developer.log('Error clearing filters: $e', name: 'ExpenseBloc');
      emit(ExpenseError(e.toString()));
    }
  }
}
