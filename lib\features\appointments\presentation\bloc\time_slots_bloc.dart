import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../data/repositories/time_slots_repository.dart';
import 'time_slots_event.dart';
import 'time_slots_state.dart';

class TimeSlotsBloc extends Bloc<TimeSlotsEvent, TimeSlotsState> {
  final TimeSlotsRepository _timeSlotsRepository;

  TimeSlotsBloc({required TimeSlotsRepository timeSlotsRepository})
      : _timeSlotsRepository = timeSlotsRepository,
        super(TimeSlotsInitial()) {
    on<LoadAllTimeSlots>(_onLoadAllTimeSlots);
    on<LoadActiveTimeSlots>(_onLoadActiveTimeSlots);
    on<LoadTimeSlotsByDay>(_onLoadTimeSlotsByDay);
    on<CreateTimeSlot>(_onCreateTimeSlot);
    on<UpdateTimeSlot>(_onUpdateTimeSlot);
    on<DeleteTimeSlot>(_onDeleteTimeSlot);
    on<ToggleTimeSlotStatus>(_onToggleTimeSlotStatus);
    on<GenerateAppointments>(_onGenerateAppointments);
    on<RefreshTimeSlots>(_onRefreshTimeSlots);
  }

  Future<void> _onLoadAllTimeSlots(
    LoadAllTimeSlots event,
    Emitter<TimeSlotsState> emit,
  ) async {
    debugPrint('🔄 TimeSlotsBloc: Starting to load all time slots...');
    emit(TimeSlotsLoading());
    try {
      debugPrint('📞 TimeSlotsBloc: Calling repository...');
      final timeSlots = await _timeSlotsRepository.getAllTimeSlots();
      debugPrint('📊 TimeSlotsBloc: Received ${timeSlots.length} time slots');

      final timeSlotsByDay = _groupTimeSlotsByDay(timeSlots);
      debugPrint('📋 TimeSlotsBloc: Grouped into ${timeSlotsByDay.length} days');

      emit(TimeSlotsLoaded(
        timeSlots: timeSlots,
        timeSlotsByDay: timeSlotsByDay,
      ));
      debugPrint('✅ TimeSlotsBloc: Successfully emitted TimeSlotsLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ TimeSlotsBloc Error: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      emit(TimeSlotsError(message: e.toString()));
    }
  }

  Future<void> _onLoadActiveTimeSlots(
    LoadActiveTimeSlots event,
    Emitter<TimeSlotsState> emit,
  ) async {
    emit(TimeSlotsLoading());
    try {
      final timeSlots = await _timeSlotsRepository.getActiveTimeSlots();
      final timeSlotsByDay = _groupTimeSlotsByDay(timeSlots);

      emit(TimeSlotsLoaded(
        timeSlots: timeSlots,
        timeSlotsByDay: timeSlotsByDay,
      ));
    } catch (e) {
      debugPrint('❌ Error loading active time slots: $e');
      emit(TimeSlotsError(message: e.toString()));
    }
  }

  Future<void> _onLoadTimeSlotsByDay(
    LoadTimeSlotsByDay event,
    Emitter<TimeSlotsState> emit,
  ) async {
    emit(TimeSlotsLoading());
    try {
      final timeSlots = await _timeSlotsRepository.getTimeSlotsByDay(event.dayOfWeek);
      final timeSlotsByDay = _groupTimeSlotsByDay(timeSlots);

      emit(TimeSlotsLoaded(
        timeSlots: timeSlots,
        timeSlotsByDay: timeSlotsByDay,
      ));
    } catch (e) {
      debugPrint('❌ Error loading time slots by day: $e');
      emit(TimeSlotsError(message: e.toString()));
    }
  }

  Future<void> _onCreateTimeSlot(
    CreateTimeSlot event,
    Emitter<TimeSlotsState> emit,
  ) async {
    emit(TimeSlotsLoading());
    try {
      final createdTimeSlot = await _timeSlotsRepository.createTimeSlot(event.timeSlot);
      emit(TimeSlotCreated(timeSlot: createdTimeSlot));

      // Refresh the list
      add(LoadAllTimeSlots());
    } catch (e) {
      debugPrint('❌ Error creating time slot: $e');
      String errorMessage = 'فشل في إنشاء الموعد';

      if (e.toString().contains('يتداخل هذا الموعد')) {
        errorMessage = 'يتداخل هذا الموعد مع موعد آخر في نفس اليوم';
      } else if (e.toString().contains('Invalid date format')) {
        errorMessage = 'تنسيق الوقت غير صحيح';
      } else if (e.toString().contains('FormatException')) {
        errorMessage = 'خطأ في تنسيق البيانات';
      }

      emit(TimeSlotsError(message: errorMessage));
    }
  }

  Future<void> _onUpdateTimeSlot(
    UpdateTimeSlot event,
    Emitter<TimeSlotsState> emit,
  ) async {
    emit(TimeSlotsLoading());
    try {
      final updatedTimeSlot = await _timeSlotsRepository.updateTimeSlot(event.timeSlot);
      emit(TimeSlotUpdated(timeSlot: updatedTimeSlot));

      // Refresh the list
      add(LoadAllTimeSlots());
    } catch (e) {
      debugPrint('❌ Error updating time slot: $e');
      String errorMessage = 'فشل في تحديث الموعد';

      if (e.toString().contains('يتداخل هذا الموعد')) {
        errorMessage = 'يتداخل هذا الموعد مع موعد آخر في نفس اليوم';
      } else if (e.toString().contains('Invalid date format')) {
        errorMessage = 'تنسيق الوقت غير صحيح';
      } else if (e.toString().contains('FormatException')) {
        errorMessage = 'خطأ في تنسيق البيانات';
      }

      emit(TimeSlotsError(message: errorMessage));
    }
  }

  Future<void> _onDeleteTimeSlot(
    DeleteTimeSlot event,
    Emitter<TimeSlotsState> emit,
  ) async {
    emit(TimeSlotsLoading());
    try {
      await _timeSlotsRepository.deleteTimeSlot(event.timeSlotId);
      emit(TimeSlotDeleted(timeSlotId: event.timeSlotId));

      // Refresh the list
      add(LoadAllTimeSlots());
    } catch (e) {
      debugPrint('❌ Error deleting time slot: $e');
      emit(TimeSlotsError(message: e.toString()));
    }
  }

  Future<void> _onToggleTimeSlotStatus(
    ToggleTimeSlotStatus event,
    Emitter<TimeSlotsState> emit,
  ) async {
    try {
      final updatedTimeSlot = await _timeSlotsRepository.toggleTimeSlotStatus(event.timeSlotId);
      emit(TimeSlotUpdated(timeSlot: updatedTimeSlot));

      // Refresh the list
      add(LoadAllTimeSlots());
    } catch (e) {
      debugPrint('❌ Error toggling time slot status: $e');
      emit(TimeSlotsError(message: e.toString()));
    }
  }

  Future<void> _onGenerateAppointments(
    GenerateAppointments event,
    Emitter<TimeSlotsState> emit,
  ) async {
    emit(TimeSlotsLoading());
    try {
      await _timeSlotsRepository.generateAppointmentsForTimeSlots(
        weeksAhead: event.weeksAhead,
      );
      emit(AppointmentsGenerated(weeksGenerated: event.weeksAhead));

      // Refresh the list
      add(LoadAllTimeSlots());
    } catch (e) {
      debugPrint('❌ Error generating appointments: $e');
      emit(TimeSlotsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshTimeSlots(
    RefreshTimeSlots event,
    Emitter<TimeSlotsState> emit,
  ) async {
    add(LoadAllTimeSlots());
  }

  // Helper method to group time slots by day
  Map<int, List<TimeSlotModel>> _groupTimeSlotsByDay(List<TimeSlotModel> timeSlots) {
    final Map<int, List<TimeSlotModel>> grouped = {};

    for (final timeSlot in timeSlots) {
      if (!grouped.containsKey(timeSlot.dayOfWeek)) {
        grouped[timeSlot.dayOfWeek] = [];
      }
      grouped[timeSlot.dayOfWeek]!.add(timeSlot);
    }

    // Sort time slots within each day by start time
    for (final daySlots in grouped.values) {
      daySlots.sort((a, b) => a.startTime.compareTo(b.startTime));
    }

    return grouped;
  }
}
