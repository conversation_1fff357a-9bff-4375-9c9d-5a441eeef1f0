import '../utils/logger.dart';
import '../network/supabase_client.dart';

class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final Logger _logger = Logger('NotificationManager');

  // Send custom notification via FCM HTTP API
  Future<bool> sendCustomNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      _logger.info('📤 Sending custom notification to user: $userId');

      // Get user's FCM token from database
      final tokenResponse = await SupabaseConfig.client
          .from('user_fcm_tokens')
          .select('fcm_token')
          .eq('user_id', userId)
          .eq('is_active', true)
          .maybeSingle();

      if (tokenResponse == null) {
        _logger.warning('⚠️ No active FCM token found for user: $userId');
        return false;
      }

      final fcmToken = tokenResponse['fcm_token'] as String;
      
      // Send notification via FCM
      final success = await _sendFCMNotification(
        fcmToken: fcmToken,
        title: title,
        body: body,
        data: {
          'type': type,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
          ...?data?.map((key, value) => MapEntry(key, value.toString())),
        },
      );

      if (success) {
        _logger.info('✅ Notification sent successfully to user: $userId');
      } else {
        _logger.error('❌ Failed to send notification to user: $userId');
      }

      return success;
    } catch (e) {
      _logger.error('❌ Error sending custom notification: $e');
      return false;
    }
  }

  // Send appointment cancelled notification
  Future<bool> notifyAppointmentCancelled({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
  }) async {
    return await sendCustomNotification(
      userId: patientUserId,
      title: 'تم إلغاء الحجز',
      body: 'تم إلغاء حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}',
      type: 'appointment_cancelled',
      data: {
        'appointment_id': appointmentId,
        'action': 'cancelled',
        'patient_name': patientName,
        'doctor_name': doctorName,
        'appointment_date': appointmentDate.toIso8601String(),
      },
    );
  }

  // Send appointment confirmed notification
  Future<bool> notifyAppointmentConfirmed({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
  }) async {
    return await sendCustomNotification(
      userId: patientUserId,
      title: 'تم تأكيد الحجز',
      body: 'تم تأكيد حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}',
      type: 'appointment_confirmed',
      data: {
        'appointment_id': appointmentId,
        'action': 'confirmed',
        'patient_name': patientName,
        'doctor_name': doctorName,
        'appointment_date': appointmentDate.toIso8601String(),
      },
    );
  }

  // Send appointment rescheduled notification
  Future<bool> notifyAppointmentRescheduled({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime oldDate,
    required DateTime newDate,
  }) async {
    return await sendCustomNotification(
      userId: patientUserId,
      title: 'تم تغيير موعد الحجز',
      body: 'تم تغيير موعد حجزك مع د. $doctorName من ${_formatDate(oldDate)} إلى ${_formatDate(newDate)}',
      type: 'appointment_rescheduled',
      data: {
        'appointment_id': appointmentId,
        'action': 'rescheduled',
        'patient_name': patientName,
        'doctor_name': doctorName,
        'old_date': oldDate.toIso8601String(),
        'new_date': newDate.toIso8601String(),
      },
    );
  }

  // Send FCM notification via HTTP API
  Future<bool> _sendFCMNotification({
    required String fcmToken,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      // Note: In production, you should use Firebase Admin SDK on your server
      // This is a simplified implementation for demonstration
      
      _logger.info('📤 Sending FCM notification');
      _logger.info('🎯 Token: ${fcmToken.substring(0, 20)}...');
      _logger.info('📝 Title: $title');
      _logger.info('📝 Body: $body');
      
      // For now, we'll simulate a successful send
      // In production, implement proper FCM HTTP API call here
      
      return true;
    } catch (e) {
      _logger.error('❌ Failed to send FCM notification: $e');
      return false;
    }
  }

  // Broadcast notification to all users
  Future<bool> broadcastNotification({
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      _logger.info('📢 Broadcasting notification to all users');

      // Get all active FCM tokens
      final tokensResponse = await SupabaseConfig.client
          .from('user_fcm_tokens')
          .select('user_id, fcm_token')
          .eq('is_active', true);

      if (tokensResponse.isEmpty) {
        _logger.warning('⚠️ No active FCM tokens found');
        return false;
      }

      int successCount = 0;
      int totalCount = tokensResponse.length;

      for (final tokenData in tokensResponse) {
        final userId = tokenData['user_id'] as String;
        final fcmToken = tokenData['fcm_token'] as String;

        final success = await _sendFCMNotification(
          fcmToken: fcmToken,
          title: title,
          body: body,
          data: {
            'type': type,
            'user_id': userId,
            'timestamp': DateTime.now().toIso8601String(),
            ...?data?.map((key, value) => MapEntry(key, value.toString())),
          },
        );

        if (success) {
          successCount++;
          // Log notification in database
          await _logNotification(
            userId: userId,
            type: type,
            title: title,
            message: body,
          );
        }
      }

      _logger.info('✅ Broadcast completed: $successCount/$totalCount notifications sent');
      return successCount > 0;
    } catch (e) {
      _logger.error('❌ Error broadcasting notification: $e');
      return false;
    }
  }

  // Notify all specialists
  Future<bool> notifySpecialists({
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      _logger.info('👨‍⚕️ Notifying all specialists');

      // Get specialist users
      final specialistsResponse = await SupabaseConfig.client
          .from('admins')
          .select('id')
          .eq('role', 'specialist');

      if (specialistsResponse.isEmpty) {
        _logger.warning('⚠️ No specialists found');
        return false;
      }

      final specialistIds = specialistsResponse.map((s) => s['id'] as String).toList();

      // Get FCM tokens for specialists
      final tokensResponse = await SupabaseConfig.client
          .from('user_fcm_tokens')
          .select('user_id, fcm_token')
          .inFilter('user_id', specialistIds)
          .eq('is_active', true);

      if (tokensResponse.isEmpty) {
        _logger.warning('⚠️ No active FCM tokens found for specialists');
        return false;
      }

      int successCount = 0;
      int totalCount = tokensResponse.length;

      for (final tokenData in tokensResponse) {
        final userId = tokenData['user_id'] as String;
        final fcmToken = tokenData['fcm_token'] as String;

        final success = await _sendFCMNotification(
          fcmToken: fcmToken,
          title: title,
          body: body,
          data: {
            'type': type,
            'user_id': userId,
            'timestamp': DateTime.now().toIso8601String(),
            ...?data?.map((key, value) => MapEntry(key, value.toString())),
          },
        );

        if (success) {
          successCount++;
          // Log notification in database
          await _logNotification(
            userId: userId,
            type: type,
            title: title,
            message: body,
          );
        }
      }

      _logger.info('✅ Specialists notification completed: $successCount/$totalCount notifications sent');
      return successCount > 0;
    } catch (e) {
      _logger.error('❌ Error notifying specialists: $e');
      return false;
    }
  }

  // Notify all receptionists
  Future<bool> notifyReceptionists({
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      _logger.info('📞 Notifying all receptionists');

      // Get receptionist users
      final receptionistsResponse = await SupabaseConfig.client
          .from('admins')
          .select('id')
          .eq('role', 'receptionist');

      if (receptionistsResponse.isEmpty) {
        _logger.warning('⚠️ No receptionists found');
        return false;
      }

      final receptionistIds = receptionistsResponse.map((r) => r['id'] as String).toList();

      // Get FCM tokens for receptionists
      final tokensResponse = await SupabaseConfig.client
          .from('user_fcm_tokens')
          .select('user_id, fcm_token')
          .inFilter('user_id', receptionistIds)
          .eq('is_active', true);

      if (tokensResponse.isEmpty) {
        _logger.warning('⚠️ No active FCM tokens found for receptionists');
        return false;
      }

      int successCount = 0;
      int totalCount = tokensResponse.length;

      for (final tokenData in tokensResponse) {
        final userId = tokenData['user_id'] as String;
        final fcmToken = tokenData['fcm_token'] as String;

        final success = await _sendFCMNotification(
          fcmToken: fcmToken,
          title: title,
          body: body,
          data: {
            'type': type,
            'user_id': userId,
            'timestamp': DateTime.now().toIso8601String(),
            ...?data?.map((key, value) => MapEntry(key, value.toString())),
          },
        );

        if (success) {
          successCount++;
          // Log notification in database
          await _logNotification(
            userId: userId,
            type: type,
            title: title,
            message: body,
          );
        }
      }

      _logger.info('✅ Receptionists notification completed: $successCount/$totalCount notifications sent');
      return successCount > 0;
    } catch (e) {
      _logger.error('❌ Error notifying receptionists: $e');
      return false;
    }
  }

  // Log notification in database
  Future<void> _logNotification({
    required String userId,
    required String type,
    required String title,
    required String message,
    String? relatedId,
  }) async {
    try {
      await SupabaseConfig.client.from('notifications_log').insert({
        'user_id': userId,
        'type': type,
        'title': title,
        'message': message,
        'related_id': relatedId,
        'sent_at': DateTime.now().toIso8601String(),
        'is_read': false,
      });
    } catch (e) {
      _logger.error('❌ Failed to log notification: $e');
    }
  }

  // Send task completed notification
  Future<bool> notifyTaskCompleted({
    required String taskId,
    required String taskTitle,
    required String assignerUserId,
    required String completedByName,
  }) async {
    return await sendCustomNotification(
      userId: assignerUserId,
      title: 'تم إنجاز المهمة',
      body: 'تم إنجاز المهمة "$taskTitle" بواسطة $completedByName',
      type: 'task_completed',
      data: {
        'task_id': taskId,
        'action': 'completed',
        'task_title': taskTitle,
        'completed_by': completedByName,
      },
    );
  }

  // Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}