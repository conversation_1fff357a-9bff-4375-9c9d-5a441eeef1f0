import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;

class ImageCompression {
  static Future<Uint8List> compressImage(File imageFile, {int quality = 85, int maxWidth = 1024}) async {
    try {
      // Read the image file
      final bytes = await imageFile.readAsBytes();
      
      // Decode the image
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Resize if needed
      img.Image resizedImage = image;
      if (image.width > maxWidth) {
        final ratio = maxWidth / image.width;
        final newHeight = (image.height * ratio).round();
        resizedImage = img.copyResize(image, width: maxWidth, height: newHeight);
      }

      // Compress and encode as JPEG
      final compressedBytes = img.encodeJpg(resizedImage, quality: quality);
      
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      // If compression fails, return original file bytes
      return await imageFile.readAsBytes();
    }
  }

  static Future<Uint8List> compressPdf(File pdfFile) async {
    try {
      // For PDF files, we just return the original bytes
      // In a real app, you might want to use a PDF compression library
      return await pdfFile.readAsBytes();
    } catch (e) {
      throw Exception('Failed to process PDF file: $e');
    }
  }

  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  static bool isPdfFile(String fileName) {
    final extension = getFileExtension(fileName);
    return extension == 'pdf';
  }

  static String generateFileName(String prefix, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_$timestamp.$extension';
  }
}
