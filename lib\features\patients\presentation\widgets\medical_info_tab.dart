import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/patient_model.dart';

class MedicalInfoTab extends StatelessWidget {
  final PatientModel patient;

  const MedicalInfoTab({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Medical Conditions Card
          _buildInfoCard(
            title: AppStrings.medicalConditions,
            content: patient.medicalConditions ?? 'لا توجد حالات طبية مسجلة',
            icon: Icons.medical_information,
            onEdit: () => _showEditDialog(
              context,
              AppStrings.medicalConditions,
              patient.medicalConditions ?? '',
            ),
          ),
          SizedBox(height: 16.h),

          // Medications Card
          _buildInfoCard(
            title: AppStrings.medications,
            content: patient.medications ?? 'لا توجد أدوية مسجلة',
            icon: Icons.medication,
            onEdit: () => _showEditDialog(
              context,
              AppStrings.medications,
              patient.medications ?? '',
            ),
          ),
          SizedBox(height: 16.h),

          // Supplements Card
          _buildInfoCard(
            title: AppStrings.supplements,
            content: patient.supplements ?? 'لا توجد مكملات غذائية مسجلة',
            icon: Icons.local_pharmacy,
            onEdit: () => _showEditDialog(
              context,
              AppStrings.supplements,
              patient.supplements ?? '',
            ),
          ),
          SizedBox(height: 16.h),

          // Allergies Card
          _buildInfoCard(
            title: AppStrings.allergies,
            content: patient.allergies ?? 'لا توجد حساسية مسجلة',
            icon: Icons.warning,
            onEdit: () => _showEditDialog(
              context,
              AppStrings.allergies,
              patient.allergies ?? '',
            ),
          ),
          SizedBox(height: 16.h),

          // Physical Activity Card
          _buildInfoCard(
            title: AppStrings.physicalActivity,
            content: patient.physicalActivity ?? 'لا يوجد نشاط بدني مسجل',
            icon: Icons.fitness_center,
            onEdit: () => _showEditDialog(
              context,
              AppStrings.physicalActivity,
              patient.physicalActivity ?? '',
            ),
          ),
          SizedBox(height: 16.h),

          // Notes Card
          _buildInfoCard(
            title: AppStrings.notes,
            content: patient.notes ?? 'لا توجد ملاحظات',
            icon: Icons.note,
            onEdit: () => _showEditDialog(
              context,
              AppStrings.notes,
              patient.notes ?? '',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required String content,
    required IconData icon,
    required VoidCallback onEdit,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.primary,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              IconButton(
                onPressed: onEdit,
                icon: Icon(
                  Icons.edit,
                  color: AppColors.textSecondary,
                  size: 20.w,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),

          // Content
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: AppColors.gray50,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: AppColors.border),
            ),
            child: Text(
              content,
              style: TextStyle(
                fontSize: 14.sp,
                color: content.contains('لا توجد') || content.contains('لا يوجد')
                    ? AppColors.textHint
                    : AppColors.textPrimary,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showEditDialog(BuildContext context, String title, String currentValue) {
    final controller = TextEditingController(text: currentValue);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('تعديل $title'),
          content: SizedBox(
            width: double.maxFinite,
            child: TextField(
              controller: controller,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'أدخل $title هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(AppStrings.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                // Save the updated information
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تحديث $title بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              },
              child: const Text(AppStrings.save),
            ),
          ],
        );
      },
    );
  }
}
