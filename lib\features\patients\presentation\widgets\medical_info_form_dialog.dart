import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/medical_info_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/medical_info_bloc.dart';
import '../bloc/medical_info_event.dart';

class MedicalInfoFormDialog extends StatefulWidget {
  final String patientId;
  final String infoType;
  final MedicalInfoModel? medicalInfo;

  const MedicalInfoFormDialog({
    super.key,
    required this.patientId,
    required this.infoType,
    this.medicalInfo,
  });

  @override
  State<MedicalInfoFormDialog> createState() => _MedicalInfoFormDialogState();
}

class _MedicalInfoFormDialogState extends State<MedicalInfoFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _dosageController;
  late TextEditingController _frequencyController;
  late TextEditingController _notesController;

  DateTime? _startDate;
  DateTime? _endDate;
  String? _severity;
  bool _isActive = true;

  final List<String> _severityOptions = ['mild', 'moderate', 'severe'];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(
      text: widget.medicalInfo?.name ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.medicalInfo?.description ?? '',
    );
    _dosageController = TextEditingController(
      text: widget.medicalInfo?.dosage ?? '',
    );
    _frequencyController = TextEditingController(
      text: widget.medicalInfo?.frequency ?? '',
    );
    _notesController = TextEditingController(
      text: widget.medicalInfo?.notes ?? '',
    );

    if (widget.medicalInfo != null) {
      _startDate = widget.medicalInfo!.startDate;
      _endDate = widget.medicalInfo!.endDate;
      _severity = widget.medicalInfo!.severity;
      _isActive = widget.medicalInfo!.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _dosageController.dispose();
    _frequencyController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _saveMedicalInfo() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show loading dialog immediately
    final isUpdate = widget.medicalInfo != null;
    LoadingDialog.show(
      context,
      isUpdate ? 'جاري تحديث المعلومة الطبية...' : 'جاري إضافة المعلومة الطبية...'
    );

    final medicalInfo = MedicalInfoModel(
      id: widget.medicalInfo?.id ?? '',
      patientId: widget.patientId,
      infoType: widget.infoType,
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isNotEmpty
          ? _descriptionController.text.trim()
          : null,
      dosage: _dosageController.text.trim().isNotEmpty
          ? _dosageController.text.trim()
          : null,
      frequency: _frequencyController.text.trim().isNotEmpty
          ? _frequencyController.text.trim()
          : null,
      startDate: _startDate,
      endDate: _endDate,
      severity: _severity,
      notes: _notesController.text.trim().isNotEmpty
          ? _notesController.text.trim()
          : null,
      isActive: _isActive,
      createdAt: widget.medicalInfo?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (widget.medicalInfo == null) {
      // Add new medical info
      context.read<MedicalInfoBloc>().add(AddMedicalInfo(medicalInfo: medicalInfo));
    } else {
      // Update existing medical info
      context.read<MedicalInfoBloc>().add(UpdateMedicalInfo(medicalInfo: medicalInfo));
    }

    Navigator.of(context).pop();
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020), // تاريخ بداية مفتوح
      lastDate: DateTime(2030), // تاريخ نهاية مفتوح
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: _startDate ?? DateTime(2020), // تاريخ بداية مفتوح
      lastDate: DateTime(2030), // تاريخ نهاية مفتوح
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  String _getInfoTypeTitle() {
    switch (widget.infoType) {
      case 'medication':
        return 'دواء';
      case 'supplement':
        return 'مكمل غذائي';
      case 'condition':
        return 'حالة صحية';
      case 'allergy':
        return 'حساسية';
      case 'activity':
        return 'نشاط بدني';
      default:
        return 'معلومة طبية';
    }
  }

  bool _shouldShowDosageFields() {
    return widget.infoType == 'medication' || widget.infoType == 'supplement';
  }

  bool _shouldShowSeverityField() {
    return widget.infoType == 'allergy' || widget.infoType == 'condition';
  }

  bool _shouldShowDateFields() {
    return widget.infoType == 'medication' ||
           widget.infoType == 'supplement' ||
           widget.infoType == 'condition';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header
              Container(
                padding: EdgeInsets.all(24.w),
                child: Text(
                  widget.medicalInfo == null
                      ? 'إضافة ${_getInfoTypeTitle()}'
                      : 'تعديل ${_getInfoTypeTitle()}',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Scrollable content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: 'الاسم *',
                            hintText: 'أدخل اسم ${_getInfoTypeTitle()}',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'يرجى إدخال الاسم';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Description
                      SizedBox(
                        height: 120.h,
                        child: TextFormField(
                          controller: _descriptionController,
                          decoration: InputDecoration(
                            labelText: 'الوصف',
                            hintText: 'أدخل وصف ${_getInfoTypeTitle()}',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Dosage and Frequency (for medications and supplements)
                      if (_shouldShowDosageFields()) ...[
                        Row(
                          children: [
                            Expanded(
                              child: SizedBox(
                                height: 80.h,
                                child: TextFormField(
                                  controller: _dosageController,
                                  decoration: InputDecoration(
                                    labelText: 'الجرعة',
                                    hintText: 'مثل: 500mg',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                    ),
                                    alignLabelWithHint: true,
                                  ),
                                  maxLines: null,
                                  expands: true,
                                  textAlignVertical: TextAlignVertical.top,
                                ),
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: SizedBox(
                                height: 80.h,
                                child: TextFormField(
                                  controller: _frequencyController,
                                  decoration: InputDecoration(
                                    labelText: 'التكرار',
                                    hintText: 'مثل: مرة يومياً',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                    ),
                                    alignLabelWithHint: true,
                                  ),
                                  maxLines: null,
                                  expands: true,
                                  textAlignVertical: TextAlignVertical.top,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                      ],

                      // Severity (for allergies and conditions)
                      if (_shouldShowSeverityField()) ...[
                        DropdownButtonFormField<String>(
                          value: _severity,
                          decoration: InputDecoration(
                            labelText: 'الشدة',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                          items: _severityOptions.map((severity) {
                            return DropdownMenuItem<String>(
                              value: severity,
                              child: Text(_getSeverityLabel(severity)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _severity = value;
                            });
                          },
                        ),
                        SizedBox(height: 16.h),
                      ],

                      // Date fields (for medications, supplements, and conditions)
                      if (_shouldShowDateFields()) ...[
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: _selectStartDate,
                                child: Container(
                                  padding: EdgeInsets.all(16.w),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.gray300),
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.calendar_today, color: AppColors.primary, size: 16.w),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'تاريخ البداية',
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: AppColors.textSecondary,
                                              ),
                                            ),
                                            Text(
                                              _startDate != null
                                                  ? DateFormat('dd/MM/yyyy').format(_startDate!)
                                                  : 'اختر التاريخ',
                                              style: TextStyle(
                                                fontSize: 14.sp,
                                                color: AppColors.textPrimary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: InkWell(
                                onTap: _selectEndDate,
                                child: Container(
                                  padding: EdgeInsets.all(16.w),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.gray300),
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.calendar_today, color: AppColors.primary, size: 16.w),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'تاريخ النهاية',
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: AppColors.textSecondary,
                                              ),
                                            ),
                                            Text(
                                              _endDate != null
                                                  ? DateFormat('dd/MM/yyyy').format(_endDate!)
                                                  : 'اختياري',
                                              style: TextStyle(
                                                fontSize: 14.sp,
                                                color: AppColors.textPrimary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                      ],

                      // Notes
                      SizedBox(
                        height: 120.h,
                        child: TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'ملاحظات (اختياري)',
                            hintText: 'أدخل أي ملاحظات إضافية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Active status
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.gray300),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _isActive ? Icons.check_circle : Icons.cancel,
                              color: _isActive ? AppColors.success : AppColors.error,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Text(
                                'الحالة',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ),
                            Switch(
                              value: _isActive,
                              onChanged: (value) {
                                setState(() {
                                  _isActive = value;
                                });
                              },
                              activeColor: AppColors.success,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),

              // Fixed footer with action buttons
              Container(
                padding: EdgeInsets.all(24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveMedicalInfo,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          widget.medicalInfo == null ? 'إضافة' : 'تحديث',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getSeverityLabel(String severity) {
    switch (severity) {
      case 'mild':
        return 'خفيف';
      case 'moderate':
        return 'متوسط';
      case 'severe':
        return 'شديد';
      default:
        return severity;
    }
  }
}
