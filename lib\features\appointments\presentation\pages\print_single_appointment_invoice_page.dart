import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:typed_data';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/utils/time_utils.dart';
import '../../../employees/data/repositories/employees_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';

class PrintSingleAppointmentInvoicePage extends StatefulWidget {
  final AppointmentModel appointment;
  final PatientModel patient;
  final List<AppointmentModel>? groupAppointments; // للحصول على عدد الجلسات الإجمالي
  final VoidCallback? onPrintComplete;

  const PrintSingleAppointmentInvoicePage({
    super.key,
    required this.appointment,
    required this.patient,
    this.groupAppointments,
    this.onPrintComplete,
  });

  @override
  State<PrintSingleAppointmentInvoicePage> createState() => _PrintSingleAppointmentInvoicePageState();
}

class _PrintSingleAppointmentInvoicePageState extends State<PrintSingleAppointmentInvoicePage> {
  ReceiptController? controller;
  PaperSize _paperSize = PaperSize.mm80;
  EmployeeModel? _employee;
  TimeSlotModel? _timeSlot;
  bool _isLoadingData = false;

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
    _loadAppointmentData();
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
    setState(() {
      _paperSize = PaperSize.values[paperSizeIndex];
    });
  }

  Future<void> _loadAppointmentData() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      // Load employee data
      if (widget.appointment.employeeId != null) {
        final employees = await EmployeesRepository.getAllEmployees();
        _employee = employees.firstWhere(
          (emp) => emp.id == widget.appointment.employeeId,
          orElse: () => throw Exception('Employee not found'),
        );
      }

      // Load time slot data
      if (widget.appointment.timeSlotId != null) {
        final timeSlotsRepo = TimeSlotsRepository();
        final timeSlots = await timeSlotsRepo.getAllTimeSlots();
        _timeSlot = timeSlots.firstWhere(
          (slot) => slot.id == widget.appointment.timeSlotId,
          orElse: () => throw Exception('Time slot not found'),
        );
      }
    } catch (e) {
      debugPrint('Error loading appointment data: $e');
    } finally {
      setState(() {
        _isLoadingData = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('طباعة فاتورة الجلسة'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.print),
            onPressed: _isLoadingData ? null : _printInvoice,
          ),
        ],
      ),
      body: _isLoadingData
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.gray300.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Receipt(
                      builder: (context) => _buildInvoiceContent(),
                      onInitialized: (controller) {
                        controller.paperSize = _paperSize;
                        this.controller = controller;
                      },
                    ),
                  ),

                  SizedBox(height: 24.h),

                  // Print Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _printInvoice,
                      icon: Icon(Icons.print, color: Colors.white),
                      label: Text(
                        'طباعة فاتورة الجلسة',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildInvoiceContent() {
    final sessionNumber = widget.appointment.bookingSequence ?? 1;
    final totalSessions = widget.groupAppointments?.length ?? 1;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header
        Text(
          'مركز مستشفى إربد الإسلامي',
          style: TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'للسمع والنطق والسلوك',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),
        Text(
          'العنوان: إربد - الأردن',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.black),
          textAlign: TextAlign.center,
        ),

        Divider(thickness: 2),

        // Invoice Type
        Text(
          'فاتورة جلسة علاجية',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: 16),
        
        Divider(),
        
        // Patient Information
        Text(
          'بيانات المريض',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28, color: Colors.black),
        ),
        SizedBox(height: 16),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الاسم:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold, color: Colors.black)),
            Expanded(
              child: Text(
                widget.patient.name,
                style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold, color: Colors.black),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الهاتف:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold, color: Colors.black)),
            Text(widget.patient.phone ?? 'غير محدد', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold, color: Colors.black)),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('رقم المريض:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
            Text(widget.patient.patientId ?? 'غير محدد', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('نوع العلاج:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
            Expanded(
              child: Text(
                widget.patient.treatmentTypes.isNotEmpty
                    ? widget.patient.treatmentTypes.map((t) => t.displayName).join(', ')
                    : 'غير محدد',
                style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        
        Divider(),
        
        // Session Details
        Text(
          'تفاصيل الجلسة',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),
        
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('رقم الجلسة:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                  Text('$sessionNumber من $totalSessions', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                ],
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('التاريخ:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                  Text('${widget.appointment.appointmentDate.day}/${widget.appointment.appointmentDate.month}/${widget.appointment.appointmentDate.year}', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                ],
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('اليوم:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                  Text(TimeUtils.getArabicDayName(widget.appointment.appointmentDate.weekday), style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                ],
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('وقت الجلسة:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                  Text(
                    _timeSlot != null
                        ? TimeUtils.formatTimeRange(_timeSlot!.startTime, _timeSlot!.endTime)
                        : 'غير محدد',
                    style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('الأخصائي:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                  Expanded(
                    child: Text(
                      _employee != null ? 'د. ${_employee!.name}' : 'غير محدد',
                      style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              if (_employee?.specialization != null) ...[
                SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('التخصص:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                    Expanded(
                      child: Text(
                        _employee!.specialization!.name,
                        style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                        textAlign: TextAlign.end,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        
        SizedBox(height: 24),
        
        Divider(),
        
        // Payment Details
        Text(
          'تفاصيل الدفع',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),

        // Show aggregated payment details for multiple bookings
        if (widget.appointment.isMultipleBooking && widget.appointment.multipleBookingGroupId != null) ...[
          FutureBuilder<Map<String, double>>(
            future: _getAggregatedPaymentDetails(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Container(
                  padding: EdgeInsets.all(12),
                  child: Text(
                    'جاري تحميل تفاصيل الدفع...',
                    style: TextStyle(fontSize: 20, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                );
              }

              if (snapshot.hasError || !snapshot.hasData) {
                return Container(
                  padding: EdgeInsets.all(12),
                  child: Text(
                    'خطأ في تحميل تفاصيل الدفع',
                    style: TextStyle(fontSize: 20, color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                );
              }

              final paymentDetails = snapshot.data!;
              return Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Text(
                          'إجمالي الرسوم',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '${paymentDetails['totalFee']!.toStringAsFixed(2)} د.ا',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        Text(
                          'إجمالي مدفوع',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '${paymentDetails['totalPaid']!.toStringAsFixed(2)} د.ا',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700],
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        Text(
                          'إجمالي متبقي',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '${paymentDetails['totalRemaining']!.toStringAsFixed(2)} د.ا',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ] else ...[
          // Show individual appointment payment details
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      'الرسوم',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '${widget.appointment.consultationFee.toStringAsFixed(2)} د.ا',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      'مدفوع',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '${widget.appointment.paidAmount.toStringAsFixed(2)} د.ا',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      'متبقي',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '${widget.appointment.remainingAmount.toStringAsFixed(2)} د.ا',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.red[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],

        SizedBox(height: 32),

        // Footer
        Text(
          "شكرا لزيارتكم",
          style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),

        // مساحة إضافية في نهاية الفاتورة
        SizedBox(height: 40),
      ],
    );
  }

  Future<void> _printInvoice() async {
    // Get printer address from settings
    final prefs = await SharedPreferences.getInstance();
    final printerAddress = prefs.getString('printer_address');

    if (printerAddress == null) {
      _showError('لم يتم تكوين الطابعة. يرجى الذهاب إلى إعدادات الطابعة أولاً');
      return;
    }

    if (controller == null) {
      _showError('خطأ في إعداد الطابعة');
      return;
    }

    // عرض ديالوج جاري الطباعة
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              SizedBox(height: 16.h),
              Text(
                'جاري الطباعة...',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    try {
      // Print the receipt
      await controller!.print(
        address: printerAddress,
        keepConnected: true,
        addFeeds: 4, // إضافة مساحة إضافية في نهاية الطباعة
      );

      // إرسال أمر قطع الورق (GS V 65 0) لقطع الورق كامل
      await FlutterBluetoothPrinter.printBytes(
        address: printerAddress,
        data: Uint8List.fromList([29, 86, 65, 0]), // أمر قطع الورق (GS V 65 0)
        keepConnected: true,
      );

      // إغلاق ديالوج الطباعة
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showSuccess('تم طباعة فاتورة الجلسة بنجاح');

    } catch (e) {
      // إغلاق ديالوج الطباعة في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
      }
      _showError('فشل في طباعة الفاتورة: $e');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  Future<Map<String, double>> _getAggregatedPaymentDetails() async {
    try {
      final response = await Supabase.instance.client
          .from('appointments')
          .select('consultation_fee, paid_amount, remaining_amount')
          .eq('multiple_booking_group_id', widget.appointment.multipleBookingGroupId!);

      double totalFee = 0.0;
      double totalPaid = 0.0;
      double totalRemaining = 0.0;

      for (final appointment in response) {
        totalFee += (appointment['consultation_fee'] as num?)?.toDouble() ?? 0.0;
        totalPaid += (appointment['paid_amount'] as num?)?.toDouble() ?? 0.0;
        totalRemaining += (appointment['remaining_amount'] as num?)?.toDouble() ?? 0.0;
      }

      return {
        'totalFee': totalFee,
        'totalPaid': totalPaid,
        'totalRemaining': totalRemaining,
      };
    } catch (e) {
      debugPrint('❌ Error getting aggregated payment details: $e');
      return {
        'totalFee': widget.appointment.consultationFee,
        'totalPaid': widget.appointment.paidAmount,
        'totalRemaining': widget.appointment.remainingAmount,
      };
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
          duration: Duration(seconds: 2),
        ),
      );

      // Call the completion callback if provided
      if (widget.onPrintComplete != null) {
        widget.onPrintComplete!();
      }

      // Navigate back after a short delay
      Future.delayed(Duration(seconds: 1), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }
}
