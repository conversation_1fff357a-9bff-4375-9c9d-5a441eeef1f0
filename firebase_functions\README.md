# Diet Rx Cloud Functions

## إعداد Firebase Cloud Functions للإشعارات المجدولة

### 1. تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. تسجيل الدخول
```bash
firebase login
```

### 3. تهيئة المشروع
```bash
cd firebase_functions
firebase init functions
```

### 4. تثبيت Dependencies
```bash
cd functions
npm install
```

### 5. تحديث المتغيرات
في `functions/index.js`:
- استبدل `YOUR_SUPABASE_SERVICE_KEY` بالمفتاح الحقيقي
- تأكد من `supabaseUrl` صحيح

### 6. نشر Functions
```bash
firebase deploy --only functions
```

### 7. اختبار Function
```bash
# اختبار محلي
firebase emulators:start --only functions

# اختبار على الخادم
curl "https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/testNotification?patientId=**********"
```

## كيف يعمل النظام:

### ✅ **الآن النظام يعمل 24/7:**
1. **Firebase Cloud Function** تعمل كل دقيقة
2. **تتحقق من Supabase** للإشعارات المستحقة
3. **ترسل الإشعارات** عبر Firebase FCM
4. **تسجل النتائج** في notification_logs

### ✅ **مميزات الحل الجديد:**
- 🌐 **يعمل بدون التطبيق**
- ⏰ **دقيق في التوقيت**
- 📊 **يسجل جميع العمليات**
- 🔄 **يعيد المحاولة عند الفشل**
- 💰 **مجاني حتى 2 مليون استدعاء شهرياً**

### 📊 **مراقبة النظام:**
- Firebase Console → Functions → Logs
- Supabase → notification_logs table
- Firebase Console → Cloud Messaging → Analytics

## الاستخدام:

### إنشاء تذكير جديد:
التطبيق ينشئ التذكير في `scheduled_notifications` والـ Cloud Function تتولى الباقي تلقائياً.

### مراقبة الإشعارات:
```sql
-- عرض الإشعارات المرسلة اليوم
SELECT * FROM notification_logs 
WHERE DATE(created_at) = CURRENT_DATE 
ORDER BY created_at DESC;

-- عرض الإشعارات المجدولة النشطة
SELECT * FROM scheduled_notifications 
WHERE is_active = true 
ORDER BY scheduled_time;
```
