import 'package:equatable/equatable.dart';
import '../../../../core/models/product_model.dart';

abstract class ProductsState extends Equatable {
  const ProductsState();

  @override
  List<Object?> get props => [];
}

class ProductsInitial extends ProductsState {}

class ProductsLoading extends ProductsState {}

class ProductsLoaded extends ProductsState {
  final List<ProductModel> products;
  final List<ProductModel> filteredProducts;
  final List<String> categories;
  final String selectedCategory;
  final bool isSearching;
  final String searchQuery;

  const ProductsLoaded({
    required this.products,
    required this.filteredProducts,
    required this.categories,
    this.selectedCategory = 'الكل',
    this.isSearching = false,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [
        products,
        filteredProducts,
        categories,
        selectedCategory,
        isSearching,
        searchQuery,
      ];

  ProductsLoaded copyWith({
    List<ProductModel>? products,
    List<ProductModel>? filteredProducts,
    List<String>? categories,
    String? selectedCategory,
    bool? isSearching,
    String? searchQuery,
  }) {
    return ProductsLoaded(
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      categories: categories ?? this.categories,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class ProductsError extends ProductsState {
  final String message;

  const ProductsError({required this.message});

  @override
  List<Object?> get props => [message];
}

class ProductCreated extends ProductsState {
  final ProductModel product;

  const ProductCreated({required this.product});

  @override
  List<Object?> get props => [product];
}

class ProductUpdated extends ProductsState {
  final ProductModel product;

  const ProductUpdated({required this.product});

  @override
  List<Object?> get props => [product];
}

class ProductDeleted extends ProductsState {
  final String productId;

  const ProductDeleted({required this.productId});

  @override
  List<Object?> get props => [productId];
}

class ProductStockUpdated extends ProductsState {
  final ProductModel product;

  const ProductStockUpdated({required this.product});

  @override
  List<Object?> get props => [product];
}
