import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/models/product_model.dart';

abstract class ProductsEvent extends Equatable {
  const ProductsEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllProducts extends ProductsEvent {}



class SearchProducts extends ProductsEvent {
  final String query;

  const SearchProducts({required this.query});

  @override
  List<Object?> get props => [query];
}

class CreateProduct extends ProductsEvent {
  final ProductModel product;

  const CreateProduct({required this.product});

  @override
  List<Object?> get props => [product];
}

class UpdateProduct extends ProductsEvent {
  final ProductModel product;

  const UpdateProduct({required this.product});

  @override
  List<Object?> get props => [product];
}

class DeleteProduct extends ProductsEvent {
  final String productId;

  const DeleteProduct({required this.productId});

  @override
  List<Object?> get props => [productId];
}

class CreateProductWithImages extends ProductsEvent {
  final ProductModel product;
  final List<XFile> images;

  const CreateProductWithImages({
    required this.product,
    required this.images,
  });

  @override
  List<Object?> get props => [product, images];
}

class UploadProductImages extends ProductsEvent {
  final String productId;
  final List<XFile> images;

  const UploadProductImages({
    required this.productId,
    required this.images,
  });

  @override
  List<Object?> get props => [productId, images];
}

class UpdateProductStock extends ProductsEvent {
  final String productId;
  final int newStock;

  const UpdateProductStock({
    required this.productId,
    required this.newStock,
  });

  @override
  List<Object?> get props => [productId, newStock];
}

class RefreshProducts extends ProductsEvent {}

class FilterProductsByPrice extends ProductsEvent {
  final double minPrice;
  final double maxPrice;

  const FilterProductsByPrice({
    required this.minPrice,
    required this.maxPrice,
  });

  @override
  List<Object?> get props => [minPrice, maxPrice];
}

class DeleteProductImage extends ProductsEvent {
  final String productId;
  final String imageId;

  const DeleteProductImage({
    required this.productId,
    required this.imageId,
  });

  @override
  List<Object?> get props => [productId, imageId];
}
