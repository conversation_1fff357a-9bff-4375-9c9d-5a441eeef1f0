import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/admin_model.dart';

import '../../../employees/data/repositories/employees_repository.dart';
import '../bloc/time_slots_bloc.dart';
import '../bloc/time_slots_event.dart';
import '../bloc/time_slots_state.dart';

class TimeSlotFormPage extends StatefulWidget {
  final TimeSlotModel? timeSlot;
  final bool isEditing;
  final int? preSelectedDay;
  final EmployeeModel? preSelectedEmployee;

  const TimeSlotFormPage({
    super.key,
    this.timeSlot,
    this.isEditing = false,
    this.preSelectedDay,
    this.preSelectedEmployee,
  });

  @override
  State<TimeSlotFormPage> createState() => _TimeSlotFormPageState();
}

class _TimeSlotFormPageState extends State<TimeSlotFormPage> {
  final _formKey = GlobalKey<FormState>();

  int _selectedDayOfWeek = 0;
  TimeOfDay _startTime = const TimeOfDay(hour: 9, minute: 0);
  TimeOfDay _endTime = const TimeOfDay(hour: 17, minute: 0);
  int _durationMinutes = 30;
  bool _isActive = true;
  int _maxPatients = 1;
  String? _selectedEmployeeId;
  List<EmployeeModel> _employees = [];
  bool _isLoadingEmployees = false;

  final List<String> _dayNames = [
    'الأحد',    // 0
    'الإثنين',  // 1
    'الثلاثاء', // 2
    'الأربعاء', // 3
    'الخميس',   // 4
    'الجمعة',   // 5
    'السبت',    // 6
  ];

  @override
  void initState() {
    super.initState();

    // If we have a timeSlot (either for editing or duplicating)
    if (widget.timeSlot != null) {
      _selectedDayOfWeek = widget.timeSlot!.dayOfWeek;
      _startTime = _parseTimeString(widget.timeSlot!.startTime);
      _endTime = _parseTimeString(widget.timeSlot!.endTime);
      _durationMinutes = widget.timeSlot!.durationMinutes;
      _isActive = widget.timeSlot!.isActive;
      _maxPatients = widget.timeSlot!.maxPatients;
      _selectedEmployeeId = widget.timeSlot!.employeeId;
    }

    // Override with pre-selected values if provided
    if (widget.preSelectedDay != null) {
      _selectedDayOfWeek = widget.preSelectedDay!;
    }
    if (widget.preSelectedEmployee != null) {
      _selectedEmployeeId = widget.preSelectedEmployee!.id;
    }

    _loadEmployees();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoadingEmployees = true;
    });

    try {
      final employees = await EmployeesRepository.getAllEmployees();
      // Filter only specialists
      final specialists = employees.where((emp) => emp.employeeType == 'specialist').toList();

      setState(() {
        _employees = specialists;
        _isLoadingEmployees = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingEmployees = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الأخصائيين: $e')),
        );
      }
    }
  }

  TimeOfDay _parseTimeString(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _formatTimeOfDayWithAmPm(TimeOfDay time) {
    if (time.hour == 0) {
      return '12:${time.minute.toString().padLeft(2, '0')} ص';
    } else if (time.hour < 12) {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')} ص';
    } else if (time.hour == 12) {
      return '12:${time.minute.toString().padLeft(2, '0')} م';
    } else {
      return '${time.hour - 12}:${time.minute.toString().padLeft(2, '0')} م';
    }
  }

  String _getPageTitle() {
    if (widget.isEditing) {
      return 'تعديل الموعد';
    } else if (widget.timeSlot != null && widget.timeSlot!.id.isNotEmpty) {
      return 'نسخ الموعد';
    } else {
      return 'إضافة موعد جديد';
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
          // Ensure end time is after start time
          if (_endTime.hour < _startTime.hour ||
              (_endTime.hour == _startTime.hour && _endTime.minute <= _startTime.minute)) {
            _endTime = TimeOfDay(
              hour: _startTime.hour + 1,
              minute: _startTime.minute,
            );
          }
        } else {
          _endTime = picked;
        }
      });
    }
  }

  void _saveTimeSlot() {
    if (_isSubmitting) return; // Prevent multiple submissions

    if (_formKey.currentState!.validate()) {
      // Validate time range
      final startMinutes = _startTime.hour * 60 + _startTime.minute;
      final endMinutes = _endTime.hour * 60 + _endTime.minute;

      if (endMinutes <= startMinutes) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('وقت النهاية يجب أن يكون بعد وقت البداية'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Validate duration
      if (_durationMinutes <= 0 || _durationMinutes > 480) { // Max 8 hours
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('مدة الموعد يجب أن تكون بين 1 دقيقة و 8 ساعات'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Validate employee selection
      if (_selectedEmployeeId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار أخصائي'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      setState(() => _isSubmitting = true);

      try {
        final timeSlot = TimeSlotModel(
          id: widget.timeSlot?.id ?? '',
          dayOfWeek: _selectedDayOfWeek,
          startTime: _formatTimeOfDay(_startTime),
          endTime: _formatTimeOfDay(_endTime),
          durationMinutes: _durationMinutes,
          isActive: _isActive,
          maxPatients: _maxPatients,
          employeeId: _selectedEmployeeId!,
          createdAt: widget.timeSlot?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );

        if (widget.isEditing) {
          context.read<TimeSlotsBloc>().add(UpdateTimeSlot(timeSlot: timeSlot));
        } else {
          context.read<TimeSlotsBloc>().add(CreateTimeSlot(timeSlot: timeSlot));
        }
      } catch (e) {
        setState(() => _isSubmitting = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البيانات: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  bool _isSubmitting = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<TimeSlotsBloc, TimeSlotsState>(
      listener: (context, state) {
        if (state is TimeSlotCreated || state is TimeSlotUpdated) {
          setState(() => _isSubmitting = false);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(widget.isEditing ? 'تم تحديث الموعد بنجاح' : 'تم حفظ الموعد بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
            Navigator.of(context).pop(); // Close form page
          }
        } else if (state is TimeSlotsError) {
          setState(() => _isSubmitting = false);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ: ${state.message}'),
                backgroundColor: AppColors.error,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(_getPageTitle()),
          backgroundColor: AppColors.white,
          elevation: 0,
          actions: [
            if (_isSubmitting)
              const Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              TextButton(
                onPressed: _saveTimeSlot,
                child: Text(
                  widget.isEditing ? 'تحديث' : 'حفظ',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: 16.sp,
                  ),
                ),
              ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Copy notification banner
                if (!widget.isEditing && widget.timeSlot != null && widget.timeSlot!.id.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    margin: EdgeInsets.only(bottom: 20.h),
                    decoration: BoxDecoration(
                      color: AppColors.info.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.copy, color: AppColors.info, size: 20),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'نسخ موعد',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.info,
                                ),
                              ),
                              Text(
                                'يمكنك تعديل أي من التفاصيل أدناه لإنشاء موعد جديد',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                // Day Selection
                _buildSectionTitle('اليوم *'),
                SizedBox(height: 8.h),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.gray300),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<int>(
                      value: _selectedDayOfWeek,
                      isExpanded: true,
                      items: List.generate(7, (index) {
                        return DropdownMenuItem<int>(
                          value: index,
                          child: Text(_dayNames[index]),
                        );
                      }),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedDayOfWeek = value;
                          });
                        }
                      },
                    ),
                  ),
                ),

                SizedBox(height: 20.h),

                // Employee Selection
                _buildSectionTitle('الأخصائي'),
                SizedBox(height: 8.h),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.gray300),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: _isLoadingEmployees
                      ? const Padding(
                          padding: EdgeInsets.all(12.0),
                          child: Row(
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 12),
                              Text('جاري تحميل الأخصائيين...'),
                            ],
                          ),
                        )
                      : DropdownButtonHideUnderline(
                          child: DropdownButton<String?>(
                            value: _selectedEmployeeId,
                            hint: const Text('اختر أخصائي (مطلوب)'),
                            isExpanded: true,
                            items: [
                              ..._employees.map((employee) {
                                return DropdownMenuItem<String?>(
                                  value: employee.id,
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        radius: 12,
                                        backgroundColor: employee.specialization?.color != null
                                            ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
                                            : AppColors.primary,
                                        child: Text(
                                          employee.name.isNotEmpty ? employee.name[0] : 'أ',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              employee.name,
                                              style: const TextStyle(fontWeight: FontWeight.w500),
                                            ),
                                            if (employee.specialization != null)
                                              Text(
                                                employee.specialization!.name,
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: AppColors.textSecondary,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedEmployeeId = value;
                              });
                            },
                          ),
                        ),
                ),
                SizedBox(height: 20.h),

                // Time Range
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('وقت البداية *'),
                          SizedBox(height: 8.h),
                          GestureDetector(
                            onTap: () => _selectTime(context, true),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.gray300),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.access_time, color: AppColors.primary),
                                  SizedBox(width: 12.w),
                                  Text(
                                    _formatTimeOfDayWithAmPm(_startTime),
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('وقت النهاية *'),
                          SizedBox(height: 8.h),
                          GestureDetector(
                            onTap: () => _selectTime(context, false),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.gray300),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.access_time, color: AppColors.primary),
                                  SizedBox(width: 12.w),
                                  Text(
                                    _formatTimeOfDayWithAmPm(_endTime),
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // Duration
                _buildSectionTitle('مدة الموعد (بالدقائق) *'),
                SizedBox(height: 8.h),
                TextFormField(
                  initialValue: _durationMinutes.toString(),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '30',
                    prefixIcon: const Icon(Icons.timer),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال مدة الموعد';
                    }
                    final duration = int.tryParse(value!);
                    if (duration == null || duration <= 0) {
                      return 'يرجى إدخال رقم صحيح أكبر من صفر';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    final duration = int.tryParse(value);
                    if (duration != null) {
                      _durationMinutes = duration;
                    }
                  },
                ),

                SizedBox(height: 20.h),

                // Max Patients
                _buildSectionTitle('عدد المرضى المسموح *'),
                SizedBox(height: 8.h),
                TextFormField(
                  initialValue: _maxPatients.toString(),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '1',
                    prefixIcon: const Icon(Icons.people),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال عدد المرضى';
                    }
                    final count = int.tryParse(value!);
                    if (count == null || count <= 0) {
                      return 'يرجى إدخال رقم صحيح أكبر من صفر';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    final count = int.tryParse(value);
                    if (count != null) {
                      _maxPatients = count;
                    }
                  },
                ),

                SizedBox(height: 20.h),

                // Active Status
                Row(
                  children: [
                    Switch(
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      'موعد نشط',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 40.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title.contains('*') ? title.replaceAll('*', '') : title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          if (title.contains('*'))
            TextSpan(
              text: ' *',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),
        ],
      ),
    );
  }
}
