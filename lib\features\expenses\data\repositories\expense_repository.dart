import 'dart:developer' as developer;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/models/expense_model.dart';

class ExpenseRepository {
  final SupabaseClient _supabase = Supabase.instance.client;
  // Get all expenses with pagination and filters
  Future<List<ExpenseModel>> getAllExpenses({
    int page = 1,
    int limit = 20,
    String? searchQuery,
    ExpenseCategory? category,
    DateTime? dateFilter,
  }) async {
    try {
      developer.log('Fetching expenses - Page: $page, Limit: $limit', name: 'ExpenseRepository');
      
      var query = _supabase
          .from('expenses')
          .select('''
            *,
            admins!created_by(id, name, email, role, employee_type)
          ''');

      // Apply filters
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.ilike('title', '%$searchQuery%');
      }

      if (category != null) {
        query = query.eq('category', category.value);
      }

      if (dateFilter != null) {
        final dateString = '${dateFilter.year}-${dateFilter.month.toString().padLeft(2, '0')}-${dateFilter.day.toString().padLeft(2, '0')}';
        query = query.eq('expense_date', dateString);
      }

      final response = await query
          .order('expense_date', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      return response.map<ExpenseModel>((data) => ExpenseModel.fromJson(data)).toList();
    } catch (e) {
      developer.log('Error fetching expenses: $e', name: 'ExpenseRepository');
      throw Exception(_getErrorMessage(e, 'جلب المصروفات'));
    }
  }

  // Create new expense
  Future<ExpenseModel> createExpense(ExpenseModel expense) async {
    try {
      developer.log('Creating expense: ${expense.title}', name: 'ExpenseRepository');
      
      final response = await _supabase
          .from('expenses')
          .insert(expense.toJson())
          .select()
          .single();

      return ExpenseModel.fromJson(response);
    } catch (e) {
      developer.log('Error creating expense: $e', name: 'ExpenseRepository');
      throw Exception(_getErrorMessage(e, 'إنشاء المصروف'));
    }
  }

  // Update expense
  Future<ExpenseModel> updateExpense(ExpenseModel expense) async {
    try {
      developer.log('Updating expense: ${expense.id}', name: 'ExpenseRepository');
      
      final response = await _supabase
          .from('expenses')
          .update(expense.toJson())
          .eq('id', expense.id)
          .select()
          .single();

      return ExpenseModel.fromJson(response);
    } catch (e) {
      developer.log('Error updating expense: $e', name: 'ExpenseRepository');
      throw Exception(_getErrorMessage(e, 'تحديث المصروف'));
    }
  }

  // Delete expense
  Future<void> deleteExpense(String expenseId) async {
    try {
      developer.log('Deleting expense: $expenseId', name: 'ExpenseRepository');
      
      await _supabase
          .from('expenses')
          .delete()
          .eq('id', expenseId);
    } catch (e) {
      developer.log('Error deleting expense: $e', name: 'ExpenseRepository');
      throw Exception(_getErrorMessage(e, 'حذف المصروف'));
    }
  }

  // Get expense by ID
  Future<ExpenseModel> getExpenseById(String expenseId) async {
    try {
      final response = await _supabase
          .from('expenses')
          .select('''
            *,
            admins!created_by(id, name, email, role, employee_type)
          ''')
          .eq('id', expenseId)
          .single();

      return ExpenseModel.fromJson(response);
    } catch (e) {
      developer.log('Error fetching expense: $e', name: 'ExpenseRepository');
      throw Exception(_getErrorMessage(e, 'جلب المصروف'));
    }
  }

  // Get total expenses for a date range
  Future<double> getTotalExpenses({
    DateTime? startDate,
    DateTime? endDate,
    ExpenseCategory? category,
  }) async {
    try {
      var query = _supabase
          .from('expenses')
          .select('amount');

      if (startDate != null) {
        query = query.gte('expense_date', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('expense_date', endDate.toIso8601String());
      }

      if (category != null) {
        query = query.eq('category', category.value);
      }

      final response = await query;
      
      double total = 0.0;
      for (final item in response) {
        total += (item['amount'] as num).toDouble();
      }

      return total;
    } catch (e) {
      developer.log('Error calculating total expenses: $e', name: 'ExpenseRepository');
      throw Exception(_getErrorMessage(e, 'حساب إجمالي المصروفات'));
    }
  }

  /// Convert technical errors to user-friendly Arabic messages
  String _getErrorMessage(dynamic error, String operation) {
    final errorString = error.toString().toLowerCase();
    
    // Database constraint errors
    if (errorString.contains('constraint') || errorString.contains('check')) {
      return 'خطأ في البيانات المدخلة. يرجى التحقق من صحة المعلومات.';
    }
    
    // Network/connection errors
    if (errorString.contains('network') || errorString.contains('connection') || 
        errorString.contains('timeout') || errorString.contains('socket')) {
      return 'مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.';
    }
    
    // Authentication errors
    if (errorString.contains('auth') || errorString.contains('unauthorized') || 
        errorString.contains('permission')) {
      return 'ليس لديك صلاحية للقيام بهذا الإجراء.';
    }
    
    // Validation errors
    if (errorString.contains('validation') || errorString.contains('invalid')) {
      return 'البيانات المدخلة غير صحيحة. يرجى المراجعة والمحاولة مرة أخرى.';
    }
    
    // Duplicate errors
    if (errorString.contains('duplicate') || errorString.contains('unique')) {
      return 'هذه البيانات موجودة مسبقاً. يرجى استخدام بيانات مختلفة.';
    }
    
    // Not found errors
    if (errorString.contains('not found') || errorString.contains('404')) {
      return 'البيانات المطلوبة غير موجودة.';
    }
    
    // Server errors
    if (errorString.contains('500') || errorString.contains('server error')) {
      return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
    }
    
    // Default error message
    return 'حدث خطأ أثناء $operation. يرجى المحاولة مرة أخرى.';
  }
}
