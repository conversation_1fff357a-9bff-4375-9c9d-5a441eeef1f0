import 'package:intl/intl.dart';

class NumberFormatter {
  static final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'en_US',
    symbol: 'د.ا',
    decimalDigits: 2,
  );

  static final NumberFormat _numberFormatter = NumberFormat('#,##0.##', 'en_US');

  /// تنسيق الأرقام كعملة
  static String formatCurrency(double amount) {
    if (amount == 0) return '0 د.ا';
    return _currencyFormatter.format(amount);
  }

  /// تنسيق الأرقام العادية
  static String formatNumber(double number) {
    if (number == 0) return '0';
    return _numberFormatter.format(number);
  }

  /// تنسيق الأرقام الصحيحة
  static String formatInteger(int number) {
    if (number == 0) return '0';
    return NumberFormat('#,##0', 'en_US').format(number);
  }

  /// تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// تحويل الرقم إلى نص مختصر (مثل 1.5K, 2.3M)
  static String formatCompact(double number) {
    if (number < 1000) {
      return number.toStringAsFixed(0);
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else if (number < 1000000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else {
      return '${(number / 1000000000).toStringAsFixed(1)}B';
    }
  }

  /// تنسيق الأرقام مع إضافة علامة + أو - للتغيير
  static String formatChange(double change) {
    if (change > 0) {
      return '+${formatCurrency(change)}';
    } else if (change < 0) {
      return formatCurrency(change);
    } else {
      return formatCurrency(0);
    }
  }

  /// تنسيق النسبة المئوية للتغيير
  static String formatPercentageChange(double change) {
    if (change > 0) {
      return '+${formatPercentage(change)}';
    } else if (change < 0) {
      return formatPercentage(change);
    } else {
      return formatPercentage(0);
    }
  }
}