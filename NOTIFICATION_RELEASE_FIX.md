# حل مشكلة الإشعارات في نسخة الـ Release

## المشكلة
الإشعارات تعمل في نسخة الـ Debug ولكن لا تعمل في نسخة الـ Release.

## الحلول المطبقة

### 1. إعدادات ProGuard
- تم إنشاء ملف `proguard-rules.pro` مع قواعد حماية Firebase والإشعارات
- تم تحديث `build.gradle.kts` لتفعيل ProGuard في الـ Release

### 2. إعدادات Android Manifest
- تم إضافة خدمة Firebase Messaging مخصصة
- تم تحديث الأذونات للإشعارات
- تم إضافة إعدادات الإشعارات الافتراضية

### 3. الملفات المضافة/المحدثة

#### Android Files:
- `android/app/proguard-rules.pro` - قواعد ProGuard
- `android/app/src/main/res/xml/network_security_config.xml` - إعدادات الأمان
- `android/app/src/main/res/drawable/ic_notification.xml` - أيقونة الإشعارات
- `android/app/src/main/res/values/colors.xml` - ألوان الإشعارات
- `android/app/src/main/kotlin/com/iihcadmin/MainActivity.kt` - MainActivity محدثة
- `android/app/src/main/kotlin/com/iihcadmin/MyFirebaseMessagingService.kt` - خدمة Firebase مخصصة

#### Flutter Files:
- `lib/core/services/notification_manager.dart` - مدير الإشعارات
- تحديث `lib/core/services/firebase_messaging_service.dart`

### 4. خطوات البناء للـ Release

```bash
# 1. تنظيف المشروع
flutter clean

# 2. الحصول على الحزم
flutter pub get

# 3. بناء نسخة Release
flutter build apk --release

# أو بناء App Bundle
flutter build appbundle --release
```

### 5. التحقق من الإعدادات

#### في Firebase Console:
1. تأكد من أن مفاتيح SHA-1 و SHA-256 مضافة للـ Release
2. تحقق من إعدادات Cloud Messaging
3. تأكد من تفعيل Firebase Cloud Messaging API

#### في Android:
1. تأكد من أن الأذونات مطلوبة بشكل صحيح
2. تحقق من إعدادات الإشعارات في الجهاز
3. تأكد من أن التطبيق غير محظور من الإشعارات

### 6. اختبار الإشعارات

#### من Firebase Console:
1. اذهب إلى Cloud Messaging
2. أنشئ رسالة جديدة
3. اختر التطبيق
4. أرسل إشعار تجريبي

#### من التطبيق:
1. سجل دخول مستخدم
2. تأكد من حفظ FCM Token
3. جرب إرسال إشعار من الأدمن

### 7. استكشاف الأخطاء

#### إذا لم تصل الإشعارات:

1. **تحقق من FCM Token:**
```dart
final token = await FirebaseMessaging.instance.getToken();
print('FCM Token: $token');
```

2. **تحقق من الأذونات:**
```dart
final settings = await FirebaseMessaging.instance.requestPermission();
print('Permission status: ${settings.authorizationStatus}');
```

3. **تحقق من Logs:**
```bash
adb logcat | grep -E "(FirebaseMessaging|MyFirebaseMsgService)"
```

4. **تحقق من قاعدة البيانات:**
- تأكد من حفظ FCM Token في جدول `user_fcm_tokens`
- تحقق من أن `is_active = true`

### 8. نصائح إضافية

1. **استخدم SHA-256 للـ Release:**
```bash
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

2. **تأكد من إعدادات البطارية:**
- أضف التطبيق لقائمة التطبيقات المستثناة من توفير البطارية

3. **تحقق من إعدادات الجهاز:**
- تأكد من تفعيل الإشعارات للتطبيق
- تحقق من إعدادات "عدم الإزعاج"

### 9. الملفات المهمة للمراجعة

- `android/app/build.gradle.kts` - إعدادات البناء
- `android/app/src/main/AndroidManifest.xml` - الأذونات والخدمات
- `android/app/google-services.json` - إعدادات Firebase
- `lib/core/services/firebase_messaging_service.dart` - خدمة الإشعارات

### 10. الدعم والمساعدة

إذا استمرت المشكلة:
1. تحقق من Firebase Console للأخطاء
2. راجع Android Logcat للرسائل
3. تأكد من تحديث جميع الحزم
4. جرب إعادة تثبيت التطبيق

## ملاحظات مهمة

- تأكد من استخدام نفس مفاتيح التوقيع للـ Debug والـ Release في Firebase
- قد تحتاج لإعادة تثبيت التطبيق بعد التحديثات
- تأكد من أن الجهاز متصل بالإنترنت
- بعض الأجهزة تتطلب إعدادات خاصة للإشعارات (Xiaomi, Huawei, etc.)