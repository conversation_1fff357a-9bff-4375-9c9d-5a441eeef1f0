import 'package:equatable/equatable.dart';

class MedicalInfoModel extends Equatable {
  final String id;
  final String patientId;
  final String infoType;
  final String name;
  final String? description;
  final String? dosage;
  final String? frequency;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? severity;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MedicalInfoModel({
    required this.id,
    required this.patientId,
    required this.infoType,
    required this.name,
    this.description,
    this.dosage,
    this.frequency,
    this.startDate,
    this.endDate,
    this.severity,
    this.notes,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MedicalInfoModel.fromJson(Map<String, dynamic> json) {
    return MedicalInfoModel(
      id: json['id'] as String,
      patientId: json['patient_id'] as String,
      infoType: json['info_type'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      dosage: json['dosage'] as String?,
      frequency: json['frequency'] as String?,
      startDate: json['start_date'] != null 
          ? DateTime.parse(json['start_date'] as String) 
          : null,
      endDate: json['end_date'] != null 
          ? DateTime.parse(json['end_date'] as String) 
          : null,
      severity: json['severity'] as String?,
      notes: json['notes'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'patient_id': patientId,
      'info_type': infoType,
      'name': name,
      'description': description,
      'dosage': dosage,
      'frequency': frequency,
      'start_date': startDate?.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'severity': severity,
      'notes': notes,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
    
    // Only include ID if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }
    
    return json;
  }

  MedicalInfoModel copyWith({
    String? id,
    String? patientId,
    String? infoType,
    String? name,
    String? description,
    String? dosage,
    String? frequency,
    DateTime? startDate,
    DateTime? endDate,
    String? severity,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MedicalInfoModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      infoType: infoType ?? this.infoType,
      name: name ?? this.name,
      description: description ?? this.description,
      dosage: dosage ?? this.dosage,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      severity: severity ?? this.severity,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get infoTypeDisplayName {
    switch (infoType.toLowerCase()) {
      case 'medication':
        return 'دواء';
      case 'supplement':
        return 'مكمل غذائي';
      case 'condition':
        return 'حالة صحية';
      case 'allergy':
        return 'حساسية';
      case 'activity':
        return 'نشاط بدني';
      default:
        return infoType;
    }
  }

  String get severityDisplayName {
    switch (severity?.toLowerCase()) {
      case 'mild':
        return 'خفيفة';
      case 'moderate':
        return 'متوسطة';
      case 'severe':
        return 'شديدة';
      default:
        return severity ?? 'غير محدد';
    }
  }

  bool get isMedication => infoType.toLowerCase() == 'medication';
  bool get isSupplement => infoType.toLowerCase() == 'supplement';
  bool get isCondition => infoType.toLowerCase() == 'condition';
  bool get isAllergy => infoType.toLowerCase() == 'allergy';
  bool get isActivity => infoType.toLowerCase() == 'activity';

  bool get hasDosage => dosage != null && dosage!.isNotEmpty;
  bool get hasFrequency => frequency != null && frequency!.isNotEmpty;
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasSeverity => severity != null && severity!.isNotEmpty;

  bool get isOngoing => endDate == null || endDate!.isAfter(DateTime.now());
  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  String get statusText {
    if (!isActive) return 'غير نشط';
    if (isExpired) return 'منتهي';
    if (isOngoing) return 'مستمر';
    return 'نشط';
  }

  @override
  List<Object?> get props => [
        id,
        patientId,
        infoType,
        name,
        description,
        dosage,
        frequency,
        startDate,
        endDate,
        severity,
        notes,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'MedicalInfoModel(id: $id, patientId: $patientId, type: $infoType, '
        'name: $name, isActive: $isActive, status: $statusText)';
  }
}
