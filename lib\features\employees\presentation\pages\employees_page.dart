import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../bloc/employees_bloc.dart';
import '../widgets/add_employee_dialog.dart';

class EmployeesPage extends StatefulWidget {
  const EmployeesPage({super.key});

  @override
  State<EmployeesPage> createState() => _EmployeesPageState();
}

class _EmployeesPageState extends State<EmployeesPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load employees when page initializes
    context.read<EmployeesBloc>().add(LoadAllEmployees());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddEmployeeDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const AddEmployeeDialog(),
    );
    if (result == true) {
      context.read<EmployeesBloc>().add(LoadAllEmployees());
    }
  }

  void _filterEmployees(String filter) {
    setState(() {
      _selectedFilter = filter;
    });

    switch (filter) {
      case 'all':
        context.read<EmployeesBloc>().add(LoadAllEmployees());
        break;
      case 'admin':
        context.read<EmployeesBloc>().add(const LoadEmployeesByType('admin'));
        break;
      case 'receptionist':
        context.read<EmployeesBloc>().add(const LoadEmployeesByType('receptionist'));
        break;
      case 'specialist':
        context.read<EmployeesBloc>().add(const LoadEmployeesByType('specialist'));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              border: Border(
                bottom: BorderSide(color: AppColors.border, width: 1),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.people,
                  size: 32,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إدارة الموظفين',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'إضافة وإدارة موظفي المركز',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _showAddEmployeeDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة موظف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          ),

          // Filter Tabs
          Container(
            color: AppColors.surface,
            child: TabBar(
              controller: _tabController,
              onTap: (index) {
                final filters = ['all', 'admin', 'receptionist', 'specialist'];
                _filterEmployees(filters[index]);
              },
              tabs: const [
                Tab(text: 'الكل'),
                Tab(text: 'المديرين'),
                Tab(text: 'الاستقبال'),
                Tab(text: 'الأخصائيين'),
              ],
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primary,
            ),
          ),

          // Content
          Expanded(
            child: BlocConsumer<EmployeesBloc, EmployeesState>(
              listener: (context, state) {
                if (state is EmployeeOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else if (state is EmployeesError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is EmployeesLoading) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.primary,
                    ),
                  );
                }

                if (state is EmployeesError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'حدث خطأ في تحميل الموظفين',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () {
                            context.read<EmployeesBloc>().add(LoadAllEmployees());
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                List<EmployeeModel> employees = [];
                if (state is EmployeesLoaded) {
                  employees = state.employees;
                } else if (state is EmployeeOperationSuccess) {
                  employees = state.employees;
                }

                if (employees.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.people_outline,
                          size: 64,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا يوجد موظفين',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'ابدأ بإضافة موظف جديد',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _showAddEmployeeDialog,
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة موظف'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return TabBarView(
                  controller: _tabController,
                  children: [
                    _buildEmployeesList(employees),
                    _buildEmployeesList(employees.where((e) => e.isAdmin).toList()),
                    _buildEmployeesList(employees.where((e) => e.isReceptionist).toList()),
                    _buildEmployeesList(employees.where((e) => e.isSpecialist).toList()),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeesList(List<EmployeeModel> employees) {
    if (employees.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد موظفين في هذه الفئة',
          style: TextStyle(
            fontSize: 16,
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(24),
      itemCount: employees.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildEmployeeCard(employees[index]),
        );
      },
    );
  }

  Widget _buildEmployeeCard(EmployeeModel employee) {
    return Card(
      elevation: 2,
      color: AppColors.surface,
      child: InkWell(
        onTap: () => _showEmployeeDetails(employee),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Top Row: Avatar and Basic Info
              Row(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: employee.specialization?.color != null
                        ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
                        : AppColors.primary,
                    child: Text(
                      employee.name.isNotEmpty ? employee.name[0] : 'م',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Employee Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          employee.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          employee.displayRole,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        if (employee.specialization != null) ...[
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF'))).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              employee.specialization!.name,
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF'))),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Status
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: employee.isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      employee.isActive ? 'نشط' : 'غير نشط',
                      style: TextStyle(
                        fontSize: 12,
                        color: employee.isActive ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Contact Info
              Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        const Icon(
                          Icons.email,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            employee.email,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (employee.phone != null) ...[
                    const SizedBox(width: 16),
                    Row(
                      children: [
                        const Icon(
                          Icons.phone,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          employee.phone!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),

              // Actions Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton(
                    icon: Icons.visibility,
                    label: 'عرض',
                    color: AppColors.primary,
                    onPressed: () => _showEmployeeDetails(employee),
                  ),
                  _buildActionButton(
                    icon: Icons.edit,
                    label: 'تعديل',
                    color: AppColors.primary,
                    onPressed: () async {
                      final result = await showDialog<bool>(
                        context: context,
                        builder: (context) => AddEmployeeDialog(employee: employee),
                      );
                      if (result == true) {
                        context.read<EmployeesBloc>().add(LoadAllEmployees());
                      }
                    },
                  ),
                  _buildActionButton(
                    icon: employee.isActive ? Icons.pause : Icons.play_arrow,
                    label: employee.isActive ? 'إيقاف' : 'تفعيل',
                    color: employee.isActive ? Colors.orange : Colors.green,
                    onPressed: () {
                      context.read<EmployeesBloc>().add(
                        ToggleEmployeeStatus(employee.id, !employee.isActive),
                      );
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.delete,
                    label: 'حذف',
                    color: AppColors.error,
                    onPressed: () => _showDeleteConfirmation(employee),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(EmployeeModel employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${employee.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<EmployeesBloc>().add(DeleteEmployee(employee.id));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showEmployeeDetails(EmployeeModel employee) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: employee.specialization?.color != null
                        ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
                        : AppColors.primary,
                    child: Text(
                      employee.name.isNotEmpty ? employee.name[0] : 'م',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          employee.name,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          employee.displayRole,
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        if (employee.specialization != null) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF'))).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Text(
                              employee.specialization!.name,
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF'))),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 24),
              const Divider(),
              const SizedBox(height: 16),

              // Details
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailSection('المعلومات الأساسية', [
                        _buildDetailRow('البريد الإلكتروني', employee.email, Icons.email),
                        if (employee.phone != null)
                          _buildDetailRow('رقم الهاتف', employee.phone!, Icons.phone),
                        if (employee.address != null)
                          _buildDetailRow('العنوان', employee.address!, Icons.location_on),
                        _buildDetailRow('الحالة', employee.isActive ? 'نشط' : 'غير نشط',
                          employee.isActive ? Icons.check_circle : Icons.cancel,
                          valueColor: employee.isActive ? Colors.green : Colors.red),
                      ]),

                      const SizedBox(height: 24),

                      _buildDetailSection('معلومات العمل', [
                        _buildDetailRow('نوع الموظف', employee.displayRole, Icons.work),
                        if (employee.hireDate != null)
                          _buildDetailRow('تاريخ التوظيف',
                            '${employee.hireDate!.day}/${employee.hireDate!.month}/${employee.hireDate!.year}',
                            Icons.calendar_today),
                        if (employee.salary != null)
                          _buildDetailRow('الراتب', '${employee.salary!.toStringAsFixed(0)} ج.م', Icons.attach_money),
                      ]),

                      if (employee.notes != null && employee.notes!.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildDetailSection('ملاحظات', [
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: AppColors.border),
                            ),
                            child: Text(
                              employee.notes!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.textPrimary,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ]),
                      ],

                      const SizedBox(height: 24),

                      _buildDetailSection('معلومات النظام', [
                        _buildDetailRow('تاريخ الإنشاء',
                          '${employee.createdAt.day}/${employee.createdAt.month}/${employee.createdAt.year}',
                          Icons.add_circle),
                        _buildDetailRow('آخر تحديث',
                          '${employee.updatedAt.day}/${employee.updatedAt.month}/${employee.updatedAt.year}',
                          Icons.update),
                        if (employee.lastLogin != null)
                          _buildDetailRow('آخر تسجيل دخول',
                            '${employee.lastLogin!.day}/${employee.lastLogin!.month}/${employee.lastLogin!.year}',
                            Icons.login),
                      ]),
                    ],
                  ),
                ),
              ),

              // Actions
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        final result = await showDialog<bool>(
                          context: context,
                          builder: (context) => AddEmployeeDialog(employee: employee),
                        );
                        if (result == true) {
                          context.read<EmployeesBloc>().add(LoadAllEmployees());
                        }
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        context.read<EmployeesBloc>().add(
                          ToggleEmployeeStatus(employee.id, !employee.isActive),
                        );
                      },
                      icon: Icon(employee.isActive ? Icons.pause : Icons.play_arrow),
                      label: Text(employee.isActive ? 'إيقاف' : 'تفعيل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: employee.isActive ? Colors.orange : Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
