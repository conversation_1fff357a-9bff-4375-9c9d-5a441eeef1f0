import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/lab_tests_repository.dart';
import 'lab_tests_event.dart';
import 'lab_tests_state.dart';

class LabTestsBloc extends Bloc<LabTestsEvent, LabTestsState> {
  final LabTestsRepository _labTestsRepository;

  LabTestsBloc({required LabTestsRepository labTestsRepository})
      : _labTestsRepository = labTestsRepository,
        super(const LabTestsInitial()) {
    on<LoadLabTestsByPatientId>(_onLoadLabTestsByPatientId);
    on<LoadLabTestsByType>(_onLoadLabTestsByType);
    on<LoadLabTestsByDateRange>(_onLoadLabTestsByDateRange);
    on<AddLabTest>(_onAddLabTest);
    on<UpdateLabTest>(_onUpdateLabTest);
    on<DeleteLabTest>(_onDeleteLabTest);
    on<UploadLabTestImage>(_onUploadLabTestImage);
    on<DeleteLabTestImage>(_onDeleteLabTestImage);
    on<SearchLabTests>(_onSearchLabTests);
    on<RefreshLabTests>(_onRefreshLabTests);
  }

  Future<void> _onLoadLabTestsByPatientId(
    LoadLabTestsByPatientId event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Loading lab tests for patient: ${event.patientId}');
    emit(const LabTestsLoading());
    try {
      final labTests = await _labTestsRepository.getLabTestsByPatientId(event.patientId);

      debugPrint('📊 LabTestsBloc: Loaded ${labTests.length} lab tests');

      emit(LabTestsLoaded(
        labTests: labTests,
        patientId: event.patientId,
      ));
      debugPrint('✅ LabTestsBloc: Successfully emitted LabTestsLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ LabTestsBloc Error: $e');
      debugPrint('📍 LabTestsBloc Stack trace: $stackTrace');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onLoadLabTestsByType(
    LoadLabTestsByType event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Loading lab tests by type: ${event.testType}');
    emit(const LabTestsLoading());
    try {
      final labTests = await _labTestsRepository.getLabTestsByType(
        event.patientId,
        event.testType,
      );

      debugPrint('📊 LabTestsBloc: Loaded ${labTests.length} lab tests of type: ${event.testType}');

      emit(LabTestsByTypeLoaded(
        labTests: labTests,
        patientId: event.patientId,
        testType: event.testType,
      ));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error loading by type: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onLoadLabTestsByDateRange(
    LoadLabTestsByDateRange event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Loading lab tests by date range');
    emit(const LabTestsLoading());
    try {
      final labTests = await _labTestsRepository.getLabTestsByDateRange(
        event.patientId,
        event.startDate,
        event.endDate,
      );

      debugPrint('📊 LabTestsBloc: Loaded ${labTests.length} lab tests in date range');

      emit(LabTestsByDateRangeLoaded(
        labTests: labTests,
        patientId: event.patientId,
        startDate: event.startDate,
        endDate: event.endDate,
      ));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error loading by date range: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onAddLabTest(
    AddLabTest event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Adding new lab test');
    try {
      final newLabTest = await _labTestsRepository.addLabTest(event.labTest);
      debugPrint('✅ LabTestsBloc: Successfully added lab test: ${newLabTest.id}');

      emit(LabTestCreated(labTest: newLabTest));

      // Reload lab tests
      add(LoadLabTestsByPatientId(patientId: event.labTest.patientId));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error adding lab test: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateLabTest(
    UpdateLabTest event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Updating lab test');
    try {
      final updatedLabTest = await _labTestsRepository.updateLabTest(event.labTest);
      debugPrint('✅ LabTestsBloc: Successfully updated lab test: ${updatedLabTest.id}');

      emit(LabTestUpdated(labTest: updatedLabTest));

      // Reload lab tests
      add(LoadLabTestsByPatientId(patientId: event.labTest.patientId));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error updating lab test: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onDeleteLabTest(
    DeleteLabTest event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Deleting lab test');
    try {
      // Don't emit loading here as it's handled by UI
      await _labTestsRepository.deleteLabTest(event.labTestId);
      debugPrint('✅ LabTestsBloc: Successfully deleted lab test: ${event.labTestId}');

      emit(LabTestDeleted(labTestId: event.labTestId));

      // Reload lab tests using patientId from event
      debugPrint('🔄 LabTestsBloc: Reloading data after deletion for patient: ${event.patientId}');
      add(LoadLabTestsByPatientId(patientId: event.patientId));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error deleting lab test: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onUploadLabTestImage(
    UploadLabTestImage event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Uploading lab test image');
    try {
      final imageUrl = await _labTestsRepository.uploadLabTestImage(
        event.imageFile,
        event.patientId,
      );
      debugPrint('✅ LabTestsBloc: Successfully uploaded image: $imageUrl');

      emit(LabTestImageUploaded(imageUrl: imageUrl));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error uploading image: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onDeleteLabTestImage(
    DeleteLabTestImage event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Deleting lab test image');
    try {
      await _labTestsRepository.deleteLabTestImage(event.imageUrl);
      debugPrint('✅ LabTestsBloc: Successfully deleted image');

      emit(LabTestImageDeleted(imageUrl: event.imageUrl));
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error deleting image: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onSearchLabTests(
    SearchLabTests event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Searching lab tests with query: ${event.query}');
    try {
      final searchResults = await _labTestsRepository.searchLabTests(
        event.patientId,
        event.query,
      );

      debugPrint('📊 LabTestsBloc: Found ${searchResults.length} lab tests matching query');

      if (state is LabTestsLoaded) {
        final currentState = state as LabTestsLoaded;
        emit(currentState.copyWith(
          searchResults: searchResults,
          searchQuery: event.query,
        ));
      }
    } catch (e) {
      debugPrint('❌ LabTestsBloc Error searching lab tests: $e');
      emit(LabTestsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshLabTests(
    RefreshLabTests event,
    Emitter<LabTestsState> emit,
  ) async {
    debugPrint('🔄 LabTestsBloc: Refreshing lab tests');
    add(LoadLabTestsByPatientId(patientId: event.patientId));
  }
}
