<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Deit Rx User</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>deit_rx_user</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- Firebase Messaging and Notifications -->
	<key>FirebaseMessagingAutoInitEnabled</key>
	<true/>
	<key>FirebaseAnalyticsCollectionEnabled</key>
	<false/>
	
	<!-- Background modes for notifications -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>remote-notification</string>
		<string>background-processing</string>
	</array>
	
	<!-- Notification permissions -->
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>
	
	<!-- App Transport Security -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	
	<!-- Privacy usage descriptions -->
	<key>NSUserNotificationUsageDescription</key>
	<string>This app needs notification permission to send you important updates about your appointments and health reminders.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app needs local network access to communicate with medical devices and local services.</string>
	
	<!-- Minimum iOS version -->
	<key>MinimumOSVersion</key>
	<string>12.0</string>
	
	<!-- Support for iOS 15+ notification features -->
	<key>UNUserNotificationCenter</key>
	<dict>
		<key>UNAuthorizationOptionAlert</key>
		<true/>
		<key>UNAuthorizationOptionBadge</key>
		<true/>
		<key>UNAuthorizationOptionSound</key>
		<true/>
		<key>UNAuthorizationOptionCriticalAlert</key>
		<true/>
		<key>UNAuthorizationOptionAnnouncement</key>
		<true/>
	</dict>
</dict>
</plist>
