import 'dart:io';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

class NotificationPermissionService {
  static const MethodChannel _channel = MethodChannel('com.iihcadmin/notifications');
  static final Logger _logger = Logger('NotificationPermissionService');

  /// Request notification permissions for all Android and iOS versions
  static Future<bool> requestNotificationPermissions() async {
    try {
      _logger.info('🔔 Requesting notification permissions...');

      if (Platform.isAndroid) {
        return await _requestAndroidNotificationPermissions();
      } else if (Platform.isIOS) {
        return await _requestIOSNotificationPermissions();
      }

      return false;
    } catch (e) {
      _logger.error('❌ Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Request Android notification permissions (supports all versions)
  static Future<bool> _requestAndroidNotificationPermissions() async {
    try {
      _logger.info('📱 Requesting Android notification permissions...');

      // For Android 13+ (API level 33+)
      if (await _isAndroid13OrHigher()) {
        final status = await Permission.notification.request();
        _logger.info('📱 Android 13+ notification permission: $status');
        
        if (status.isGranted) {
          await _createNotificationChannels();
          return true;
        } else if (status.isDenied) {
          _logger.warning('⚠️ Notification permission denied');
          return false;
        } else if (status.isPermanentlyDenied) {
          _logger.warning('⚠️ Notification permission permanently denied');
          await openAppSettings();
          return false;
        }
      } else {
        // For Android 12 and below, notifications are enabled by default
        _logger.info('📱 Android 12 and below - notifications enabled by default');
        await _createNotificationChannels();
        return true;
      }

      return false;
    } catch (e) {
      _logger.error('❌ Error requesting Android notification permissions: $e');
      return false;
    }
  }

  /// Request iOS notification permissions (supports all versions)
  static Future<bool> _requestIOSNotificationPermissions() async {
    try {
      _logger.info('🍎 Requesting iOS notification permissions...');

      // Request notification permission
      final status = await Permission.notification.request();
      _logger.info('🍎 iOS notification permission: $status');

      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        _logger.warning('⚠️ iOS notification permission denied');
        return false;
      } else if (status.isPermanentlyDenied) {
        _logger.warning('⚠️ iOS notification permission permanently denied');
        await openAppSettings();
        return false;
      }

      return false;
    } catch (e) {
      _logger.error('❌ Error requesting iOS notification permissions: $e');
      return false;
    }
  }

  /// Check if current Android version is 13 or higher
  static Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    try {
      // Get Android SDK version
      final version = Platform.operatingSystemVersion;
      _logger.info('📱 Android version: $version');
      
      // Extract API level from version string
      final regex = RegExp(r'SDK (\d+)');
      final match = regex.firstMatch(version);
      
      if (match != null) {
        final apiLevel = int.parse(match.group(1)!);
        return apiLevel >= 33; // Android 13 is API level 33
      }
      
      return false;
    } catch (e) {
      _logger.error('❌ Error checking Android version: $e');
      return false;
    }
  }

  /// Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    try {
      if (Platform.isAndroid) {
        await _channel.invokeMethod('createNotificationChannels');
        _logger.info('✅ Notification channels created');
      }
    } catch (e) {
      _logger.error('❌ Error creating notification channels: $e');
    }
  }

  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    try {
      if (Platform.isAndroid) {
        final enabled = await _channel.invokeMethod('areNotificationsEnabled');
        return enabled ?? false;
      } else if (Platform.isIOS) {
        final status = await Permission.notification.status;
        return status.isGranted;
      }
      
      return false;
    } catch (e) {
      _logger.error('❌ Error checking notification status: $e');
      return false;
    }
  }

  /// Request permission through native channel (Android 13+)
  static Future<void> requestPermissionThroughNative() async {
    try {
      if (Platform.isAndroid) {
        await _channel.invokeMethod('requestNotificationPermission');
      }
    } catch (e) {
      _logger.error('❌ Error requesting permission through native: $e');
    }
  }

  /// Show permission rationale dialog
  static Future<bool> shouldShowPermissionRationale() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.notification.status;
        return status.isDenied && !status.isPermanentlyDenied;
      }
      
      return false;
    } catch (e) {
      _logger.error('❌ Error checking permission rationale: $e');
      return false;
    }
  }

  /// Get detailed permission status
  static Future<Map<String, dynamic>> getPermissionStatus() async {
    try {
      final status = await Permission.notification.status;
      final enabled = await areNotificationsEnabled();
      
      return {
        'permission_status': status.toString(),
        'is_granted': status.isGranted,
        'is_denied': status.isDenied,
        'is_permanently_denied': status.isPermanentlyDenied,
        'notifications_enabled': enabled,
        'platform': Platform.operatingSystem,
        'should_show_rationale': await shouldShowPermissionRationale(),
      };
    } catch (e) {
      _logger.error('❌ Error getting permission status: $e');
      return {
        'error': e.toString(),
        'platform': Platform.operatingSystem,
      };
    }
  }

  /// Initialize notification permissions on app start
  static Future<void> initializePermissions() async {
    try {
      _logger.info('🔄 Initializing notification permissions...');
      
      final status = await getPermissionStatus();
      _logger.info('📊 Current permission status: $status');
      
      if (!status['is_granted']) {
        _logger.info('⚠️ Notifications not granted, will request when needed');
      } else {
        _logger.info('✅ Notifications already granted');
        
        // Create channels for Android
        if (Platform.isAndroid) {
          await _createNotificationChannels();
        }
      }
    } catch (e) {
      _logger.error('❌ Error initializing permissions: $e');
    }
  }

  /// Request permissions with user-friendly flow
  static Future<bool> requestPermissionsWithFlow() async {
    try {
      _logger.info('🔄 Starting permission request flow...');
      
      // Check current status
      final currentStatus = await getPermissionStatus();
      
      if (currentStatus['is_granted']) {
        _logger.info('✅ Permissions already granted');
        return true;
      }
      
      if (currentStatus['is_permanently_denied']) {
        _logger.warning('⚠️ Permissions permanently denied, opening settings');
        await openAppSettings();
        return false;
      }
      
      // Request permissions
      final granted = await requestNotificationPermissions();
      
      if (granted) {
        _logger.info('✅ Notification permissions granted successfully');
      } else {
        _logger.warning('❌ Notification permissions denied');
      }
      
      return granted;
    } catch (e) {
      _logger.error('❌ Error in permission request flow: $e');
      return false;
    }
  }
}