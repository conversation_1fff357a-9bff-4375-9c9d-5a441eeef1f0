import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/employee_tasks_repository.dart';
import 'employee_tasks_event.dart';
import 'employee_tasks_state.dart';
import '../../../../core/services/task_notification_service.dart';
import '../../../../core/services/enhanced_auth_service.dart';

class EmployeeTasksBloc extends Bloc<EmployeeTasksEvent, EmployeeTasksState> {
  final TaskNotificationService _notificationService = TaskNotificationService();

  EmployeeTasksBloc() : super(const EmployeeTasksInitial()) {
    on<LoadAllTasks>(_onLoadAllTasks);
    on<LoadTasksByEmployee>(_onLoadTasksByEmployee);
    on<LoadTasksByStatus>(_onLoadTasksByStatus);
    on<LoadOverdueTasks>(_onLoadOverdueTasks);
    on<LoadTasksDueSoon>(_onLoadTasksDueSoon);
    on<AddTask>(_onAddTask);
    on<UpdateTask>(_onUpdateTask);
    on<UpdateTaskStatus>(_onUpdateTaskStatus);
    on<DeleteTask>(_onDeleteTask);
    on<RefreshTasks>(_onRefreshTasks);
  }

  Future<void> _onLoadAllTasks(
    LoadAllTasks event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading all tasks...');
      final tasks = await EmployeeTasksRepository.getAllTasks();
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading tasks: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadTasksByEmployee(
    LoadTasksByEmployee event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading tasks for employee: ${event.employeeId}');
      final tasks = await EmployeeTasksRepository.getTasksByEmployeeId(event.employeeId);
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks for employee');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading employee tasks: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadTasksByStatus(
    LoadTasksByStatus event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading tasks with status: ${event.status}');
      final tasks = await EmployeeTasksRepository.getTasksByStatus(event.status);
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks with status ${event.status}');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading tasks by status: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadOverdueTasks(
    LoadOverdueTasks event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading overdue tasks...');
      final tasks = await EmployeeTasksRepository.getOverdueTasks();
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} overdue tasks');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading overdue tasks: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadTasksDueSoon(
    LoadTasksDueSoon event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading tasks due soon...');
      final tasks = await EmployeeTasksRepository.getTasksDueSoon();
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks due soon');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading tasks due soon: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onAddTask(
    AddTask event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Adding new task: ${event.task.title}');
      final newTask = await EmployeeTasksRepository.addTask(event.task);
      debugPrint('✅ EmployeeTasksBloc: Successfully added task');
      
      // إرسال إشعار للأخصائي المكلف بالمهمة
      try {
        debugPrint('📱 EmployeeTasksBloc: Sending notification to employee: ${newTask.employeeId}');
        await _notificationService.notifyNewTask(
          taskId: newTask.id,
          taskTitle: newTask.title,
          taskDescription: newTask.description,
          assigneeUserId: newTask.employeeId,
          assignerUserId: newTask.assignedBy ?? 'unknown',
          assignedByName: await _getAssignerName(newTask.assignedBy),
          assigneeName: await _getEmployeeName(newTask.employeeId),
        );
        debugPrint('✅ EmployeeTasksBloc: Task notification sent successfully');
      } catch (notificationError) {
        debugPrint('❌ EmployeeTasksBloc: Failed to send task notification: $notificationError');
        // لا نوقف العملية إذا فشل الإشعار
      }
      
      emit(TaskAdded(newTask));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error adding task: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onUpdateTask(
    UpdateTask event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Updating task: ${event.task.title}');
      final updatedTask = await EmployeeTasksRepository.updateTask(event.task);
      debugPrint('✅ EmployeeTasksBloc: Successfully updated task');
      
      emit(TaskUpdated(updatedTask));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error updating task: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onUpdateTaskStatus(
    UpdateTaskStatus event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Updating task status: ${event.taskId} to ${event.status}');
      final updatedTask = await EmployeeTasksRepository.updateTaskStatus(event.taskId, event.status);
      debugPrint('✅ EmployeeTasksBloc: Successfully updated task status');
      
      // إرسال إشعار عند تغيير حالة المهمة
      try {
        debugPrint('📱 EmployeeTasksBloc: Sending status change notification for task: ${event.taskId}');
        
        // الحصول على معرف المستخدم الحالي
        final currentUserId = EnhancedAuthService.currentUserId ?? 'unknown';
        final currentUserName = await _getCurrentUserName();
        
        await _notificationService.notifyTaskStatusChanged(
          taskId: event.taskId,
          taskTitle: updatedTask.title,
          assignerUserId: updatedTask.assignedBy ?? 'unknown',
          assigneeUserId: updatedTask.employeeId,
          oldStatus: 'pending', // يمكن تحسين هذا لاحقاً
          newStatus: event.status,
          changedByName: currentUserName,
          changedByUserId: currentUserId,
        );
        debugPrint('✅ EmployeeTasksBloc: Status change notification sent successfully');
      } catch (notificationError) {
        debugPrint('❌ EmployeeTasksBloc: Failed to send status change notification: $notificationError');
      }
      
      emit(TaskStatusUpdated(updatedTask));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error updating task status: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onDeleteTask(
    DeleteTask event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Deleting task: ${event.taskId}');
      await EmployeeTasksRepository.deleteTask(event.taskId);
      debugPrint('✅ EmployeeTasksBloc: Successfully deleted task');
      
      emit(TaskDeleted(event.taskId));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error deleting task: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onRefreshTasks(
    RefreshTasks event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    add(const LoadAllTasks());
  }

  // Helper methods لجلب أسماء المستخدمين
  Future<String> _getAssignerName(String? assignerId) async {
    if (assignerId == null) return 'المدير';
    
    try {
      final response = await EmployeeTasksRepository.getAdminName(assignerId);
      return response ?? 'المدير';
    } catch (e) {
      debugPrint('❌ Error getting assigner name: $e');
      return 'المدير';
    }
  }

  Future<String> _getEmployeeName(String employeeId) async {
    try {
      final response = await EmployeeTasksRepository.getEmployeeName(employeeId);
      return response ?? 'الأخصائي';
    } catch (e) {
      debugPrint('❌ Error getting employee name: $e');
      return 'الأخصائي';
    }
  }

  Future<String> _getCurrentUserName() async {
    try {
      final currentUser = await EnhancedAuthService.getCurrentAdminUser();
      return currentUser?.name ?? 'المستخدم';
    } catch (e) {
      debugPrint('❌ Error getting current user name: $e');
      return 'المستخدم';
    }
  }
}