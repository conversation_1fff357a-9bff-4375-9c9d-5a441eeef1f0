import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/lab_test_model.dart';

class LabTestsRepository {
  // Get all lab tests for a patient
  Future<List<LabTestModel>> getLabTestsByPatientId(String patientId) async {
    try {
      debugPrint('🔍 LabTestsRepository: Loading lab tests for patient: $patientId');
      final response = await SupabaseConfig.labTests
          .select()
          .eq('patient_id', patientId)
          .order('test_date', ascending: false);

      debugPrint('📊 LabTestsRepository: Raw response: $response');
      debugPrint('📊 LabTestsRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ LabTestsRepository: No lab tests found for patient: $patientId');
        return [];
      }

      final labTests = response.map((json) => LabTestModel.fromJson(json)).toList();
      debugPrint('✅ LabTestsRepository: Successfully parsed ${labTests.length} lab tests');

      return labTests;
    } catch (e, stackTrace) {
      debugPrint('❌ LabTestsRepository: Error loading lab tests: $e');
      debugPrint('📍 LabTestsRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب الفحوصات المخبرية: ${e.toString()}');
    }
  }

  // Get lab test by ID
  Future<LabTestModel?> getLabTestById(String labTestId) async {
    try {
      debugPrint('🔍 LabTestsRepository: Loading lab test: $labTestId');
      final response = await SupabaseConfig.labTests
          .select()
          .eq('id', labTestId)
          .single();

      final labTest = LabTestModel.fromJson(response);
      debugPrint('✅ LabTestsRepository: Successfully loaded lab test: ${labTest.testName}');

      return labTest;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error loading lab test: $e');
      return null;
    }
  }

  // Add new lab test
  Future<LabTestModel> addLabTest(LabTestModel labTest) async {
    try {
      debugPrint('🔍 LabTestsRepository: Adding new lab test: ${labTest.testName}');

      final response = await SupabaseConfig.labTests
          .insert(labTest.toJson())
          .select()
          .single();

      final newLabTest = LabTestModel.fromJson(response);
      debugPrint('✅ LabTestsRepository: Successfully added lab test: ${newLabTest.id}');

      return newLabTest;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error adding lab test: $e');
      throw Exception('فشل في إضافة الفحص المخبري: ${e.toString()}');
    }
  }

  // Update lab test
  Future<LabTestModel> updateLabTest(LabTestModel labTest) async {
    try {
      debugPrint('🔍 LabTestsRepository: Updating lab test: ${labTest.id}');

      final response = await SupabaseConfig.labTests
          .update(labTest.toJson())
          .eq('id', labTest.id)
          .select()
          .single();

      final updatedLabTest = LabTestModel.fromJson(response);
      debugPrint('✅ LabTestsRepository: Successfully updated lab test: ${updatedLabTest.id}');

      return updatedLabTest;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error updating lab test: $e');
      throw Exception('فشل في تحديث الفحص المخبري: ${e.toString()}');
    }
  }

  // Delete lab test
  Future<void> deleteLabTest(String labTestId) async {
    try {
      debugPrint('🔍 LabTestsRepository: Deleting lab test: $labTestId');

      // First get the lab test to check if it has an image
      final labTest = await getLabTestById(labTestId);

      // Delete the image from storage if it exists
      if (labTest?.hasImage == true) {
        await deleteLabTestImage(labTest!.imageUrl!);
      }

      // Delete the lab test record
      await SupabaseConfig.labTests
          .delete()
          .eq('id', labTestId);

      debugPrint('✅ LabTestsRepository: Successfully deleted lab test: $labTestId');
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error deleting lab test: $e');
      throw Exception('فشل في حذف الفحص المخبري: ${e.toString()}');
    }
  }

  // Upload lab test image
  Future<String> uploadLabTestImage(XFile imageFile, String patientId) async {
    try {
      debugPrint('🔍 LabTestsRepository: Uploading lab test image for patient: $patientId');

      final fileName = '${patientId}_${DateTime.now().millisecondsSinceEpoch}.${imageFile.path.split('.').last}';
      final filePath = 'lab_tests/$fileName';

      final file = File(imageFile.path);
      await SupabaseConfig.labTestImagesBucket
          .upload(filePath, file);

      // Get the public URL
      final imageUrl = SupabaseConfig.labTestImagesBucket.getPublicUrl(filePath);

      debugPrint('✅ LabTestsRepository: Successfully uploaded image: $imageUrl');
      return imageUrl;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error uploading image: $e');
      throw Exception('فشل في رفع صورة الفحص: ${e.toString()}');
    }
  }

  // Delete lab test image
  Future<void> deleteLabTestImage(String imageUrl) async {
    try {
      debugPrint('🔍 LabTestsRepository: Deleting lab test image: $imageUrl');

      // Extract file path from URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      final filePath = pathSegments.sublist(pathSegments.indexOf('lab_tests')).join('/');

      await SupabaseConfig.labTestImagesBucket.remove([filePath]);

      debugPrint('✅ LabTestsRepository: Successfully deleted image: $filePath');
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error deleting image: $e');
      // Don't throw error for image deletion failure
    }
  }

  // Search lab tests
  Future<List<LabTestModel>> searchLabTests(String patientId, String query) async {
    try {
      debugPrint('🔍 LabTestsRepository: Searching lab tests for patient: $patientId with query: $query');

      final response = await SupabaseConfig.labTests
          .select()
          .eq('patient_id', patientId)
          .or('test_name.ilike.%$query%,test_type.ilike.%$query%,results.ilike.%$query%')
          .order('test_date', ascending: false);

      final labTests = response.map((json) => LabTestModel.fromJson(json)).toList();
      debugPrint('✅ LabTestsRepository: Found ${labTests.length} lab tests matching query');

      return labTests;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error searching lab tests: $e');
      throw Exception('فشل في البحث عن الفحوصات: ${e.toString()}');
    }
  }

  // Get lab tests by type
  Future<List<LabTestModel>> getLabTestsByType(String patientId, String testType) async {
    try {
      debugPrint('🔍 LabTestsRepository: Loading lab tests by type: $testType for patient: $patientId');

      final response = await SupabaseConfig.labTests
          .select()
          .eq('patient_id', patientId)
          .eq('test_type', testType)
          .order('test_date', ascending: false);

      final labTests = response.map((json) => LabTestModel.fromJson(json)).toList();
      debugPrint('✅ LabTestsRepository: Found ${labTests.length} lab tests of type: $testType');

      return labTests;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error loading lab tests by type: $e');
      throw Exception('فشل في جلب الفحوصات حسب النوع: ${e.toString()}');
    }
  }

  // Get lab tests by date range
  Future<List<LabTestModel>> getLabTestsByDateRange(
    String patientId,
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      debugPrint('🔍 LabTestsRepository: Loading lab tests by date range for patient: $patientId');

      final response = await SupabaseConfig.labTests
          .select()
          .eq('patient_id', patientId)
          .gte('test_date', startDate.toIso8601String().split('T')[0])
          .lte('test_date', endDate.toIso8601String().split('T')[0])
          .order('test_date', ascending: false);

      final labTests = response.map((json) => LabTestModel.fromJson(json)).toList();
      debugPrint('✅ LabTestsRepository: Found ${labTests.length} lab tests in date range');

      return labTests;
    } catch (e) {
      debugPrint('❌ LabTestsRepository: Error loading lab tests by date range: $e');
      throw Exception('فشل في جلب الفحوصات حسب التاريخ: ${e.toString()}');
    }
  }
}
