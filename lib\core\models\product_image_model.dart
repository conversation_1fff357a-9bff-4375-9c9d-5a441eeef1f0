import 'package:equatable/equatable.dart';

class ProductImageModel extends Equatable {
  final String id;
  final String? productId;
  final String imageUrl;
  final String? imagePath;
  final bool isPrimary;
  final int sortOrder;
  final DateTime createdAt;

  const ProductImageModel({
    required this.id,
    this.productId,
    required this.imageUrl,
    this.imagePath,
    this.isPrimary = false,
    this.sortOrder = 0,
    required this.createdAt,
  });

  factory ProductImageModel.fromJson(Map<String, dynamic> json) {
    return ProductImageModel(
      id: json['id']?.toString() ?? '',
      productId: json['product_id']?.toString(),
      imageUrl: json['image_url']?.toString() ?? '',
      imagePath: json['image_path']?.toString(),
      isPrimary: json['is_primary'] as bool? ?? false,
      sortOrder: json['sort_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at']?.toString() ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'image_url': imageUrl,
      'image_path': imagePath,
      'is_primary': isPrimary,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
    };
  }

  ProductImageModel copyWith({
    String? id,
    String? productId,
    String? imageUrl,
    String? imagePath,
    bool? isPrimary,
    int? sortOrder,
    DateTime? createdAt,
  }) {
    return ProductImageModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      imageUrl: imageUrl ?? this.imageUrl,
      imagePath: imagePath ?? this.imagePath,
      isPrimary: isPrimary ?? this.isPrimary,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        productId,
        imageUrl,
        imagePath,
        isPrimary,
        sortOrder,
        createdAt,
      ];
}
