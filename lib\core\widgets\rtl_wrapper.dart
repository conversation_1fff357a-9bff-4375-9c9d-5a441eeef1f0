import 'package:flutter/material.dart';
import '../constants/app_localization.dart';

class RTL<PERSON>rapper extends StatelessWidget {
  final Widget child;
  final TextDirection? textDirection;

  const RTLWrapper({
    super.key,
    required this.child,
    this.textDirection,
  });

  @override
  Widget build(BuildContext context) {
    final direction = textDirection ?? 
        AppLocalization.getTextDirection(AppLocalization.defaultLocale);
    
    return Directionality(
      textDirection: direction,
      child: child,
    );
  }
}

class RTLText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const RTLText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: style,
      textAlign: textAlign ?? TextAlign.right,
      maxLines: maxLines,
      overflow: overflow,
      textDirection: TextDirection.rtl,
    );
  }
}

class RTLPadding extends StatelessWidget {
  final Widget child;
  final double? start;
  final double? top;
  final double? end;
  final double? bottom;
  final double? all;
  final double? horizontal;
  final double? vertical;

  const RTLPadding({
    super.key,
    required this.child,
    this.start,
    this.top,
    this.end,
    this.bottom,
    this.all,
    this.horizontal,
    this.vertical,
  });

  @override
  Widget build(BuildContext context) {
    final direction = Directionality.of(context);
    
    EdgeInsets padding;
    
    if (all != null) {
      padding = EdgeInsets.all(all!);
    } else if (horizontal != null || vertical != null) {
      padding = EdgeInsets.symmetric(
        horizontal: horizontal ?? 0,
        vertical: vertical ?? 0,
      );
    } else {
      padding = AppLocalization.getEdgeInsets(
        start: start ?? 0,
        top: top ?? 0,
        end: end ?? 0,
        bottom: bottom ?? 0,
        direction: direction,
      );
    }
    
    return Padding(
      padding: padding,
      child: child,
    );
  }
}

class RTLRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const RTLRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    final direction = Directionality.of(context);
    
    // Reverse children for RTL
    final reversedChildren = direction == TextDirection.rtl 
        ? children.reversed.toList() 
        : children;
    
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: reversedChildren,
    );
  }
}

class RTLContainer extends StatelessWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;

  const RTLContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.decoration,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    final direction = Directionality.of(context);
    
    AlignmentGeometry? rtlAlignment = alignment;
    if (alignment != null && direction == TextDirection.rtl) {
      if (alignment == Alignment.centerLeft) {
        rtlAlignment = Alignment.centerRight;
      } else if (alignment == Alignment.centerRight) {
        rtlAlignment = Alignment.centerLeft;
      } else if (alignment == Alignment.topLeft) {
        rtlAlignment = Alignment.topRight;
      } else if (alignment == Alignment.topRight) {
        rtlAlignment = Alignment.topLeft;
      } else if (alignment == Alignment.bottomLeft) {
        rtlAlignment = Alignment.bottomRight;
      } else if (alignment == Alignment.bottomRight) {
        rtlAlignment = Alignment.bottomLeft;
      }
    }
    
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: decoration,
      alignment: rtlAlignment,
      child: child,
    );
  }
}

class RTLListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;

  const RTLListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final direction = Directionality.of(context);
    
    return ListTile(
      leading: direction == TextDirection.rtl ? trailing : leading,
      title: title,
      subtitle: subtitle,
      trailing: direction == TextDirection.rtl ? leading : trailing,
      onTap: onTap,
      contentPadding: contentPadding,
    );
  }
}
