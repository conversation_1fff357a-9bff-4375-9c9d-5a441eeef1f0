import 'package:equatable/equatable.dart';
import '../../../../core/models/reminder_model.dart';

abstract class RemindersState extends Equatable {
  const RemindersState();

  @override
  List<Object?> get props => [];
}

class RemindersInitial extends RemindersState {
  const RemindersInitial();
}

class RemindersLoading extends RemindersState {
  const RemindersLoading();
}

class RemindersLoaded extends RemindersState {
  final List<ReminderModel> reminders;
  final String patientId;
  final Map<String, List<ReminderModel>> groupedByType;

  const RemindersLoaded({
    required this.reminders,
    required this.patientId,
    required this.groupedByType,
  });

  RemindersLoaded copyWith({
    List<ReminderModel>? reminders,
    String? patientId,
    Map<String, List<ReminderModel>>? groupedByType,
  }) {
    return RemindersLoaded(
      reminders: reminders ?? this.reminders,
      patientId: patientId ?? this.patientId,
      groupedByType: groupedByType ?? this.groupedByType,
    );
  }

  @override
  List<Object?> get props => [reminders, patientId, groupedByType];
}

class RemindersByTypeLoaded extends RemindersState {
  final List<ReminderModel> reminders;
  final String patientId;
  final String reminderType;

  const RemindersByTypeLoaded({
    required this.reminders,
    required this.patientId,
    required this.reminderType,
  });

  @override
  List<Object?> get props => [reminders, patientId, reminderType];
}

class ActiveRemindersLoaded extends RemindersState {
  final List<ReminderModel> activeReminders;
  final String patientId;

  const ActiveRemindersLoaded({
    required this.activeReminders,
    required this.patientId,
  });

  @override
  List<Object?> get props => [activeReminders, patientId];
}

class ReminderCreated extends RemindersState {
  final ReminderModel reminder;

  const ReminderCreated({required this.reminder});

  @override
  List<Object?> get props => [reminder];
}

class ReminderUpdated extends RemindersState {
  final ReminderModel reminder;

  const ReminderUpdated({required this.reminder});

  @override
  List<Object?> get props => [reminder];
}

class ReminderDeleted extends RemindersState {
  final String reminderId;

  const ReminderDeleted({required this.reminderId});

  @override
  List<Object?> get props => [reminderId];
}

class ReminderStatusToggled extends RemindersState {
  final String reminderId;
  final bool isActive;

  const ReminderStatusToggled({
    required this.reminderId,
    required this.isActive,
  });

  @override
  List<Object?> get props => [reminderId, isActive];
}

class RemindersError extends RemindersState {
  final String message;

  const RemindersError({required this.message});

  @override
  List<Object?> get props => [message];
}
