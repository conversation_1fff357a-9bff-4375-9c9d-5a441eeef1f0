import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/holiday_model.dart';

class AppointmentBookingRepository {
  /// Get available time slots for a specific date
  /// This method handles the complex logic of:
  /// 1. Getting day of week from date
  /// 2. Finding time slots for that day
  /// 3. Checking for existing bookings
  /// 4. Checking for holidays
  /// 5. Returning only available slots
  Future<AppointmentBookingResult> getAvailableTimeSlotsForDate(DateTime date) async {
    try {
      debugPrint('🔍 AppointmentBookingRepository: Getting available slots for date: ${date.toIso8601String().split('T')[0]}');

      // Step 1: Check if the date is a holiday
      final holiday = await _checkHolidayForDate(date);
      if (holiday != null) {
        debugPrint('🏖️ AppointmentBookingRepository: Date is a holiday: ${holiday.occasionName}');
        return AppointmentBookingResult(
          date: date,
          dayOfWeek: date.weekday % 7, // Convert to 0=Sunday format
          dayName: _getDayName(date.weekday % 7),
          isHoliday: true,
          holiday: holiday,
          availableSlots: [],
          bookedSlots: [],
        );
      }

      // Step 2: Get day of week (0=Sunday, 1=Monday, ..., 6=Saturday)
      final dayOfWeek = date.weekday % 7;
      debugPrint('📅 AppointmentBookingRepository: Day of week: $dayOfWeek (${_getDayName(dayOfWeek)})');

      // Step 3: Get all active time slots for this day of week
      final allTimeSlots = await _getTimeSlotsForDay(dayOfWeek);
      debugPrint('⏰ AppointmentBookingRepository: Found ${allTimeSlots.length} time slots for ${_getDayName(dayOfWeek)}');

      if (allTimeSlots.isEmpty) {
        debugPrint('⚠️ AppointmentBookingRepository: No time slots configured for ${_getDayName(dayOfWeek)}');
        return AppointmentBookingResult(
          date: date,
          dayOfWeek: dayOfWeek,
          dayName: _getDayName(dayOfWeek),
          isHoliday: false,
          availableSlots: [],
          bookedSlots: [],
        );
      }

      // Step 4: Get existing appointments for this date
      final existingAppointments = await _getAppointmentsForDate(date);
      debugPrint('📋 AppointmentBookingRepository: Found ${existingAppointments.length} existing appointments');

      // Step 5: Filter available slots
      final availableSlots = <TimeSlotModel>[];
      final bookedSlots = <TimeSlotWithAppointment>[];

      for (final timeSlot in allTimeSlots) {
        // Check if this time slot is already booked
        final bookedAppointment = existingAppointments.firstWhere(
          (appointment) => appointment.timeSlotId == timeSlot.id && appointment.isBooked,
          orElse: () => AppointmentModel(
            id: '',
            appointmentDate: date,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        if (bookedAppointment.id.isNotEmpty) {
          // Slot is booked
          bookedSlots.add(TimeSlotWithAppointment(
            timeSlot: timeSlot,
            appointment: bookedAppointment,
          ));
          debugPrint('🔒 AppointmentBookingRepository: Slot ${timeSlot.startTime}-${timeSlot.endTime} is booked');
        } else {
          // Slot is available
          availableSlots.add(timeSlot);
          debugPrint('✅ AppointmentBookingRepository: Slot ${timeSlot.startTime}-${timeSlot.endTime} is available');
        }
      }

      debugPrint('📊 AppointmentBookingRepository: Result - Available: ${availableSlots.length}, Booked: ${bookedSlots.length}');

      return AppointmentBookingResult(
        date: date,
        dayOfWeek: dayOfWeek,
        dayName: _getDayName(dayOfWeek),
        isHoliday: false,
        availableSlots: availableSlots,
        bookedSlots: bookedSlots,
      );

    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentBookingRepository: Error getting available slots: $e');
      debugPrint('📍 AppointmentBookingRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب المواعيد المتاحة: ${e.toString()}');
    }
  }

  /// Get patient's upcoming appointments with time slot details
  Future<List<PatientAppointmentWithDetails>> getPatientUpcomingAppointments(String patientId) async {
    try {
      debugPrint('🔍 AppointmentBookingRepository: Getting upcoming appointments for patient: $patientId');

      final today = DateTime.now();
      final todayString = today.toIso8601String().split('T')[0];

      // Get appointments from today onwards
      final response = await SupabaseConfig.appointments
          .select('''
            *,
            time_slots!inner(*)
          ''')
          .eq('patient_id', patientId)
          .gte('appointment_date', todayString)
          .order('appointment_date', ascending: true);

      final appointmentsWithDetails = <PatientAppointmentWithDetails>[];

      for (final item in response) {
        final appointment = AppointmentModel.fromJson(item);
        final timeSlotData = item['time_slots'] as Map<String, dynamic>;
        final timeSlot = TimeSlotModel.fromJson(timeSlotData);

        appointmentsWithDetails.add(PatientAppointmentWithDetails(
          appointment: appointment,
          timeSlot: timeSlot,
        ));
      }

      debugPrint('✅ AppointmentBookingRepository: Found ${appointmentsWithDetails.length} upcoming appointments');
      return appointmentsWithDetails;

    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentBookingRepository: Error getting patient appointments: $e');
      debugPrint('📍 AppointmentBookingRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب مواعيد المريض: ${e.toString()}');
    }
  }

  /// Get patient's past appointments with time slot details
  Future<List<PatientAppointmentWithDetails>> getPatientPastAppointments(String patientId) async {
    try {
      debugPrint('🔍 AppointmentBookingRepository: Getting past appointments for patient: $patientId');

      final today = DateTime.now();
      final todayString = today.toIso8601String().split('T')[0];

      debugPrint('📅 AppointmentBookingRepository: Today string: $todayString');
      debugPrint('👤 AppointmentBookingRepository: Patient ID: $patientId');

      // Get appointments before today - using left join to include appointments even without time slots
      final response = await SupabaseConfig.appointments
          .select('''
            *,
            time_slots(*)
          ''')
          .eq('patient_id', patientId)
          .lt('appointment_date', todayString)
          .order('appointment_date', ascending: false); // الأحدث أولاً

      debugPrint('📊 AppointmentBookingRepository: Raw response: $response');
      debugPrint('📊 AppointmentBookingRepository: Response length: ${response.length}');

      final appointmentsWithDetails = <PatientAppointmentWithDetails>[];

      for (final item in response) {
        debugPrint('🔄 AppointmentBookingRepository: Processing item: $item');

        final appointment = AppointmentModel.fromJson(item);

        // Handle case where time_slots might be null
        final timeSlotData = item['time_slots'] as Map<String, dynamic>?;
        if (timeSlotData != null) {
          final timeSlot = TimeSlotModel.fromJson(timeSlotData);
          appointmentsWithDetails.add(PatientAppointmentWithDetails(
            appointment: appointment,
            timeSlot: timeSlot,
          ));
          debugPrint('✅ AppointmentBookingRepository: Added appointment ${appointment.id} with time slot');
        } else {
          debugPrint('⚠️ AppointmentBookingRepository: Appointment ${appointment.id} has no time slot');
        }
      }

      debugPrint('✅ AppointmentBookingRepository: Found ${appointmentsWithDetails.length} past appointments');
      return appointmentsWithDetails;

    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentBookingRepository: Error getting past appointments: $e');
      debugPrint('📍 AppointmentBookingRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب المواعيد السابقة: ${e.toString()}');
    }
  }

  // Private helper methods

  Future<HolidayModel?> _checkHolidayForDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      final response = await SupabaseConfig.holidays
          .select()
          .eq('holiday_date', dateString)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;
      return HolidayModel.fromJson(response);
    } catch (e) {
      debugPrint('❌ AppointmentBookingRepository: Error checking holiday: $e');
      return null;
    }
  }

  Future<List<TimeSlotModel>> _getTimeSlotsForDay(int dayOfWeek) async {
    try {
      final response = await SupabaseConfig.timeSlots
          .select()
          .eq('day_of_week', dayOfWeek)
          .eq('is_active', true)
          .order('start_time');

      return response.map((json) => TimeSlotModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('❌ AppointmentBookingRepository: Error getting time slots: $e');
      return [];
    }
  }

  Future<List<AppointmentModel>> _getAppointmentsForDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      final response = await SupabaseConfig.appointments
          .select()
          .eq('appointment_date', dateString);

      return response.map((json) => AppointmentModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('❌ AppointmentBookingRepository: Error getting appointments: $e');
      return [];
    }
  }

  String _getDayName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 0: return 'الأحد';
      case 1: return 'الإثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      default: return 'غير محدد';
    }
  }
}

// Data classes for complex results

class AppointmentBookingResult {
  final DateTime date;
  final int dayOfWeek;
  final String dayName;
  final bool isHoliday;
  final HolidayModel? holiday;
  final List<TimeSlotModel> availableSlots;
  final List<TimeSlotWithAppointment> bookedSlots;

  const AppointmentBookingResult({
    required this.date,
    required this.dayOfWeek,
    required this.dayName,
    required this.isHoliday,
    this.holiday,
    required this.availableSlots,
    required this.bookedSlots,
  });
}

class TimeSlotWithAppointment {
  final TimeSlotModel timeSlot;
  final AppointmentModel appointment;

  const TimeSlotWithAppointment({
    required this.timeSlot,
    required this.appointment,
  });
}

class PatientAppointmentWithDetails {
  final AppointmentModel appointment;
  final TimeSlotModel timeSlot;

  const PatientAppointmentWithDetails({
    required this.appointment,
    required this.timeSlot,
  });
}
