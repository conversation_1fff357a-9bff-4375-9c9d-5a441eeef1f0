import '../utils/logger.dart';
import 'notification_manager.dart';
import '../network/supabase_client.dart';

class AppointmentNotificationService {
  static final AppointmentNotificationService _instance = AppointmentNotificationService._internal();
  factory AppointmentNotificationService() => _instance;
  AppointmentNotificationService._internal();

  final Logger _logger = Logger('AppointmentNotificationService');
  final NotificationManager _notificationManager = NotificationManager();

  // Send notification when appointment is cancelled
  Future<void> notifyAppointmentCancelled({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
  }) async {
    try {
      _logger.info('📅 Sending appointment cancelled notification for: $appointmentId');
      
      await _notificationManager.notifyAppointmentCancelled(
        appointmentId: appointmentId,
        patientUserId: patientUserId,
        patientName: patientName,
        doctorName: doctorName,
        appointmentDate: appointmentDate,
      );
      
      // Log the notification in database
      await _logNotification(
        userId: patientUserId,
        type: 'appointment_cancelled',
        title: 'تم إلغاء الحجز',
        message: 'تم إلغاء حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}',
        relatedId: appointmentId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send appointment cancelled notification: $e');
    }
  }

  // Send notification when appointment is confirmed
  Future<void> notifyAppointmentConfirmed({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
  }) async {
    try {
      _logger.info('📅 Sending appointment confirmed notification for: $appointmentId');
      
      await _notificationManager.notifyAppointmentConfirmed(
        appointmentId: appointmentId,
        patientUserId: patientUserId,
        patientName: patientName,
        doctorName: doctorName,
        appointmentDate: appointmentDate,
      );
      
      // Log the notification in database
      await _logNotification(
        userId: patientUserId,
        type: 'appointment_confirmed',
        title: 'تم تأكيد الحجز',
        message: 'تم تأكيد حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}',
        relatedId: appointmentId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send appointment confirmed notification: $e');
    }
  }

  // Send notification when appointment is rescheduled
  Future<void> notifyAppointmentRescheduled({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime oldDate,
    required DateTime newDate,
  }) async {
    try {
      _logger.info('📅 Sending appointment rescheduled notification for: $appointmentId');
      
      await _notificationManager.notifyAppointmentRescheduled(
        appointmentId: appointmentId,
        patientUserId: patientUserId,
        patientName: patientName,
        doctorName: doctorName,
        oldDate: oldDate,
        newDate: newDate,
      );
      
      // Log the notification in database
      await _logNotification(
        userId: patientUserId,
        type: 'appointment_rescheduled',
        title: 'تم تغيير موعد الحجز',
        message: 'تم تغيير موعد حجزك مع د. $doctorName من ${_formatDate(oldDate)} إلى ${_formatDate(newDate)}',
        relatedId: appointmentId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send appointment rescheduled notification: $e');
    }
  }

  // Send notification when appointment status changes
  Future<void> notifyAppointmentStatusChanged({
    required String appointmentId,
    required String patientUserId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
    required String newStatus,
    required String statusDisplayName,
  }) async {
    try {
      _logger.info('📅 Sending appointment status changed notification for: $appointmentId');
      
      String title, message;
      switch (newStatus) {
        case 'completed':
          title = 'تم إنجاز الحجز';
          message = 'تم إنجاز حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}';
          break;
        case 'in_progress':
          title = 'بدء الحجز';
          message = 'بدأ حجزك مع د. $doctorName الآن';
          break;
        case 'no_show':
          title = 'عدم حضور';
          message = 'لم تحضر لحجزك مع د. $doctorName في ${_formatDate(appointmentDate)}';
          break;
        default:
          title = 'تحديث حالة الحجز';
          message = 'تم تحديث حالة حجزك مع ��. $doctorName إلى $statusDisplayName';
      }

      await _notificationManager.sendCustomNotification(
        userId: patientUserId,
        title: title,
        body: message,
        type: 'appointment',
        data: {
          'appointment_id': appointmentId,
          'action': 'status_changed',
          'new_status': newStatus,
          'patient_name': patientName,
          'doctor_name': doctorName,
        },
      );
      
      // Log the notification in database
      await _logNotification(
        userId: patientUserId,
        type: 'appointment_status_changed',
        title: title,
        message: message,
        relatedId: appointmentId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send appointment status changed notification: $e');
    }
  }

  // Send notification to specialist when appointment status changes to cancelled or no_show
  Future<void> notifySpecialistAppointmentStatusChanged({
    required String appointmentId,
    required String specialistUserId,
    required String specialistName,
    required String patientName,
    required DateTime appointmentDate,
    required String newStatus,
    required String statusDisplayName,
  }) async {
    try {
      _logger.info('👨‍⚕️ Sending specialist notification for appointment: $appointmentId');
      
      String title, message;
      switch (newStatus) {
        case 'cancelled':
          title = 'تم إلغاء حجز';
          message = 'تم إلغاء حجز المريض $patientName المقرر في ${_formatDate(appointmentDate)}';
          break;
        case 'no_show':
          title = 'عدم حضور مريض';
          message = 'لم يحضر المريض $patientName لحجزه المقرر في ${_formatDate(appointmentDate)}';
          break;
        default:
          title = 'تحديث حالة حجز';
          message = 'تم تحديث حالة حجز المريض $patientName إلى $statusDisplayName';
      }

      await _notificationManager.sendCustomNotification(
        userId: specialistUserId,
        title: title,
        body: message,
        type: 'appointment_specialist',
        data: {
          'appointment_id': appointmentId,
          'action': 'status_changed_specialist',
          'new_status': newStatus,
          'patient_name': patientName,
          'specialist_name': specialistName,
        },
      );
      
      // Log the notification in database
      await _logNotification(
        userId: specialistUserId,
        type: 'appointment_specialist_status_changed',
        title: title,
        message: message,
        relatedId: appointmentId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send specialist appointment status changed notification: $e');
    }
  }

  // Log notification in database for history
  Future<void> _logNotification({
    required String userId,
    required String type,
    required String title,
    required String message,
    String? relatedId,
  }) async {
    try {
      await SupabaseConfig.client
          .from('notifications_log')
          .insert({
            'user_id': userId,
            'type': type,
            'title': title,
            'message': message,
            'related_id': relatedId,
            'sent_at': DateTime.now().toIso8601String(),
            'is_read': false,
          });
    } catch (e) {
      _logger.error('❌ Failed to log notification: $e');
    }
  }

  // Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}