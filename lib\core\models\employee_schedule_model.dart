import 'package:equatable/equatable.dart';

class EmployeeScheduleModel extends Equatable {
  final String id;
  final String employeeId;
  final int dayOfWeek; // 0=Sunday, 6=Saturday
  final String startTime; // HH:mm format
  final String endTime; // HH:mm format
  final int sessionDuration; // Duration in minutes
  final int breakDuration; // Break between sessions in minutes
  final int maxPatientsPerSession;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeScheduleModel({
    required this.id,
    required this.employeeId,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    this.sessionDuration = 30,
    this.breakDuration = 15,
    this.maxPatientsPerSession = 1,
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeScheduleModel.fromJson(Map<String, dynamic> json) {
    return EmployeeScheduleModel(
      id: json['id'] as String,
      employeeId: json['employee_id'] as String,
      dayOfWeek: json['day_of_week'] as int,
      startTime: json['start_time'] as String,
      endTime: json['end_time'] as String,
      sessionDuration: json['session_duration'] as int? ?? 30,
      breakDuration: json['break_duration'] as int? ?? 15,
      maxPatientsPerSession: json['max_patients_per_session'] as int? ?? 1,
      isActive: json['is_active'] as bool? ?? true,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employee_id': employeeId,
      'day_of_week': dayOfWeek,
      'start_time': startTime,
      'end_time': endTime,
      'session_duration': sessionDuration,
      'break_duration': breakDuration,
      'max_patients_per_session': maxPatientsPerSession,
      'is_active': isActive,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EmployeeScheduleModel copyWith({
    String? id,
    String? employeeId,
    int? dayOfWeek,
    String? startTime,
    String? endTime,
    int? sessionDuration,
    int? breakDuration,
    int? maxPatientsPerSession,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeScheduleModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      sessionDuration: sessionDuration ?? this.sessionDuration,
      breakDuration: breakDuration ?? this.breakDuration,
      maxPatientsPerSession: maxPatientsPerSession ?? this.maxPatientsPerSession,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  String get dayName {
    const days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    return days[dayOfWeek];
  }

  String get dayNameEn {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  }

  String get timeRange => '$startTime - $endTime';

  Duration get workDuration {
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);
    return end.difference(start);
  }

  int get totalSessions {
    final workMinutes = workDuration.inMinutes;
    final sessionWithBreak = sessionDuration + breakDuration;
    return (workMinutes / sessionWithBreak).floor();
  }

  DateTime _parseTime(String time) {
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }

  @override
  List<Object?> get props => [
        id,
        employeeId,
        dayOfWeek,
        startTime,
        endTime,
        sessionDuration,
        breakDuration,
        maxPatientsPerSession,
        isActive,
        notes,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'EmployeeScheduleModel(id: $id, employeeId: $employeeId, dayOfWeek: $dayOfWeek, timeRange: $timeRange)';
  }
}
