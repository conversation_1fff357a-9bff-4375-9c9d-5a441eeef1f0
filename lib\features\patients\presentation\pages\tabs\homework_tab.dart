import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/homework_model.dart';
import '../../../../../core/utils/image_compression.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:developer' as developer;

class HomeworkTab extends StatefulWidget {
  final String patientId;

  const HomeworkTab({
    super.key,
    required this.patientId,
  });

  @override
  State<HomeworkTab> createState() => _HomeworkTabState();
}

class _HomeworkTabState extends State<HomeworkTab> {
  List<HomeworkModel> _homework = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadHomework();
  }

  Future<void> _loadHomework() async {
    setState(() => _isLoading = true);
    try {
      final response = await Supabase.instance.client
          .from('homework')
          .select()
          .eq('patient_id', widget.patientId)
          .order('assigned_date', ascending: false);

      final homework = (response as List)
          .map((json) => HomeworkModel.fromJson(json))
          .toList();

      setState(() {
        _homework = homework;
      });
    } catch (e) {
      developer.log('Error loading homework: $e', name: 'HomeworkTab');
      if (mounted) {
        _showErrorMessage('خطأ في تحميل الواجبات');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(Icons.assignment, color: AppColors.primary, size: 24),
                SizedBox(width: 12.w),
                Text(
                  'الواجبات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _showAddHomeworkDialog(),
                  icon: const Icon(Icons.add, color: AppColors.white),
                  label: const Text('إضافة واجب', style: TextStyle(color: AppColors.white)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _homework.isEmpty
                    ? _buildEmptyState()
                    : _buildHomeworkList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد واجبات مسجلة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'انقر على "إضافة واجب" لإضافة واجب جديد',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHomeworkList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _homework.length,
      itemBuilder: (context, index) {
        final homework = _homework[index];
        return _buildHomeworkCard(homework);
      },
    );
  }

  Widget _buildHomeworkCard(HomeworkModel homework) {
    final statusColor = _getStatusColor(homework.status);
    
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.transparent,
          width: 0,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and ID
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.assignment,
                  color: statusColor,
                  size: 20,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      homework.title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    // ID Badge
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        'ID: ${homework.id.substring(0, 8)}',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Status and Action Buttons Row
          Row(
            children: [
              // Status Badge
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8.w,
                      height: 8.h,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      homework.status.arabicName,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: statusColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Action Buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Status Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: IconButton(
                      onPressed: () => _showStatusDialog(homework),
                      icon: Icon(Icons.update, color: AppColors.warning, size: 16),
                      tooltip: 'تغيير الحالة',
                      padding: EdgeInsets.all(6.w),
                      constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  // Edit Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.info.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: IconButton(
                      onPressed: () => _showEditHomeworkDialog(homework),
                      icon: Icon(Icons.edit, color: AppColors.info, size: 16),
                      tooltip: 'تعديل الواجب',
                      padding: EdgeInsets.all(6.w),
                      constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  // Delete Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: IconButton(
                      onPressed: () => _showDeleteConfirmation(homework),
                      icon: Icon(Icons.delete, color: AppColors.error, size: 16),
                      tooltip: 'حذف الواجب',
                      padding: EdgeInsets.all(6.w),
                      constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
                    ),
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Dates
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: AppColors.textSecondary),
              SizedBox(width: 8.w),
              Text(
                'تاريخ التكليف: ${homework.assignedDate.day}/${homework.assignedDate.month}/${homework.assignedDate.year}',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),



          // Description
          if (homework.description != null && homework.description!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Text(
              homework.description!,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimary,
              ),
            ),
          ],

          // Instructions
          if (homework.instructions != null && homework.instructions!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, size: 16, color: AppColors.info),
                      SizedBox(width: 8.w),
                      Text(
                        'تعليمات:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.info,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    homework.instructions!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // PDF File
          if (homework.pdfUrl != null && homework.pdfUrl!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            InkWell(
              onTap: () {
                // Open PDF - you can implement PDF viewer here
                _showErrorMessage('سيتم إضافة عارض PDF قريباً');
              },
              child: Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppColors.gray50,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: AppColors.gray200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: AppColors.error, size: 24),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ملف PDF مرفق',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          Text(
                            'انقر لفتح الملف',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.open_in_new, color: AppColors.primary, size: 20),
                  ],
                ),
              ),
            ),
          ],

          // Notes
          if (homework.notes != null && homework.notes!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.gray50,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملاحظات:',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    homework.notes!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(HomeworkStatus status) {
    switch (status) {
      case HomeworkStatus.assigned:
        return AppColors.info;
      case HomeworkStatus.inProgress:
        return AppColors.warning;
      case HomeworkStatus.completed:
        return AppColors.success;
    }
  }

  void _showAddHomeworkDialog() {
    _showHomeworkDialog();
  }

  void _showEditHomeworkDialog(HomeworkModel homework) {
    _showHomeworkDialog(homework: homework);
  }

  void _showHomeworkDialog({HomeworkModel? homework}) {
    final isEditing = homework != null;
    final titleController = TextEditingController(text: homework?.title ?? '');
    final descriptionController = TextEditingController(text: homework?.description ?? '');
    final instructionsController = TextEditingController(text: homework?.instructions ?? '');
    final notesController = TextEditingController(text: homework?.notes ?? '');

    DateTime assignedDate = homework?.assignedDate ?? DateTime.now();
    File? selectedPdf;
    String? existingPdfUrl = homework?.pdfUrl;
    bool isUploading = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل الواجب' : 'إضافة واجب جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                TextFormField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'عنوان الواجب *',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),

                // Assigned Date
                InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: assignedDate,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (date != null) {
                      setDialogState(() {
                        assignedDate = date;
                      });
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today),
                        SizedBox(width: 12.w),
                        Text(
                          'تاريخ التكليف: ${assignedDate.day}/${assignedDate.month}/${assignedDate.year}',
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16.h),



                // Description
                TextFormField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'وصف الواجب',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),

                // Instructions
                TextFormField(
                  controller: instructionsController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'تعليمات التنفيذ',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),

                // PDF File
                if (existingPdfUrl != null || selectedPdf != null) ...[
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Column(
                      children: [
                        Icon(Icons.picture_as_pdf, color: AppColors.error, size: 48),
                        SizedBox(height: 8.h),
                        Text(
                          selectedPdf != null
                              ? 'ملف PDF جديد محدد'
                              : 'ملف PDF موجود',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 8.h),
                ],

                // PDF buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          final result = await FilePicker.platform.pickFiles(
                            type: FileType.custom,
                            allowedExtensions: ['pdf'],
                          );
                          if (result != null && result.files.single.path != null) {
                            setDialogState(() {
                              selectedPdf = File(result.files.single.path!);
                              existingPdfUrl = null;
                            });
                          }
                        },
                        icon: const Icon(Icons.upload_file),
                        label: const Text('اختيار PDF'),
                      ),
                    ),
                    if (selectedPdf != null || existingPdfUrl != null) ...[
                      SizedBox(width: 8.w),
                      OutlinedButton.icon(
                        onPressed: () {
                          setDialogState(() {
                            selectedPdf = null;
                            existingPdfUrl = null;
                          });
                        },
                        icon: const Icon(Icons.delete, color: AppColors.error),
                        label: const Text('حذف', style: TextStyle(color: AppColors.error)),
                      ),
                    ],
                  ],
                ),

                SizedBox(height: 16.h),

                // Notes
                TextFormField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: isUploading ? null : () async {
                if (titleController.text.trim().isEmpty) {
                  _showErrorMessage('يرجى إدخال عنوان الواجب');
                  return;
                }

                setDialogState(() => isUploading = true);

                try {
                  String? pdfUrl = existingPdfUrl;

                  // Upload PDF if selected
                  if (selectedPdf != null) {
                    try {
                      // Compress PDF (just get bytes for now)
                      final compressedPdf = await ImageCompression.compressPdf(selectedPdf!);

                      // Upload to Supabase Storage
                      final fileName = 'homework_${DateTime.now().millisecondsSinceEpoch}.pdf';
                      final uploadPath = 'homework/$fileName';

                      await Supabase.instance.client.storage
                          .from('medical-files')
                          .uploadBinary(uploadPath, compressedPdf);

                      pdfUrl = Supabase.instance.client.storage
                          .from('medical-files')
                          .getPublicUrl(uploadPath);
                    } catch (e) {
                      developer.log('Error uploading PDF: $e', name: 'HomeworkTab');
                      // Continue without PDF if upload fails
                      pdfUrl = null;
                    }
                  }

                  if (isEditing) {
                    // Update homework
                    await Supabase.instance.client
                        .from('homework')
                        .update({
                          'title': titleController.text.trim(),
                          'description': descriptionController.text.trim().isEmpty
                              ? null : descriptionController.text.trim(),
                          'instructions': instructionsController.text.trim().isEmpty
                              ? null : instructionsController.text.trim(),
                          'pdf_url': pdfUrl,
                          'assigned_date': assignedDate.toIso8601String().split('T')[0],
                          'notes': notesController.text.trim().isEmpty
                              ? null : notesController.text.trim(),
                          'updated_at': DateTime.now().toIso8601String(),
                        })
                        .eq('id', homework.id);
                  } else {
                    // Create new homework
                    await Supabase.instance.client
                        .from('homework')
                        .insert({
                          'patient_id': widget.patientId,
                          'title': titleController.text.trim(),
                          'description': descriptionController.text.trim().isEmpty
                              ? null : descriptionController.text.trim(),
                          'instructions': instructionsController.text.trim().isEmpty
                              ? null : instructionsController.text.trim(),
                          'pdf_url': pdfUrl,
                          'assigned_date': assignedDate.toIso8601String().split('T')[0],
                          'notes': notesController.text.trim().isEmpty
                              ? null : notesController.text.trim(),
                        });
                  }

                  if (mounted) {
                    Navigator.of(context).pop();
                    _showSuccessMessage(isEditing ? 'تم تحديث الواجب بنجاح' : 'تم إضافة الواجب بنجاح');
                    _loadHomework();
                  }
                } catch (e) {
                  developer.log('Error saving homework: $e', name: 'HomeworkTab');
                  _showErrorMessage('خطأ في حفظ الواجب');
                } finally {
                  setDialogState(() => isUploading = false);
                }
              },
              child: isUploading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showStatusDialog(HomeworkModel homework) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير حالة الواجب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: HomeworkStatus.values.map((status) {
            return RadioListTile<HomeworkStatus>(
              title: Text(status.arabicName),
              value: status,
              groupValue: homework.status,
              onChanged: (value) async {
                if (value != null) {
                  try {
                    await Supabase.instance.client
                        .from('homework')
                        .update({
                          'status': value.name,
                          'updated_at': DateTime.now().toIso8601String(),
                        })
                        .eq('id', homework.id);

                    if (mounted) {
                      Navigator.of(context).pop();
                      _showSuccessMessage('تم تحديث حالة الواجب بنجاح');
                      _loadHomework();
                    }
                  } catch (e) {
                    developer.log('Error updating homework status: $e', name: 'HomeworkTab');
                    _showErrorMessage('خطأ في تحديث حالة الواجب');
                  }
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(HomeworkModel homework) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف واجب "${homework.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // Delete PDF from storage if exists
                if (homework.pdfUrl != null && homework.pdfUrl!.isNotEmpty) {
                  await _deletePdfFromStorage(homework.pdfUrl!);
                }

                // Delete homework from database
                await Supabase.instance.client
                    .from('homework')
                    .delete()
                    .eq('id', homework.id);

                if (mounted) {
                  Navigator.of(context).pop();
                  _showSuccessMessage('تم حذف الواجب والملف بنجاح');
                  _loadHomework();
                }
              } catch (e) {
                developer.log('Error deleting homework: $e', name: 'HomeworkTab');
                _showErrorMessage('خطأ في حذف الواجب');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deletePdfFromStorage(String pdfUrl) async {
    try {
      // Extract file path from URL
      final uri = Uri.parse(pdfUrl);
      final pathSegments = uri.pathSegments;

      // Find the path after 'medical-files'
      int bucketIndex = pathSegments.indexOf('medical-files');
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        final filePath = pathSegments.sublist(bucketIndex + 1).join('/');

        await Supabase.instance.client.storage
            .from('medical-files')
            .remove([filePath]);

        developer.log('PDF deleted from storage: $filePath', name: 'HomeworkTab');
      }
    } catch (e) {
      developer.log('Error deleting PDF from storage: $e', name: 'HomeworkTab');
      // Don't throw error, just log it
    }
  }
}
