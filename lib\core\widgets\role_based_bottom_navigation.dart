import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../services/permissions_manager.dart';
import '../enums/user_role.dart';

class RoleBasedBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const RoleBasedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final permissionsManager = PermissionsManager();
    final userRole = permissionsManager.currentUserRole;

    if (userRole == null) {
      return const SizedBox.shrink();
    }

    final items = _getNavigationItems(userRole);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.white,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.gray500,
        selectedLabelStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 11.sp,
          fontWeight: FontWeight.w500,
        ),
        items: items,
      ),
    );
  }

  List<BottomNavigationBarItem> _getNavigationItems(UserRole userRole) {
    switch (userRole) {
      case UserRole.manager:
      case UserRole.admin:
      case UserRole.superAdmin:
        // الأدمن: جميع الصفحات
        return [
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today, size: 24.sp),
            activeIcon: Icon(Icons.calendar_today, size: 26.sp),
            label: 'الحجوزات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people, size: 24.sp),
            activeIcon: Icon(Icons.people, size: 26.sp),
            label: 'المرضى',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.point_of_sale, size: 24.sp),
            activeIcon: Icon(Icons.point_of_sale, size: 26.sp),
            label: 'المبيعات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory, size: 24.sp),
            activeIcon: Icon(Icons.inventory, size: 26.sp),
            label: 'المنتجات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.group, size: 24.sp),
            activeIcon: Icon(Icons.group, size: 26.sp),
            label: 'الموظفين',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics, size: 24.sp),
            activeIcon: Icon(Icons.analytics, size: 26.sp),
            label: 'الجرد',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings, size: 24.sp),
            activeIcon: Icon(Icons.settings, size: 26.sp),
            label: 'الإعدادات',
          ),
        ];

      case UserRole.specialist:
        // الأخصائي: الحجوزات + المهام + الإعدادات فقط
        return [
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today, size: 24.sp),
            activeIcon: Icon(Icons.calendar_today, size: 26.sp),
            label: 'الحجوزات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.task_alt, size: 24.sp),
            activeIcon: Icon(Icons.task_alt, size: 26.sp),
            label: 'المهام',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings, size: 24.sp),
            activeIcon: Icon(Icons.settings, size: 26.sp),
            label: 'الإعدادات',
          ),
        ];

      case UserRole.receptionist:
        // الريسبشنست: الحجوزات + المرضى + المبيعات + الإعدادات فقط
        return [
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today, size: 24.sp),
            activeIcon: Icon(Icons.calendar_today, size: 26.sp),
            label: 'الحجوزات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people, size: 24.sp),
            activeIcon: Icon(Icons.people, size: 26.sp),
            label: 'المرضى',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.point_of_sale, size: 24.sp),
            activeIcon: Icon(Icons.point_of_sale, size: 26.sp),
            label: 'المبيعات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings, size: 24.sp),
            activeIcon: Icon(Icons.settings, size: 26.sp),
            label: 'الإعدادات',
          ),
        ];
    }
  }
}

// Helper class to map navigation indices to page names
class NavigationHelper {
  static String getPageName(UserRole userRole, int index) {
    switch (userRole) {
      case UserRole.manager:
      case UserRole.admin:
      case UserRole.superAdmin:
        const pages = [
          'appointments',
          'patients',
          'sales',
          'products',
          'employees',
          'inventory',
          'settings'
        ];
        return index < pages.length ? pages[index] : 'appointments';

      case UserRole.specialist:
        const pages = ['appointments', 'tasks', 'settings'];
        return index < pages.length ? pages[index] : 'appointments';

      case UserRole.receptionist:
        const pages = ['appointments', 'patients', 'sales', 'settings'];
        return index < pages.length ? pages[index] : 'appointments';
    }
  }

  static int getPageIndex(UserRole userRole, String pageName) {
    switch (userRole) {
      case UserRole.manager:
      case UserRole.admin:
      case UserRole.superAdmin:
        const pages = [
          'appointments',
          'patients',
          'sales',
          'products',
          'employees',
          'inventory',
          'settings'
        ];
        return pages.indexOf(pageName).clamp(0, pages.length - 1);

      case UserRole.specialist:
        const pages = ['appointments', 'tasks', 'settings'];
        return pages.indexOf(pageName).clamp(0, pages.length - 1);

      case UserRole.receptionist:
        const pages = ['appointments', 'patients', 'sales', 'settings'];
        return pages.indexOf(pageName).clamp(0, pages.length - 1);
    }
  }
}