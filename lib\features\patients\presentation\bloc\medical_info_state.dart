import 'package:equatable/equatable.dart';
import '../../../../core/models/medical_info_model.dart';

abstract class MedicalInfoState extends Equatable {
  const MedicalInfoState();

  @override
  List<Object?> get props => [];
}

class MedicalInfoInitial extends MedicalInfoState {
  const MedicalInfoInitial();
}

class MedicalInfoLoading extends MedicalInfoState {
  const MedicalInfoLoading();
}

class MedicalInfoLoaded extends MedicalInfoState {
  final List<MedicalInfoModel> medicalInfoList;
  final String patientId;
  final Map<String, List<MedicalInfoModel>> groupedByType;

  const MedicalInfoLoaded({
    required this.medicalInfoList,
    required this.patientId,
    required this.groupedByType,
  });

  MedicalInfoLoaded copyWith({
    List<MedicalInfoModel>? medicalInfoList,
    String? patientId,
    Map<String, List<MedicalInfoModel>>? groupedByType,
  }) {
    return MedicalInfoLoaded(
      medicalInfoList: medicalInfoList ?? this.medicalInfoList,
      patientId: patientId ?? this.patientId,
      groupedByType: groupedByType ?? this.groupedByType,
    );
  }

  @override
  List<Object?> get props => [medicalInfoList, patientId, groupedByType];
}

class MedicalInfoByTypeLoaded extends MedicalInfoState {
  final List<MedicalInfoModel> medicalInfoList;
  final String patientId;
  final String infoType;

  const MedicalInfoByTypeLoaded({
    required this.medicalInfoList,
    required this.patientId,
    required this.infoType,
  });

  @override
  List<Object?> get props => [medicalInfoList, patientId, infoType];
}

class MedicalInfoCreated extends MedicalInfoState {
  final MedicalInfoModel medicalInfo;

  const MedicalInfoCreated({required this.medicalInfo});

  @override
  List<Object?> get props => [medicalInfo];
}

class MedicalInfoUpdated extends MedicalInfoState {
  final MedicalInfoModel medicalInfo;

  const MedicalInfoUpdated({required this.medicalInfo});

  @override
  List<Object?> get props => [medicalInfo];
}

class MedicalInfoDeleted extends MedicalInfoState {
  final String medicalInfoId;

  const MedicalInfoDeleted({required this.medicalInfoId});

  @override
  List<Object?> get props => [medicalInfoId];
}

class MedicalInfoStatusToggled extends MedicalInfoState {
  final String medicalInfoId;
  final bool isActive;

  const MedicalInfoStatusToggled({
    required this.medicalInfoId,
    required this.isActive,
  });

  @override
  List<Object?> get props => [medicalInfoId, isActive];
}

class MedicalInfoError extends MedicalInfoState {
  final String message;

  const MedicalInfoError({required this.message});

  @override
  List<Object?> get props => [message];
}
