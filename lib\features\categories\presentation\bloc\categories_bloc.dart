import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/categories_repository.dart';
import 'categories_event.dart';
import 'categories_state.dart';

class CategoriesBloc extends Bloc<CategoriesEvent, CategoriesState> {
  final CategoriesRepository _categoriesRepository;

  CategoriesBloc({required CategoriesRepository categoriesRepository})
      : _categoriesRepository = categoriesRepository,
        super(CategoriesInitial()) {
    on<LoadAllCategories>(_onLoadAllCategories);
    on<LoadActiveCategories>(_onLoadActiveCategories);
    on<SearchCategories>(_onSearchCategories);
    on<CreateCategory>(_onCreateCategory);
    on<UpdateCategory>(_onUpdateCategory);
    on<DeleteCategory>(_onDeleteCategory);
    on<ToggleCategoryStatus>(_onToggleCategoryStatus);
    on<GetCategoryProductsCount>(_onGetCategoryProductsCount);
  }

  Future<void> _onLoadAllCategories(
    LoadAllCategories event,
    Emitter<CategoriesState> emit,
  ) async {
    emit(CategoriesLoading());
    try {
      final categories = await _categoriesRepository.getAllCategories();
      
      emit(CategoriesLoaded(
        categories: categories,
        filteredCategories: categories,
      ));
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }

  Future<void> _onLoadActiveCategories(
    LoadActiveCategories event,
    Emitter<CategoriesState> emit,
  ) async {
    emit(CategoriesLoading());
    try {
      final categories = await _categoriesRepository.getAllCategories();
      final activeCategories = categories.where((cat) => cat.isActive).toList();
      
      emit(CategoriesLoaded(
        categories: categories,
        filteredCategories: activeCategories,
      ));
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }

  Future<void> _onSearchCategories(
    SearchCategories event,
    Emitter<CategoriesState> emit,
  ) async {
    if (state is CategoriesLoaded) {
      final currentState = state as CategoriesLoaded;

      if (event.query.isEmpty) {
        emit(currentState.copyWith(
          filteredCategories: currentState.categories,
          searchQuery: event.query,
        ));
      } else {
        try {
          debugPrint('🔍 CategoriesBloc: Searching categories with query: ${event.query}');

          final searchResults = await _categoriesRepository.searchCategories(event.query);

          emit(currentState.copyWith(
            filteredCategories: searchResults,
            searchQuery: event.query,
          ));

          debugPrint('✅ CategoriesBloc: Found ${searchResults.length} categories');
        } catch (e) {
          debugPrint('❌ CategoriesBloc: Error searching categories: $e');
          emit(CategoriesError(message: e.toString()));
        }
      }
    }
  }

  Future<void> _onCreateCategory(
    CreateCategory event,
    Emitter<CategoriesState> emit,
  ) async {
    try {
      final createdCategory = await _categoriesRepository.createCategory(event.category);
      emit(CategoryCreated(category: createdCategory));
      
      // Refresh the list
      add(LoadAllCategories());
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }

  Future<void> _onUpdateCategory(
    UpdateCategory event,
    Emitter<CategoriesState> emit,
  ) async {
    try {
      final updatedCategory = await _categoriesRepository.updateCategory(event.category);
      emit(CategoryUpdated(category: updatedCategory));
      
      // Refresh the list
      add(LoadAllCategories());
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }

  Future<void> _onDeleteCategory(
    DeleteCategory event,
    Emitter<CategoriesState> emit,
  ) async {
    try {
      await _categoriesRepository.deleteCategory(event.categoryId);
      emit(CategoryDeleted(categoryId: event.categoryId));
      
      // Refresh the list
      add(LoadAllCategories());
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }

  Future<void> _onToggleCategoryStatus(
    ToggleCategoryStatus event,
    Emitter<CategoriesState> emit,
  ) async {
    try {
      final toggledCategory = await _categoriesRepository.toggleCategoryStatus(event.categoryId);
      emit(CategoryStatusToggled(category: toggledCategory));
      
      // Refresh the list
      add(LoadAllCategories());
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }

  Future<void> _onGetCategoryProductsCount(
    GetCategoryProductsCount event,
    Emitter<CategoriesState> emit,
  ) async {
    try {
      final count = await _categoriesRepository.getCategoryProductsCount(event.categoryId);
      emit(CategoryProductsCountLoaded(
        categoryId: event.categoryId,
        count: count,
      ));
    } catch (e) {
      emit(CategoriesError(message: e.toString()));
    }
  }
}
