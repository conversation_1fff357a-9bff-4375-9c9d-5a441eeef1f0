class EmployeeTaskModel {
  final String id;
  final String employeeId;
  final String title;
  final String description;
  final String priority; // high, medium, low
  final String status; // pending, in_progress, completed, cancelled
  final DateTime? dueDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? assignedBy; // ID of admin who assigned the task
  final String? notes;

  EmployeeTaskModel({
    required this.id,
    required this.employeeId,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    this.dueDate,
    required this.createdAt,
    required this.updatedAt,
    this.assignedBy,
    this.notes,
  });

  factory EmployeeTaskModel.fromJson(Map<String, dynamic> json) {
    return EmployeeTaskModel(
      id: json['id'] ?? '',
      employeeId: json['employee_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      priority: json['priority'] ?? 'medium',
      status: json['status'] ?? 'pending',
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      assignedBy: json['assigned_by'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employee_id': employeeId,
      'title': title,
      'description': description,
      'priority': priority,
      'status': status,
      'due_date': dueDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'assigned_by': assignedBy,
      'notes': notes,
    };
  }

  EmployeeTaskModel copyWith({
    String? id,
    String? employeeId,
    String? title,
    String? description,
    String? priority,
    String? status,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? assignedBy,
    String? notes,
  }) {
    return EmployeeTaskModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      assignedBy: assignedBy ?? this.assignedBy,
      notes: notes ?? this.notes,
    );
  }

  String get priorityText {
    switch (priority) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return 'متوسطة';
    }
  }

  String get statusText {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return 'في الانتظار';
    }
  }

  bool get isOverdue {
    if (dueDate == null || status == 'completed' || status == 'cancelled') {
      return false;
    }
    return DateTime.now().isAfter(dueDate!);
  }

  bool get isDueSoon {
    if (dueDate == null || status == 'completed' || status == 'cancelled') {
      return false;
    }
    final now = DateTime.now();
    final difference = dueDate!.difference(now).inDays;
    return difference <= 3 && difference >= 0;
  }
}
