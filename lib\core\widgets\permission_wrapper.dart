import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../services/permissions_manager.dart';

class PermissionWrapper extends StatelessWidget {
  final String permissionKey;
  final String featureName;
  final Widget child;

  const PermissionWrapper({
    super.key,
    required this.permissionKey,
    required this.featureName,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final permissionsManager = PermissionsManager();
    
    // التحقق من صحة المستخدم
    if (!permissionsManager.isValidUser) {
      return NoPermissionWidget(
        featureName: featureName,
        message: 'يرجى تسجيل الدخول أولاً',
      );
    }
    
    // التحقق من نشاط الحساب
    if (!permissionsManager.isAccountActive) {
      return NoPermissionWidget(
        featureName: featureName,
        message: permissionsManager.accountSuspendedMessage,
      );
    }
    
    // التحقق من صلاحية الوصول للصفحة
    if (!permissionsManager.canAccessPage(permissionKey)) {
      return NoPermissionWidget(
        featureName: featureName,
        message: permissionsManager.accessDeniedMessage,
      );
    }
    
    // إذا كان لديه صلاحية، عرض المحتوى
    return child;
  }
}

class NoPermissionWidget extends StatelessWidget {
  final String featureName;
  final String? message;

  const NoPermissionWidget({
    super.key,
    required this.featureName,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64.sp,
                color: AppColors.gray400,
              ),
              SizedBox(height: 16.h),
              Text(
                'غير مصرح لك بالوصول',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gray900,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                message ?? 'ليس لديك صلاحية للوصول إلى $featureName',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.gray600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              Text(
                'يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.gray500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}