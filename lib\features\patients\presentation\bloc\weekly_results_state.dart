import 'package:equatable/equatable.dart';
import '../../../../core/models/weekly_result_model.dart';

abstract class WeeklyResultsState extends Equatable {
  const WeeklyResultsState();

  @override
  List<Object?> get props => [];
}

class WeeklyResultsInitial extends WeeklyResultsState {
  const WeeklyResultsInitial();
}

class WeeklyResultsLoading extends WeeklyResultsState {
  const WeeklyResultsLoading();
}

class WeeklyResultsLoaded extends WeeklyResultsState {
  final List<WeeklyResultModel> weeklyResults;
  final String patientId;
  final WeeklyResultModel? latestResult;

  const WeeklyResultsLoaded({
    required this.weeklyResults,
    required this.patientId,
    this.latestResult,
  });

  WeeklyResultsLoaded copyWith({
    List<WeeklyResultModel>? weeklyResults,
    String? patientId,
    WeeklyResultModel? latestResult,
  }) {
    return WeeklyResultsLoaded(
      weeklyResults: weeklyResults ?? this.weeklyResults,
      patientId: patientId ?? this.patientId,
      latestResult: latestResult ?? this.latestResult,
    );
  }

  @override
  List<Object?> get props => [weeklyResults, patientId, latestResult];
}

class WeeklyResultsByDateRangeLoaded extends WeeklyResultsState {
  final List<WeeklyResultModel> weeklyResults;
  final String patientId;
  final DateTime startDate;
  final DateTime endDate;

  const WeeklyResultsByDateRangeLoaded({
    required this.weeklyResults,
    required this.patientId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [weeklyResults, patientId, startDate, endDate];
}

class WeeklyResultCreated extends WeeklyResultsState {
  final WeeklyResultModel weeklyResult;

  const WeeklyResultCreated({required this.weeklyResult});

  @override
  List<Object?> get props => [weeklyResult];
}

class WeeklyResultUpdated extends WeeklyResultsState {
  final WeeklyResultModel weeklyResult;

  const WeeklyResultUpdated({required this.weeklyResult});

  @override
  List<Object?> get props => [weeklyResult];
}

class WeeklyResultDeleted extends WeeklyResultsState {
  final String resultId;

  const WeeklyResultDeleted({required this.resultId});

  @override
  List<Object?> get props => [resultId];
}

class ResultExistsForDate extends WeeklyResultsState {
  final bool exists;
  final DateTime date;

  const ResultExistsForDate({
    required this.exists,
    required this.date,
  });

  @override
  List<Object?> get props => [exists, date];
}

class WeightProgressLoaded extends WeeklyResultsState {
  final List<Map<String, dynamic>> weightProgress;
  final String patientId;

  const WeightProgressLoaded({
    required this.weightProgress,
    required this.patientId,
  });

  @override
  List<Object?> get props => [weightProgress, patientId];
}

class BodyCompositionProgressLoaded extends WeeklyResultsState {
  final List<Map<String, dynamic>> bodyCompositionProgress;
  final String patientId;

  const BodyCompositionProgressLoaded({
    required this.bodyCompositionProgress,
    required this.patientId,
  });

  @override
  List<Object?> get props => [bodyCompositionProgress, patientId];
}

class WeeklyResultsError extends WeeklyResultsState {
  final String message;

  const WeeklyResultsError({required this.message});

  @override
  List<Object?> get props => [message];
}
