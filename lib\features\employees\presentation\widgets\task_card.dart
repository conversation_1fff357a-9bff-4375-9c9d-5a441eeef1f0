import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/employee_task_model.dart';
import 'task_details_dialog.dart';

class TaskCard extends StatelessWidget {
  final EmployeeTaskModel task;
  final EmployeeModel employee;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final Function(String) onStatusChanged;

  const TaskCard({
    super.key,
    required this.task,
    required this.employee,
    required this.onEdit,
    required this.onDelete,
    required this.onStatusChanged,
  });

  Color _getPriorityColor() {
    switch (task.priority) {
      case 'high':
        return AppColors.error;
      case 'medium':
        return AppColors.warning;
      case 'low':
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getStatusColor() {
    switch (task.status) {
      case 'pending':
        return AppColors.warning;
      case 'in_progress':
        return AppColors.info;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.textSecondary;
      default:
        return AppColors.textSecondary;
    }
  }

  Widget _buildStatusChip() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: _getStatusColor(), width: 1),
      ),
      child: Text(
        task.statusText,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
          color: _getStatusColor(),
        ),
      ),
    );
  }

  Widget _buildPriorityChip() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: _getPriorityColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: _getPriorityColor(), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.flag,
            size: 12.sp,
            color: _getPriorityColor(),
          ),
          SizedBox(width: 4.w),
          Text(
            task.priorityText,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: _getPriorityColor(),
            ),
          ),
        ],
      ),
    );
  }

  void _showStatusMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تغيير حالة المهمة',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildStatusOption(context, 'pending', 'في الانتظار', Icons.schedule),
            _buildStatusOption(context, 'in_progress', 'قيد التنفيذ', Icons.play_arrow),
            _buildStatusOption(context, 'completed', 'مكتملة', Icons.check_circle),
            _buildStatusOption(context, 'cancelled', 'ملغية', Icons.cancel),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusOption(BuildContext context, String status, String label, IconData icon) {
    final isSelected = task.status == status;
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
      ),
      title: Text(
        label,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? AppColors.primary : AppColors.textPrimary,
        ),
      ),
      trailing: isSelected ? const Icon(Icons.check, color: AppColors.primary) : null,
      onTap: () {
        Navigator.of(context).pop();
        if (!isSelected) {
          onStatusChanged(status);
        }
      },
    );
  }

  Color _getStatusBorderColor() {
    switch (task.status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOverdue = task.isOverdue;
    final isDueSoon = task.isDueSoon;

    return InkWell(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) => TaskDetailsDialog(
            task: task,
            employee: employee,
            onEdit: onEdit,
            onDelete: onDelete,
          ),
        );
      },
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: _getStatusBorderColor(),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Expanded(
                  child: Text(
                    task.title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.w),
                _buildStatusChip(),
                SizedBox(width: 4.w),
                PopupMenuButton<String>(
                  padding: EdgeInsets.zero,
                  iconSize: 18.sp,
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEdit();
                        break;
                      case 'delete':
                        onDelete();
                        break;
                      case 'status':
                        _showStatusMenu(context);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'status',
                      child: Row(
                        children: [
                          Icon(Icons.edit_note),
                          SizedBox(width: 8),
                          Text('تغيير الحالة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: AppColors.error)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: 8.h),

            // Employee and Due Date Row
            Row(
              children: [
                // Employee Info
                Expanded(
                  child: Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: 4.w),
                      Flexible(
                        child: Text(
                          employee.name,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

                // Due Date
                if (task.dueDate != null) ...[
                  Icon(
                    Icons.schedule,
                    size: 14.sp,
                    color: isOverdue
                        ? AppColors.error
                        : isDueSoon 
                            ? AppColors.warning 
                            : AppColors.textSecondary,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    DateFormat('dd/MM').format(task.dueDate!),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: isOverdue
                          ? AppColors.error
                          : isDueSoon
                              ? AppColors.warning
                              : AppColors.textSecondary,
                      fontWeight: isOverdue || isDueSoon ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ],
            ),

            SizedBox(height: 8.h),

            // Priority Row
            Row(
              children: [
                _buildPriorityChip(),
                const Spacer(),
                if (isOverdue)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'متأخرة',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  )
                else if (isDueSoon)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: AppColors.warning.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'مستحقة قريباً',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.warning,
                      ),
                    ),
                  ),
              ],
            ),

            // Notes
            if (task.notes != null && task.notes!.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'ملاحظات: ${task.notes}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
