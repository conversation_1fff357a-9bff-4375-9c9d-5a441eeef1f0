import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import 'notification_scheduler_service.dart';

class NotificationAutomationService {
  static Timer? _timer;
  static bool _isRunning = false;

  // Start the notification automation service
  static void startAutomation() {
    if (_isRunning) return;

    _isRunning = true;
    debugPrint('🚀 Starting notification automation service...');

    // Check for due notifications every minute
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkAndSendDueNotifications();
    });

    // Also check immediately when starting
    _checkAndSendDueNotifications();
  }

  // Stop the notification automation service
  static void stopAutomation() {
    if (!_isRunning) return;

    _isRunning = false;
    _timer?.cancel();
    _timer = null;
    debugPrint('🛑 Notification automation service stopped');
  }

  // Check and send due notifications
  static Future<void> _checkAndSendDueNotifications() async {
    try {
      await NotificationSchedulerService.processDueNotifications();
    } catch (e) {
      debugPrint('❌ Error in notification automation: $e');
    }
  }

  // Create notification when reminder is created
  static Future<void> createNotificationForReminder({
    required String patientId,
    required String reminderId,
    required String reminderType,
    required String reminderContent,
    required String scheduledTime,
    required List<int> daysOfWeek,
  }) async {
    try {
      String title;
      String body;

      switch (reminderType) {
        case 'meal':
          title = 'تذكير الوجبة';
          body = reminderContent;
          break;
        case 'exercise':
          title = 'تذكير النشاط البدني';
          body = reminderContent;
          break;
        case 'medication':
          title = 'تذكير الدواء';
          body = reminderContent;
          break;
        case 'water':
          title = 'تذكير شرب الماء';
          body = reminderContent;
          break;
        default:
          title = 'تذكير من Diet Rx';
          body = reminderContent;
      }

      final notificationId =
          await NotificationSchedulerService.createScheduledNotification(
            patientId: patientId,
            reminderId: reminderId.isNotEmpty ? reminderId : const Uuid().v4(),
            notificationType: reminderType,
            title: title,
            body: body,
            scheduledTime: scheduledTime,
            daysOfWeek: daysOfWeek,
          );

      if (notificationId != null) {
        debugPrint('✅ Notification created for reminder: $reminderId');
      } else {
        debugPrint('❌ Failed to create notification for reminder: $reminderId');
      }
    } catch (e) {
      debugPrint('❌ Error creating notification for reminder: $e');
    }
  }

  // Update notification when reminder is updated
  static Future<void> updateNotificationForReminder({
    required String reminderId,
    String? reminderContent,
    String? scheduledTime,
    List<int>? daysOfWeek,
  }) async {
    try {
      debugPrint('📝 Updating notification for reminder: $reminderId');

      // Get the notification by reminder_id
      final notification =
          await NotificationSchedulerService.getNotificationByReminderId(
            reminderId,
          );

      if (notification != null) {
        // Update the notification
        await NotificationSchedulerService.updateScheduledNotification(
          notificationId: notification['id'],
          title:
              reminderContent != null
                  ? _getTitleByType(notification['notification_type'])
                  : null,
          body: reminderContent,
          scheduledTime: scheduledTime,
          daysOfWeek: daysOfWeek,
        );
        debugPrint('✅ Notification updated for reminder: $reminderId');
      } else {
        debugPrint('⚠️ No notification found for reminder: $reminderId');
      }
    } catch (e) {
      debugPrint('❌ Error updating notification for reminder: $e');
    }
  }

  // Delete notification when reminder is deleted
  static Future<void> deleteNotificationForReminder(String reminderId) async {
    try {
      debugPrint('🗑️ Deleting notification for reminder: $reminderId');

      // Get the notification by reminder_id
      final notification =
          await NotificationSchedulerService.getNotificationByReminderId(
            reminderId,
          );

      if (notification != null) {
        // Delete the notification
        await NotificationSchedulerService.deleteScheduledNotification(
          notification['id'],
        );
        debugPrint('✅ Notification deleted for reminder: $reminderId');
      } else {
        debugPrint('⚠️ No notification found for reminder: $reminderId');
      }
    } catch (e) {
      debugPrint('❌ Error deleting notification for reminder: $e');
    }
  }

  // Send immediate test notification
  static Future<bool> sendTestNotification({
    required String patientId,
    String? customMessage,
  }) async {
    try {
      final success = await NotificationSchedulerService.sendNotificationNow(
        patientId: patientId,
        notificationType: 'test',
        title: 'اختبار الإشعارات',
        body: customMessage ?? 'هذا إشعار تجريبي من تطبيق Diet Rx',
        additionalData: {
          'test': 'true',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (success) {
        debugPrint('✅ Test notification sent successfully');
      } else {
        debugPrint('❌ Failed to send test notification');
      }

      return success;
    } catch (e) {
      debugPrint('❌ Error sending test notification: $e');
      return false;
    }
  }

  // Get automation status
  static bool get isRunning => _isRunning;

  // Force check for due notifications (for manual trigger)
  static Future<void> forceCheckDueNotifications() async {
    debugPrint('🔄 Manually checking for due notifications...');
    await _checkAndSendDueNotifications();
  }

  // Helper method to get title by notification type
  static String _getTitleByType(String notificationType) {
    switch (notificationType) {
      case 'meal':
        return 'تذكير الوجبة';
      case 'exercise':
        return 'تذكير النشاط البدني';
      case 'medication':
        return 'تذكير الدواء';
      case 'water':
        return 'تذكير شرب الماء';
      default:
        return 'تذكير من Diet Rx';
    }
  }
}
