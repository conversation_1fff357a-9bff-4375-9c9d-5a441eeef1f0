import 'package:equatable/equatable.dart';
import '../../../../core/models/appointment_model.dart';

abstract class AppointmentsEvent extends Equatable {
  const AppointmentsEvent();

  @override
  List<Object?> get props => [];
}

class LoadTodayAppointments extends AppointmentsEvent {}

class LoadAppointmentsByDate extends AppointmentsEvent {
  final DateTime date;

  const LoadAppointmentsByDate({required this.date});

  @override
  List<Object?> get props => [date];
}

class LoadSpecialistAppointmentsByDate extends AppointmentsEvent {
  final DateTime date;
  final String specialistId;

  const LoadSpecialistAppointmentsByDate({
    required this.date,
    required this.specialistId,
  });

  @override
  List<Object?> get props => [date, specialistId];
}

class LoadPatientAppointments extends AppointmentsEvent {
  final String patientId;

  const LoadPatientAppointments({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadUpcomingAppointments extends AppointmentsEvent {}

class CreateAppointment extends AppointmentsEvent {
  final AppointmentModel appointment;

  const CreateAppointment({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class UpdateAppointment extends AppointmentsEvent {
  final AppointmentModel appointment;

  const UpdateAppointment({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class UpdateAppointmentStatus extends AppointmentsEvent {
  final String appointmentId;
  final String status;

  const UpdateAppointmentStatus({
    required this.appointmentId,
    required this.status,
  });

  @override
  List<Object?> get props => [appointmentId, status];
}

class DeleteAppointment extends AppointmentsEvent {
  final String appointmentId;

  const DeleteAppointment({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

class RefreshAppointments extends AppointmentsEvent {}

class ChangeSelectedDate extends AppointmentsEvent {
  final DateTime date;

  const ChangeSelectedDate({required this.date});

  @override
  List<Object?> get props => [date];
}