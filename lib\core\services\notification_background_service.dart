import 'dart:async';
import 'package:flutter/foundation.dart';
import 'notification_scheduler_service.dart';

class NotificationBackgroundService {
  static Timer? _timer;
  static bool _isRunning = false;

  // Start the background service to check for due notifications
  static void startBackgroundService() {
    if (_isRunning) {
      debugPrint('🔄 Notification background service is already running');
      return;
    }

    debugPrint('🚀 Starting notification background service...');
    _isRunning = true;

    // Check every minute for due notifications
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkDueNotifications();
    });

    // Also check immediately
    _checkDueNotifications();
  }

  // Stop the background service
  static void stopBackgroundService() {
    debugPrint('🛑 Stopping notification background service...');
    _timer?.cancel();
    _timer = null;
    _isRunning = false;
  }

  // Check for due notifications
  static Future<void> _checkDueNotifications() async {
    try {
      debugPrint('🔍 Checking for due notifications...');
      await NotificationSchedulerService.processDueNotifications();
    } catch (e) {
      debugPrint('❌ Error in background notification check: $e');
    }
  }

  // Force check (for manual trigger)
  static Future<void> forceCheck() async {
    debugPrint('🔄 Force checking for due notifications...');
    await _checkDueNotifications();
  }

  // Check if service is running
  static bool get isRunning => _isRunning;
}
