import 'package:equatable/equatable.dart';
import 'patient_model.dart';
import 'sales_invoice_model.dart';
import 'return_item_model.dart';

enum ReturnStatus { pending, approved, completed, cancelled }
enum RefundMethod { cash, card, bankTransfer, credit }

extension ReturnStatusExtension on ReturnStatus {
  String get value {
    switch (this) {
      case ReturnStatus.pending:
        return 'pending';
      case ReturnStatus.approved:
        return 'approved';
      case ReturnStatus.completed:
        return 'completed';
      case ReturnStatus.cancelled:
        return 'cancelled';
    }
  }

  String get arabicName {
    switch (this) {
      case ReturnStatus.pending:
        return 'في الانتظار';
      case ReturnStatus.approved:
        return 'موافق عليه';
      case ReturnStatus.completed:
        return 'مكتمل';
      case ReturnStatus.cancelled:
        return 'ملغي';
    }
  }

  static ReturnStatus fromString(String value) {
    switch (value) {
      case 'pending':
        return ReturnStatus.pending;
      case 'approved':
        return ReturnStatus.approved;
      case 'completed':
        return ReturnStatus.completed;
      case 'cancelled':
        return ReturnStatus.cancelled;
      default:
        return ReturnStatus.pending;
    }
  }
}

extension RefundMethodExtension on RefundMethod {
  String get value {
    switch (this) {
      case RefundMethod.cash:
        return 'cash';
      case RefundMethod.card:
        return 'card';
      case RefundMethod.bankTransfer:
        return 'bank_transfer';
      case RefundMethod.credit:
        return 'credit';
    }
  }

  String get arabicName {
    switch (this) {
      case RefundMethod.cash:
        return 'نقداً';
      case RefundMethod.card:
        return 'بطاقة ائتمان';
      case RefundMethod.bankTransfer:
        return 'تحويل بنكي';
      case RefundMethod.credit:
        return 'رصيد';
    }
  }

  static RefundMethod fromString(String value) {
    switch (value) {
      case 'cash':
        return RefundMethod.cash;
      case 'card':
        return RefundMethod.card;
      case 'bank_transfer':
        return RefundMethod.bankTransfer;
      case 'credit':
        return RefundMethod.credit;
      default:
        return RefundMethod.cash;
    }
  }
}

class ReturnModel extends Equatable {
  final String id;
  final String returnNumber;
  final String invoiceId;
  final SalesInvoiceModel? invoice;
  final String patientId;
  final PatientModel? patient;
  final double totalReturnAmount;
  final double refundAmount;
  final RefundMethod? refundMethod;
  final String? reason;
  final ReturnStatus status;
  final String? createdBy;
  final String? approvedBy;
  final DateTime createdAt;
  final DateTime? approvedAt;
  final List<ReturnItemModel> items;

  const ReturnModel({
    required this.id,
    required this.returnNumber,
    required this.invoiceId,
    this.invoice,
    required this.patientId,
    this.patient,
    required this.totalReturnAmount,
    required this.refundAmount,
    this.refundMethod,
    this.reason,
    this.status = ReturnStatus.pending,
    this.createdBy,
    this.approvedBy,
    required this.createdAt,
    this.approvedAt,
    this.items = const [],
  });

  factory ReturnModel.fromJson(Map<String, dynamic> json) {
    return ReturnModel(
      id: json['id'] as String,
      returnNumber: json['return_number'] as String,
      invoiceId: json['invoice_id'] as String,
      invoice: json['sales_invoices'] != null 
          ? SalesInvoiceModel.fromJson(json['sales_invoices'] as Map<String, dynamic>)
          : null,
      patientId: json['patient_id'] as String,
      patient: json['patients'] != null 
          ? PatientModel.fromJson(json['patients'] as Map<String, dynamic>)
          : null,
      totalReturnAmount: (json['total_return_amount'] as num?)?.toDouble() ?? 0.0,
      refundAmount: (json['refund_amount'] as num?)?.toDouble() ?? 0.0,
      refundMethod: json['refund_method'] != null 
          ? RefundMethodExtension.fromString(json['refund_method'] as String)
          : null,
      reason: json['reason'] as String?,
      status: ReturnStatusExtension.fromString(json['status'] as String? ?? 'pending'),
      createdBy: json['created_by'] as String?,
      approvedBy: json['approved_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      approvedAt: json['approved_at'] != null 
          ? DateTime.parse(json['approved_at'] as String)
          : null,
      items: json['return_items'] != null
          ? (json['return_items'] as List)
              .map((item) => ReturnItemModel.fromJson(item as Map<String, dynamic>))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // Don't include 'id' when creating new returns, let database generate UUID
      'return_number': returnNumber,
      'invoice_id': invoiceId,
      'patient_id': patientId,
      'total_return_amount': totalReturnAmount,
      'refund_amount': refundAmount,
      'refund_method': refundMethod?.value,
      'reason': reason,
      'status': status.value,
      'created_by': createdBy,
      'approved_by': approvedBy,
      'created_at': createdAt.toIso8601String(),
      'approved_at': approvedAt?.toIso8601String(),
    };
  }

  ReturnModel copyWith({
    String? id,
    String? returnNumber,
    String? invoiceId,
    SalesInvoiceModel? invoice,
    String? patientId,
    PatientModel? patient,
    double? totalReturnAmount,
    double? refundAmount,
    RefundMethod? refundMethod,
    String? reason,
    ReturnStatus? status,
    String? createdBy,
    String? approvedBy,
    DateTime? createdAt,
    DateTime? approvedAt,
    List<ReturnItemModel>? items,
  }) {
    return ReturnModel(
      id: id ?? this.id,
      returnNumber: returnNumber ?? this.returnNumber,
      invoiceId: invoiceId ?? this.invoiceId,
      invoice: invoice ?? this.invoice,
      patientId: patientId ?? this.patientId,
      patient: patient ?? this.patient,
      totalReturnAmount: totalReturnAmount ?? this.totalReturnAmount,
      refundAmount: refundAmount ?? this.refundAmount,
      refundMethod: refundMethod ?? this.refundMethod,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      approvedBy: approvedBy ?? this.approvedBy,
      createdAt: createdAt ?? this.createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      items: items ?? this.items,
    );
  }

  @override
  List<Object?> get props => [
        id,
        returnNumber,
        invoiceId,
        invoice,
        patientId,
        patient,
        totalReturnAmount,
        refundAmount,
        refundMethod,
        reason,
        status,
        createdBy,
        approvedBy,
        createdAt,
        approvedAt,
        items,
      ];
}
