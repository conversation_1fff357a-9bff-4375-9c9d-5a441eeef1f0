import 'package:equatable/equatable.dart';
import '../../../../core/models/patient_model.dart';

abstract class PatientsState extends Equatable {
  const PatientsState();

  @override
  List<Object?> get props => [];
}

class PatientsInitial extends PatientsState {}

class PatientsLoading extends PatientsState {}

class PatientsLoaded extends PatientsState {
  final List<PatientModel> patients;
  final List<PatientModel> premiumPatients;
  final bool isSearching;
  final String searchQuery;

  const PatientsLoaded({
    required this.patients,
    required this.premiumPatients,
    this.isSearching = false,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [patients, premiumPatients, isSearching, searchQuery];

  PatientsLoaded copyWith({
    List<PatientModel>? patients,
    List<PatientModel>? premiumPatients,
    bool? isSearching,
    String? searchQuery,
  }) {
    return PatientsLoaded(
      patients: patients ?? this.patients,
      premiumPatients: premiumPatients ?? this.premiumPatients,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class PatientsError extends PatientsState {
  final String message;

  const PatientsError({required this.message});

  @override
  List<Object?> get props => [message];
}

class PatientCreated extends PatientsState {
  final PatientModel patient;

  const PatientCreated({required this.patient});

  @override
  List<Object?> get props => [patient];
}

class PatientUpdated extends PatientsState {
  final PatientModel patient;

  const PatientUpdated({required this.patient});

  @override
  List<Object?> get props => [patient];
}

class PatientDeleted extends PatientsState {
  final String patientId;

  const PatientDeleted({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class PatientUpgraded extends PatientsState {
  final PatientModel patient;

  const PatientUpgraded({required this.patient});

  @override
  List<Object?> get props => [patient];
}

class PatientDowngraded extends PatientsState {
  final PatientModel patient;

  const PatientDowngraded({required this.patient});

  @override
  List<Object?> get props => [patient];
}
