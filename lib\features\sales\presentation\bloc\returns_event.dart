import 'package:equatable/equatable.dart';
import '../../../../core/models/return_model.dart';
import '../../../../core/models/return_item_model.dart';

abstract class ReturnsEvent extends Equatable {
  const ReturnsEvent();

  @override
  List<Object?> get props => [];
}

class LoadReturns extends ReturnsEvent {
  final int page;
  final String? searchQuery;
  final ReturnStatus? status;

  const LoadReturns({
    this.page = 1,
    this.searchQuery,
    this.status,
  });

  @override
  List<Object?> get props => [page, searchQuery, status];
}

class LoadMoreReturns extends ReturnsEvent {}

class RefreshReturns extends ReturnsEvent {}

class CreateReturn extends ReturnsEvent {
  final ReturnModel returnModel;
  final List<ReturnItemModel> items;

  const CreateReturn({
    required this.returnModel,
    required this.items,
  });

  @override
  List<Object?> get props => [returnModel, items];
}

class LoadReturnDetails extends ReturnsEvent {
  final String returnId;

  const LoadReturnDetails(this.returnId);

  @override
  List<Object?> get props => [returnId];
}

class ApproveReturn extends ReturnsEvent {
  final String returnId;
  final String approvedBy;

  const ApproveReturn({
    required this.returnId,
    required this.approvedBy,
  });

  @override
  List<Object?> get props => [returnId, approvedBy];
}

class CompleteReturn extends ReturnsEvent {
  final String returnId;

  const CompleteReturn(this.returnId);

  @override
  List<Object?> get props => [returnId];
}

class CancelReturn extends ReturnsEvent {
  final String returnId;

  const CancelReturn(this.returnId);

  @override
  List<Object?> get props => [returnId];
}

class LoadReturnableItems extends ReturnsEvent {
  final String invoiceId;

  const LoadReturnableItems(this.invoiceId);

  @override
  List<Object?> get props => [invoiceId];
}

class SearchReturns extends ReturnsEvent {
  final String query;

  const SearchReturns(this.query);

  @override
  List<Object?> get props => [query];
}

class FilterReturnsByStatus extends ReturnsEvent {
  final ReturnStatus? status;

  const FilterReturnsByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

class ClearReturnsFilters extends ReturnsEvent {}

class GenerateReturnNumber extends ReturnsEvent {}
