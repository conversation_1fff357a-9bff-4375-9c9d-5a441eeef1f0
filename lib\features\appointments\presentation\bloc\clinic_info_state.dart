import 'package:equatable/equatable.dart';
import '../../../../core/models/clinic_info_model.dart';

abstract class ClinicInfoState extends Equatable {
  const ClinicInfoState();

  @override
  List<Object?> get props => [];
}

class ClinicInfoInitial extends ClinicInfoState {
  const ClinicInfoInitial();
}

class ClinicInfoLoading extends ClinicInfoState {
  const ClinicInfoLoading();
}

class ClinicInfoLoaded extends ClinicInfoState {
  final List<ClinicInfoModel> clinicInfoList;
  final Map<String, List<ClinicInfoModel>> groupedInfo;
  final List<ClinicInfoModel> searchResults;
  final String searchQuery;

  const ClinicInfoLoaded({
    required this.clinicInfoList,
    this.groupedInfo = const {},
    this.searchResults = const [],
    this.searchQuery = '',
  });

  // Helper getters for different types
  List<ClinicInfoModel> get phoneNumbers => 
      clinicInfoList.where((info) => info.infoType == 'phone').toList();

  List<ClinicInfoModel> get emailAddresses => 
      clinicInfoList.where((info) => info.infoType == 'email').toList();

  List<ClinicInfoModel> get socialMediaLinks => 
      clinicInfoList.where((info) => info.infoType == 'social_media').toList();

  List<ClinicInfoModel> get addresses => 
      clinicInfoList.where((info) => info.infoType == 'address').toList();

  List<ClinicInfoModel> get workingHours => 
      clinicInfoList.where((info) => info.infoType == 'working_hours').toList();

  List<ClinicInfoModel> get websiteInfo => 
      clinicInfoList.where((info) => info.infoType == 'website').toList();

  ClinicInfoLoaded copyWith({
    List<ClinicInfoModel>? clinicInfoList,
    Map<String, List<ClinicInfoModel>>? groupedInfo,
    List<ClinicInfoModel>? searchResults,
    String? searchQuery,
  }) {
    return ClinicInfoLoaded(
      clinicInfoList: clinicInfoList ?? this.clinicInfoList,
      groupedInfo: groupedInfo ?? this.groupedInfo,
      searchResults: searchResults ?? this.searchResults,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [clinicInfoList, groupedInfo, searchResults, searchQuery];
}

class ClinicInfoByTypeLoaded extends ClinicInfoState {
  final String infoType;
  final List<ClinicInfoModel> clinicInfoList;

  const ClinicInfoByTypeLoaded({
    required this.infoType,
    required this.clinicInfoList,
  });

  @override
  List<Object?> get props => [infoType, clinicInfoList];
}

class ClinicInfoGroupedLoaded extends ClinicInfoState {
  final Map<String, List<ClinicInfoModel>> groupedInfo;

  const ClinicInfoGroupedLoaded({required this.groupedInfo});

  @override
  List<Object?> get props => [groupedInfo];
}

class ClinicInfoCreated extends ClinicInfoState {
  final ClinicInfoModel clinicInfo;

  const ClinicInfoCreated({required this.clinicInfo});

  @override
  List<Object?> get props => [clinicInfo];
}

class ClinicInfoUpdated extends ClinicInfoState {
  final ClinicInfoModel clinicInfo;

  const ClinicInfoUpdated({required this.clinicInfo});

  @override
  List<Object?> get props => [clinicInfo];
}

class ClinicInfoDeleted extends ClinicInfoState {
  final String clinicInfoId;

  const ClinicInfoDeleted({required this.clinicInfoId});

  @override
  List<Object?> get props => [clinicInfoId];
}

class ClinicInfoStatusToggled extends ClinicInfoState {
  final ClinicInfoModel clinicInfo;

  const ClinicInfoStatusToggled({required this.clinicInfo});

  @override
  List<Object?> get props => [clinicInfo];
}

class ClinicInfoError extends ClinicInfoState {
  final String message;

  const ClinicInfoError({required this.message});

  @override
  List<Object?> get props => [message];
}
