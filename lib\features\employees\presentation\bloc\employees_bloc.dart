import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/models/admin_model.dart';
import '../../data/repositories/employees_repository.dart';

// Events
abstract class EmployeesEvent extends Equatable {
  const EmployeesEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllEmployees extends EmployeesEvent {}

class LoadEmployeesByType extends EmployeesEvent {
  final String employeeType;

  const LoadEmployeesByType(this.employeeType);

  @override
  List<Object?> get props => [employeeType];
}

class LoadSpecialistsBySpecialization extends EmployeesEvent {
  final String specializationId;

  const LoadSpecialistsBySpecialization(this.specializationId);

  @override
  List<Object?> get props => [specializationId];
}

class AddEmployee extends EmployeesEvent {
  final EmployeeModel employee;

  const AddEmployee(this.employee);

  @override
  List<Object?> get props => [employee];
}

class UpdateEmployee extends EmployeesEvent {
  final EmployeeModel employee;

  const UpdateEmployee(this.employee);

  @override
  List<Object?> get props => [employee];
}

class DeleteEmployee extends EmployeesEvent {
  final String employeeId;

  const DeleteEmployee(this.employeeId);

  @override
  List<Object?> get props => [employeeId];
}

class ToggleEmployeeStatus extends EmployeesEvent {
  final String employeeId;
  final bool isActive;

  const ToggleEmployeeStatus(this.employeeId, this.isActive);

  @override
  List<Object?> get props => [employeeId, isActive];
}

// States
abstract class EmployeesState extends Equatable {
  const EmployeesState();

  @override
  List<Object?> get props => [];
}

class EmployeesInitial extends EmployeesState {}

class EmployeesLoading extends EmployeesState {}

class EmployeesLoaded extends EmployeesState {
  final List<EmployeeModel> employees;

  const EmployeesLoaded(this.employees);

  @override
  List<Object?> get props => [employees];
}

class EmployeesError extends EmployeesState {
  final String message;

  const EmployeesError(this.message);

  @override
  List<Object?> get props => [message];
}

class EmployeeOperationSuccess extends EmployeesState {
  final String message;
  final List<EmployeeModel> employees;

  const EmployeeOperationSuccess(this.message, this.employees);

  @override
  List<Object?> get props => [message, employees];
}

// Bloc
class EmployeesBloc extends Bloc<EmployeesEvent, EmployeesState> {
  EmployeesBloc() : super(EmployeesInitial()) {
    on<LoadAllEmployees>(_onLoadAllEmployees);
    on<LoadEmployeesByType>(_onLoadEmployeesByType);
    on<LoadSpecialistsBySpecialization>(_onLoadSpecialistsBySpecialization);
    on<AddEmployee>(_onAddEmployee);
    on<UpdateEmployee>(_onUpdateEmployee);
    on<DeleteEmployee>(_onDeleteEmployee);
    on<ToggleEmployeeStatus>(_onToggleEmployeeStatus);
  }

  Future<void> _onLoadAllEmployees(LoadAllEmployees event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Starting to load all employees...');
      emit(EmployeesLoading());

      final employees = await EmployeesRepository.getAllEmployees();
      
      debugPrint('✅ EmployeesBloc: Successfully loaded ${employees.length} employees');
      emit(EmployeesLoaded(employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error: $e');
      emit(EmployeesError(e.toString()));
    }
  }

  Future<void> _onLoadEmployeesByType(LoadEmployeesByType event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Starting to load employees by type: ${event.employeeType}...');
      emit(EmployeesLoading());

      final employees = await EmployeesRepository.getEmployeesByType(event.employeeType);
      
      debugPrint('✅ EmployeesBloc: Successfully loaded ${employees.length} employees of type ${event.employeeType}');
      emit(EmployeesLoaded(employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error: $e');
      emit(EmployeesError(e.toString()));
    }
  }

  Future<void> _onLoadSpecialistsBySpecialization(LoadSpecialistsBySpecialization event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Starting to load specialists by specialization: ${event.specializationId}...');
      emit(EmployeesLoading());

      final employees = await EmployeesRepository.getSpecialistsBySpecialization(event.specializationId);
      
      debugPrint('✅ EmployeesBloc: Successfully loaded ${employees.length} specialists');
      emit(EmployeesLoaded(employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error: $e');
      emit(EmployeesError(e.toString()));
    }
  }

  Future<void> _onAddEmployee(AddEmployee event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Adding new employee: ${event.employee.name}...');
      
      await EmployeesRepository.addEmployee(event.employee);
      
      // Reload all employees
      final employees = await EmployeesRepository.getAllEmployees();
      
      debugPrint('✅ EmployeesBloc: Successfully added employee');
      emit(EmployeeOperationSuccess('تم إضافة الموظف بنجاح', employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error adding employee: $e');
      emit(EmployeesError(e.toString()));
    }
  }

  Future<void> _onUpdateEmployee(UpdateEmployee event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Updating employee: ${event.employee.name}...');
      
      await EmployeesRepository.updateEmployee(event.employee);
      
      // Reload all employees
      final employees = await EmployeesRepository.getAllEmployees();
      
      debugPrint('✅ EmployeesBloc: Successfully updated employee');
      emit(EmployeeOperationSuccess('تم تحديث الموظف بنجاح', employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error updating employee: $e');
      emit(EmployeesError(e.toString()));
    }
  }

  Future<void> _onDeleteEmployee(DeleteEmployee event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Deleting employee: ${event.employeeId}...');
      
      await EmployeesRepository.deleteEmployee(event.employeeId);
      
      // Reload all employees
      final employees = await EmployeesRepository.getAllEmployees();
      
      debugPrint('✅ EmployeesBloc: Successfully deleted employee');
      emit(EmployeeOperationSuccess('تم حذف الموظف بنجاح', employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error deleting employee: $e');
      emit(EmployeesError(e.toString()));
    }
  }

  Future<void> _onToggleEmployeeStatus(ToggleEmployeeStatus event, Emitter<EmployeesState> emit) async {
    try {
      debugPrint('🔄 EmployeesBloc: Toggling employee status: ${event.employeeId} to ${event.isActive}...');
      
      await EmployeesRepository.toggleEmployeeStatus(event.employeeId, event.isActive);
      
      // Reload all employees
      final employees = await EmployeesRepository.getAllEmployees();
      
      final statusText = event.isActive ? 'تم تفعيل' : 'تم إلغاء تفعيل';
      debugPrint('✅ EmployeesBloc: Successfully toggled employee status');
      emit(EmployeeOperationSuccess('$statusText الموظف بنجاح', employees));
    } catch (e) {
      debugPrint('❌ EmployeesBloc Error toggling employee status: $e');
      emit(EmployeesError(e.toString()));
    }
  }
}
