import 'package:equatable/equatable.dart';

class ExaminationModel extends Equatable {
  final String id;
  final String patientId;
  final String title;
  final String type;
  final String? description;
  final String? imageUrl;
  final DateTime examinationDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExaminationModel({
    required this.id,
    required this.patientId,
    required this.title,
    required this.type,
    this.description,
    this.imageUrl,
    required this.examinationDate,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ExaminationModel.fromJson(Map<String, dynamic> json) {
    return ExaminationModel(
      id: json['id'] as String,
      patientId: json['patient_id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      examinationDate: DateTime.parse(json['examination_date'] as String),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'title': title,
      'type': type,
      'description': description,
      'image_url': imageUrl,
      'examination_date': examinationDate.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ExaminationModel copyWith({
    String? id,
    String? patientId,
    String? title,
    String? type,
    String? description,
    String? imageUrl,
    DateTime? examinationDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExaminationModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      title: title ?? this.title,
      type: type ?? this.type,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      examinationDate: examinationDate ?? this.examinationDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        patientId,
        title,
        type,
        description,
        imageUrl,
        examinationDate,
        notes,
        createdAt,
        updatedAt,
      ];
}

// Examination types enum
enum ExaminationType {
  hearing('فحص السمع'),
  speech('فحص النطق'),
  behavioral('فحص سلوكي'),
  cognitive('فحص إدراكي'),
  motor('فحص حركي'),
  sensory('فحص حسي'),
  developmental('فحص نمائي'),
  communication('فحص التواصل'),
  other('أخرى');

  const ExaminationType(this.arabicName);
  final String arabicName;

  static ExaminationType fromString(String value) {
    return ExaminationType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => ExaminationType.other,
    );
  }

  static List<String> get allTypes => ExaminationType.values.map((e) => e.name).toList();
  static List<String> get allArabicNames => ExaminationType.values.map((e) => e.arabicName).toList();
}
