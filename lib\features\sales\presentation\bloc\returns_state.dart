import 'package:equatable/equatable.dart';
import '../../../../core/models/return_model.dart';

abstract class ReturnsState extends Equatable {
  const ReturnsState();

  @override
  List<Object?> get props => [];
}

class ReturnsInitial extends ReturnsState {}

class ReturnsLoading extends ReturnsState {}

class ReturnsLoaded extends ReturnsState {
  final List<ReturnModel> returns;
  final bool hasReachedMax;
  final int currentPage;
  final String? searchQuery;
  final ReturnStatus? statusFilter;

  const ReturnsLoaded({
    required this.returns,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.searchQuery,
    this.statusFilter,
  });

  ReturnsLoaded copyWith({
    List<ReturnModel>? returns,
    bool? hasReachedMax,
    int? currentPage,
    String? searchQuery,
    ReturnStatus? statusFilter,
    bool clearSearchQuery = false,
    bool clearStatusFilter = false,
  }) {
    return ReturnsLoaded(
      returns: returns ?? this.returns,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      statusFilter: clearStatusFilter ? null : (statusFilter ?? this.statusFilter),
    );
  }

  @override
  List<Object?> get props => [
        returns,
        hasReachedMax,
        currentPage,
        searchQuery,
        statusFilter,
      ];
}

class ReturnDetailsLoaded extends ReturnsState {
  final ReturnModel returnModel;

  const ReturnDetailsLoaded(this.returnModel);

  @override
  List<Object?> get props => [returnModel];
}

class ReturnCreated extends ReturnsState {
  final ReturnModel returnModel;

  const ReturnCreated(this.returnModel);

  @override
  List<Object?> get props => [returnModel];
}

class ReturnApproved extends ReturnsState {
  final ReturnModel returnModel;

  const ReturnApproved(this.returnModel);

  @override
  List<Object?> get props => [returnModel];
}

class ReturnCompleted extends ReturnsState {
  final ReturnModel returnModel;

  const ReturnCompleted(this.returnModel);

  @override
  List<Object?> get props => [returnModel];
}

class ReturnCancelled extends ReturnsState {
  final ReturnModel returnModel;

  const ReturnCancelled(this.returnModel);

  @override
  List<Object?> get props => [returnModel];
}

class ReturnableItemsLoaded extends ReturnsState {
  final List<Map<String, dynamic>> items;
  final String invoiceId;

  const ReturnableItemsLoaded({
    required this.items,
    required this.invoiceId,
  });

  @override
  List<Object?> get props => [items, invoiceId];
}

class ReturnNumberGenerated extends ReturnsState {
  final String returnNumber;

  const ReturnNumberGenerated(this.returnNumber);

  @override
  List<Object?> get props => [returnNumber];
}

class ReturnsError extends ReturnsState {
  final String message;

  const ReturnsError(this.message);

  @override
  List<Object?> get props => [message];
}
