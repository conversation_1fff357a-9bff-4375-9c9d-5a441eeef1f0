import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/services/enhanced_auth_service.dart';
import '../../data/repositories/tasks_repository.dart';
import '../cubit/tasks_cubit.dart';
import '../widgets/task_details_dialog.dart';

class TasksPage extends StatelessWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const TasksPage({
    super.key,
    required this.isVisible,
    required this.hasBeenVisited,
  });

  @override
  Widget build(BuildContext context) {
    final currentUserId = EnhancedAuthService.currentUserId ?? '';
    debugPrint('🔍 TasksPage: Current user ID = $currentUserId');
    
    return BlocProvider(
      create: (context) => TasksCubit(TasksRepository())
        ..loadEmployeeTasks(currentUserId),
      child: const TasksView(),
    );
  }
}

class TasksView extends StatefulWidget {
  const TasksView({super.key});

  @override
  State<TasksView> createState() => _TasksViewState();
}

class _TasksViewState extends State<TasksView> with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          switch (_tabController.index) {
            case 0:
              _selectedFilter = 'all';
              break;
            case 1:
              _selectedFilter = 'pending';
              break;
            case 2:
              _selectedFilter = 'in_progress';
              break;
            case 3:
              _selectedFilter = 'completed';
              break;
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'مهامي',
        // إزالة زر إعادة التحميل
      ),
      body: Column(
        children: [
          // Header with stats
          _buildStatsHeader(),
          
          // Tabs
          _buildTabBar(),
          
          // Tasks content
          Expanded(
            child: BlocBuilder<TasksCubit, TasksState>(
              builder: (context, state) {
                if (state is TasksLoading) {
                  return const LoadingWidget();
                }
                
                if (state is TasksError) {
                  return _buildErrorState(state.message);
                }
                
                if (state is TasksLoaded) {
                  final filteredTasks = _filterTasks(state.tasks);
                  
                  if (filteredTasks.isEmpty) {
                    return _buildEmptyState();
                  }
                  
                  return RefreshIndicator(
                    onRefresh: () async {
                      final userId = EnhancedAuthService.currentUserId;
                      if (userId != null) {
                        await context.read<TasksCubit>().loadEmployeeTasks(userId);
                      }
                    },
                    child: ListView.builder(
                      padding: EdgeInsets.all(16.w),
                      itemCount: filteredTasks.length,
                      itemBuilder: (context, index) {
                        final task = filteredTasks[index];
                        return _buildModernTaskCard(context, task);
                      },
                    ),
                  );
                }
                
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsHeader() {
    return BlocBuilder<TasksCubit, TasksState>(
      builder: (context, state) {
        if (state is TasksLoaded) {
          final allTasks = state.tasks;
          final pendingCount = allTasks.where((t) => t['status'] == 'pending').length;
          final inProgressCount = allTasks.where((t) => t['status'] == 'in_progress').length;
          final completedCount = allTasks.where((t) => t['status'] == 'completed').length;
          
          return Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'المجموع',
                    allTasks.length.toString(),
                    Icons.assignment,
                    AppColors.white,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40.h,
                  color: AppColors.white.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildStatItem(
                    'في الانتظار',
                    pendingCount.toString(),
                    Icons.pending,
                    Colors.orange,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40.h,
                  color: AppColors.white.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildStatItem(
                    'قيد التنفيذ',
                    inProgressCount.toString(),
                    Icons.hourglass_empty,
                    Colors.blue,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40.h,
                  color: AppColors.white.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildStatItem(
                    'مكتملة',
                    completedCount.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildStatItem(String label, String count, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24.sp,
        ),
        SizedBox(height: 8.h),
        Text(
          count,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(12.r),
        ),
        indicatorPadding: EdgeInsets.all(4.w),
        labelColor: AppColors.white,
        unselectedLabelColor: AppColors.gray600,
        labelStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          Tab(text: 'الكل'),
          Tab(text: 'في الانتظار'),
          Tab(text: 'قيد التنفيذ'),
          Tab(text: 'مكتملة'),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _filterTasks(List<Map<String, dynamic>> tasks) {
    if (_selectedFilter == 'all') {
      return tasks;
    }
    return tasks.where((task) => task['status'] == _selectedFilter).toList();
  }

  Widget _buildErrorState(String message) {
    String userMessage = 'حدث خطأ في تحميل المهام';
    if (message.contains('column') && message.contains('does not exist')) {
      userMessage = 'خطأ في قاعدة البيانات. يرجى التواصل مع الدعم الفني';
    } else if (message.contains('network') || message.contains('connection')) {
      userMessage = 'خطأ في الاتصال. تحقق من الإنترنت وحاول مرة أخرى';
    } else if (message.contains('permission') || message.contains('unauthorized')) {
      userMessage = 'ليس لديك صلاحية للوصول لهذه المهام';
    }
    
    return CustomErrorWidget(
      message: userMessage,
      onRetry: () {
        final userId = EnhancedAuthService.currentUserId;
        if (userId != null) {
          context.read<TasksCubit>().loadEmployeeTasks(userId);
        }
      },
    );
  }

  Widget _buildEmptyState() {
    String emptyMessage = 'لا توجد مهام';
    String emptySubMessage = 'لم يتم تعيين أي مهام لك حتى الآن';
    
    switch (_selectedFilter) {
      case 'pending':
        emptyMessage = 'لا توجد مهام في الانتظار';
        emptySubMessage = 'جميع المهام إما قيد التنفيذ أو مكتملة';
        break;
      case 'in_progress':
        emptyMessage = 'لا توجد مهام قيد التنفيذ';
        emptySubMessage = 'ابدأ العمل على المهام المعلقة';
        break;
      case 'completed':
        emptyMessage = 'لا توجد مهام مكتملة';
        emptySubMessage = 'أكمل بعض المهام لتظهر هنا';
        break;
    }
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120.w,
            height: 120.w,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.task_alt,
              size: 60.sp,
              color: AppColors.primary.withValues(alpha: 0.5),
            ),
          ),
          SizedBox(height: 24.h),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.gray700,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            emptySubMessage,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.gray500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildModernTaskCard(BuildContext context, Map<String, dynamic> task) {
    final title = task['title'] as String? ?? 'مهمة بدون عنوان';
    final description = task['description'] as String? ?? '';
    final status = task['status'] as String? ?? 'pending';
    final priority = task['priority'] as String? ?? 'medium';
    final dueDate = task['due_date'] as String?;
    final assignedBy = task['assigned_by_name'] as String? ?? 'غير محدد';

    // تحديد لون الحالة
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (status) {
      case 'completed':
        statusColor = Colors.green;
        statusText = 'مكتملة';
        statusIcon = Icons.check_circle;
        break;
      case 'in_progress':
        statusColor = Colors.blue;
        statusText = 'قيد التنفيذ';
        statusIcon = Icons.hourglass_empty;
        break;
      case 'pending':
      default:
        statusColor = Colors.orange;
        statusText = 'في الانتظار';
        statusIcon = Icons.pending;
        break;
    }

    // تحديد لون الأولوية
    Color priorityColor;
    String priorityText;

    switch (priority) {
      case 'high':
        priorityColor = Colors.red;
        priorityText = 'عالية';
        break;
      case 'low':
        priorityColor = Colors.green;
        priorityText = 'منخفضة';
        break;
      case 'medium':
      default:
        priorityColor = Colors.orange;
        priorityText = 'متوسطة';
        break;
    }

    // تحديد إذا كانت المهمة متأخرة
    bool isOverdue = false;
    if (dueDate != null && status != 'completed') {
      try {
        final due = DateTime.parse(dueDate);
        isOverdue = DateTime.now().isAfter(due);
      } catch (e) {
        // ignore
      }
    }

    return GestureDetector(
      onTap: () {
        _showTaskDetails(context, task);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: AppColors.gray300.withValues(alpha: 0.2),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: isOverdue 
                ? Colors.red.withValues(alpha: 0.3)
                : statusColor.withValues(alpha: 0.2),
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            // Header with gradient
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    statusColor.withValues(alpha: 0.1),
                    statusColor.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
              ),
              child: Row(
                children: [
                  // Status badge
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(20.r),
                      boxShadow: [
                        BoxShadow(
                          color: statusColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          statusIcon,
                          size: 16.sp,
                          color: AppColors.white,
                        ),
                        SizedBox(width: 6.w),
                        Text(
                          statusText,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(width: 12.w),
                  
                  // Priority badge
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: priorityColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: priorityColor.withValues(alpha: 0.4),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.flag,
                          size: 14.sp,
                          color: priorityColor,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          priorityText,
                          style: TextStyle(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.w600,
                            color: priorityColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Overdue indicator
                  if (isOverdue)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: Colors.red.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'متأخرة',
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.red,
                        ),
                      ),
                    ),
                  
                  SizedBox(width: 8.w),
                  
                  // Action menu
                  if (status != 'completed')
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'mark_completed') {
                          _markTaskAsCompleted(context, task['id']);
                        } else if (value == 'mark_in_progress') {
                          _markTaskAsInProgress(context, task['id']);
                        }
                      },
                      itemBuilder: (context) => [
                        if (status != 'in_progress')
                          const PopupMenuItem(
                            value: 'mark_in_progress',
                            child: Row(
                              children: [
                                Icon(Icons.play_arrow, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('بدء التنفيذ'),
                              ],
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'mark_completed',
                          child: Row(
                            children: [
                              Icon(Icons.check, color: Colors.green),
                              SizedBox(width: 8),
                              Text('تم الإنجاز'),
                            ],
                          ),
                        ),
                      ],
                      child: Container(
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          color: AppColors.gray100,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Icon(
                          Icons.more_vert,
                          color: AppColors.gray600,
                          size: 18.sp,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            // Content
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Task title
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gray900,
                      height: 1.3,
                    ),
                  ),

                  if (description.isNotEmpty) ...[
                    SizedBox(height: 8.h),
                    Text(
                      description.length > 100 
                          ? '${description.substring(0, 100)}...'
                          : description,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.gray700,
                        height: 1.4,
                      ),
                    ),
                  ],

                  SizedBox(height: 16.h),

                  // Task details
                  Row(
                    children: [
                      // Assigned by
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(6.w),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Icon(
                                Icons.person,
                                size: 16.sp,
                                color: AppColors.primary,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'مُسند من',
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      color: AppColors.gray500,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    assignedBy,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.gray700,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      if (dueDate != null) ...[
                        SizedBox(width: 16.w),
                        // Due date
                        Expanded(
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(6.w),
                                decoration: BoxDecoration(
                                  color: isOverdue 
                                      ? Colors.red.withValues(alpha: 0.1)
                                      : AppColors.warning.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Icon(
                                  Icons.schedule,
                                  size: 16.sp,
                                  color: isOverdue ? Colors.red : AppColors.warning,
                                ),
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'موعد التسليم',
                                      style: TextStyle(
                                        fontSize: 10.sp,
                                        color: AppColors.gray500,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      _formatDate(dueDate),
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: isOverdue ? Colors.red : AppColors.gray700,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                  
                  SizedBox(height: 12.h),
                  
                  // Tap to view details
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.visibility,
                          size: 16.sp,
                          color: AppColors.primary,
                        ),
                        SizedBox(width: 6.w),
                        Text(
                          'اضغط لعرض التفاصيل',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showTaskDetails(BuildContext context, Map<String, dynamic> task) {
    showDialog(
      context: context,
      builder: (context) => TaskDetailsDialog(
        task: task,
        onStatusChanged: (taskId, newStatus) {
          context.read<TasksCubit>().updateTaskStatus(taskId, newStatus);
        },
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  void _markTaskAsCompleted(BuildContext context, String taskId) {
    context.read<TasksCubit>().updateTaskStatus(taskId, 'completed');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث حالة المهمة إلى مكتملة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _markTaskAsInProgress(BuildContext context, String taskId) {
    context.read<TasksCubit>().updateTaskStatus(taskId, 'in_progress');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث حالة المهمة إلى قيد التنفيذ'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}