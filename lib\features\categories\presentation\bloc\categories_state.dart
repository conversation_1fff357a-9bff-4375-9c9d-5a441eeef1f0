import 'package:equatable/equatable.dart';
import '../../../../core/models/category_model.dart';

abstract class CategoriesState extends Equatable {
  const CategoriesState();

  @override
  List<Object?> get props => [];
}

class CategoriesInitial extends CategoriesState {}

class CategoriesLoading extends CategoriesState {}

class CategoriesLoaded extends CategoriesState {
  final List<CategoryModel> categories;
  final List<CategoryModel> filteredCategories;
  final String searchQuery;

  const CategoriesLoaded({
    required this.categories,
    required this.filteredCategories,
    this.searchQuery = '',
  });

  CategoriesLoaded copyWith({
    List<CategoryModel>? categories,
    List<CategoryModel>? filteredCategories,
    String? searchQuery,
  }) {
    return CategoriesLoaded(
      categories: categories ?? this.categories,
      filteredCategories: filteredCategories ?? this.filteredCategories,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [categories, filteredCategories, searchQuery];
}

class CategoriesError extends CategoriesState {
  final String message;

  const CategoriesError({required this.message});

  @override
  List<Object?> get props => [message];
}

class CategoryCreated extends CategoriesState {
  final CategoryModel category;

  const CategoryCreated({required this.category});

  @override
  List<Object?> get props => [category];
}

class CategoryUpdated extends CategoriesState {
  final CategoryModel category;

  const CategoryUpdated({required this.category});

  @override
  List<Object?> get props => [category];
}

class CategoryDeleted extends CategoriesState {
  final String categoryId;

  const CategoryDeleted({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class CategoryStatusToggled extends CategoriesState {
  final CategoryModel category;

  const CategoryStatusToggled({required this.category});

  @override
  List<Object?> get props => [category];
}

class CategoryProductsCountLoaded extends CategoriesState {
  final String categoryId;
  final int count;

  const CategoryProductsCountLoaded({
    required this.categoryId,
    required this.count,
  });

  @override
  List<Object?> get props => [categoryId, count];
}
