import 'package:equatable/equatable.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../../../../core/models/invoice_item_model.dart';
import '../../../../core/models/payment_model.dart';

abstract class SalesEvent extends Equatable {
  const SalesEvent();

  @override
  List<Object?> get props => [];
}

// Invoice Events
class LoadInvoices extends SalesEvent {
  final int page;
  final String? searchQuery;
  final PaymentStatus? paymentStatus;
  final InvoiceStatus? status;
  final PaymentType? paymentType;
  final DateTime? dateFilter;

  const LoadInvoices({
    this.page = 1,
    this.searchQuery,
    this.paymentStatus,
    this.status,
    this.paymentType,
    this.dateFilter,
  });

  @override
  List<Object?> get props => [page, searchQuery, paymentStatus, status, paymentType, dateFilter];
}

class LoadMoreInvoices extends SalesEvent {}

class RefreshInvoices extends SalesEvent {}

class CreateInvoice extends SalesEvent {
  final SalesInvoiceModel invoice;
  final List<InvoiceItemModel> items;

  const CreateInvoice({
    required this.invoice,
    required this.items,
  });

  @override
  List<Object?> get props => [invoice, items];
}

class UpdateInvoice extends SalesEvent {
  final SalesInvoiceModel invoice;
  final List<InvoiceItemModel> items;

  const UpdateInvoice({
    required this.invoice,
    required this.items,
  });

  @override
  List<Object?> get props => [invoice, items];
}

class LoadInvoiceDetails extends SalesEvent {
  final String invoiceId;

  const LoadInvoiceDetails(this.invoiceId);

  @override
  List<Object?> get props => [invoiceId];
}

class DeleteInvoice extends SalesEvent {
  final String invoiceId;

  const DeleteInvoice(this.invoiceId);

  @override
  List<Object?> get props => [invoiceId];
}

// Payment Events
class AddPayment extends SalesEvent {
  final PaymentModel payment;

  const AddPayment(this.payment);

  @override
  List<Object?> get props => [payment];
}

// Search and Filter Events
class SearchInvoices extends SalesEvent {
  final String query;

  const SearchInvoices(this.query);

  @override
  List<Object?> get props => [query];
}

class FilterInvoicesByPaymentStatus extends SalesEvent {
  final PaymentStatus? paymentStatus;

  const FilterInvoicesByPaymentStatus(this.paymentStatus);

  @override
  List<Object?> get props => [paymentStatus];
}

class FilterInvoicesByStatus extends SalesEvent {
  final InvoiceStatus? status;

  const FilterInvoicesByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

class FilterInvoicesByPaymentType extends SalesEvent {
  final PaymentType? paymentType;

  const FilterInvoicesByPaymentType(this.paymentType);

  @override
  List<Object?> get props => [paymentType];
}

class ClearFilters extends SalesEvent {}

// Generate Numbers Events
class GenerateInvoiceNumber extends SalesEvent {}

class GeneratePaymentNumber extends SalesEvent {}
