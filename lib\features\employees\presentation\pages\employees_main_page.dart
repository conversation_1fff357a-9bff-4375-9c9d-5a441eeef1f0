import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/permission_wrapper.dart';
import '../bloc/employees_bloc.dart';
import '../bloc/employee_tasks_bloc.dart';
import 'employees_page.dart';
import 'employee_tasks_page.dart';
import 'employee_commissions_page.dart';
import 'specializations_page.dart';

class EmployeesMainPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const EmployeesMainPage({
    super.key,
    required this.isVisible,
    this.hasBeenVisited = false,
  });

  @override
  State<EmployeesMainPage> createState() => _EmployeesMainPageState();
}

class _EmployeesMainPageState extends State<EmployeesMainPage>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Load employees when page initializes
    if (widget.isVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<EmployeesBloc>().add(LoadAllEmployees());
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return PermissionWrapper(
      permissionKey: 'employees',
      featureName: 'إدارة الموظفين',
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: const Text('إدارة الموظفين'),
          backgroundColor: AppColors.white,
          elevation: 0,
          bottom: TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.primary,
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            tabs: const [
              Tab(
                icon: Icon(Icons.people),
                text: 'الموظفين',
              ),
              Tab(
                icon: Icon(Icons.task_alt),
                text: 'مهام الموظفين',
              ),
              Tab(
                icon: Icon(Icons.monetization_on),
                text: 'العمولات والمبيعات',
              ),
              Tab(
                icon: Icon(Icons.medical_services),
                text: 'التخصصات',
              ),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            // Tab 1: Employees Management
            const EmployeesPage(),

            // Tab 2: Employee Tasks
            BlocProvider(
              create: (context) => EmployeeTasksBloc(),
              child: const EmployeeTasksPage(),
            ),

            // Tab 3: Employee Commissions and Sales
            const EmployeeCommissionsPage(),

            // Tab 4: Specializations Management
            const SpecializationsPage(),
          ],
        ),
      ),
    );
  }
}
