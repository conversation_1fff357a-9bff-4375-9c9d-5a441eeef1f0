import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/weekly_results_repository.dart';
import 'weekly_results_event.dart';
import 'weekly_results_state.dart';

class WeeklyResultsBloc extends Bloc<WeeklyResultsEvent, WeeklyResultsState> {
  final WeeklyResultsRepository _weeklyResultsRepository;

  WeeklyResultsBloc({required WeeklyResultsRepository weeklyResultsRepository})
      : _weeklyResultsRepository = weeklyResultsRepository,
        super(const WeeklyResultsInitial()) {
    on<LoadWeeklyResultsByPatientId>(_onLoadWeeklyResultsByPatientId);
    on<LoadRecentWeeklyResults>(_onLoadRecentWeeklyResults);
    on<LoadWeeklyResultsByDateRange>(_onLoadWeeklyResultsByDateRange);
    on<AddWeeklyResult>(_onAddWeeklyResult);
    on<UpdateWeeklyResult>(_onUpdateWeeklyResult);
    on<DeleteWeeklyResult>(_onDeleteWeeklyResult);
    on<CheckResultForDate>(_onCheckResultForDate);
    on<LoadWeightProgress>(_onLoadWeightProgress);
    on<LoadBodyCompositionProgress>(_onLoadBodyCompositionProgress);
    on<RefreshWeeklyResults>(_onRefreshWeeklyResults);
  }

  Future<void> _onLoadWeeklyResultsByPatientId(
    LoadWeeklyResultsByPatientId event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Loading weekly results for patient: ${event.patientId}');
    emit(const WeeklyResultsLoading());
    try {
      final weeklyResults = await _weeklyResultsRepository.getWeeklyResultsByPatientId(event.patientId);
      final latestResult = await _weeklyResultsRepository.getLatestWeeklyResult(event.patientId);

      debugPrint('📊 WeeklyResultsBloc: Loaded ${weeklyResults.length} weekly results');

      emit(WeeklyResultsLoaded(
        weeklyResults: weeklyResults,
        patientId: event.patientId,
        latestResult: latestResult,
      ));
      debugPrint('✅ WeeklyResultsBloc: Successfully emitted WeeklyResultsLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ WeeklyResultsBloc Error: $e');
      debugPrint('📍 WeeklyResultsBloc Stack trace: $stackTrace');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onLoadRecentWeeklyResults(
    LoadRecentWeeklyResults event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Loading recent weekly results for patient: ${event.patientId}');
    emit(const WeeklyResultsLoading());
    try {
      final weeklyResults = await _weeklyResultsRepository.getRecentWeeklyResults(
        event.patientId,
        weeks: event.weeks,
      );
      final latestResult = weeklyResults.isNotEmpty ? weeklyResults.first : null;

      debugPrint('📊 WeeklyResultsBloc: Loaded ${weeklyResults.length} recent weekly results');

      emit(WeeklyResultsLoaded(
        weeklyResults: weeklyResults,
        patientId: event.patientId,
        latestResult: latestResult,
      ));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error loading recent results: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onLoadWeeklyResultsByDateRange(
    LoadWeeklyResultsByDateRange event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Loading weekly results by date range');
    emit(const WeeklyResultsLoading());
    try {
      final weeklyResults = await _weeklyResultsRepository.getWeeklyResultsByDateRange(
        event.patientId,
        event.startDate,
        event.endDate,
      );

      debugPrint('📊 WeeklyResultsBloc: Loaded ${weeklyResults.length} results in date range');

      emit(WeeklyResultsByDateRangeLoaded(
        weeklyResults: weeklyResults,
        patientId: event.patientId,
        startDate: event.startDate,
        endDate: event.endDate,
      ));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error loading by date range: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onAddWeeklyResult(
    AddWeeklyResult event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Adding new weekly result');
    try {
      final newWeeklyResult = await _weeklyResultsRepository.addWeeklyResult(event.weeklyResult);
      debugPrint('✅ WeeklyResultsBloc: Successfully added weekly result: ${newWeeklyResult.id}');

      emit(WeeklyResultCreated(weeklyResult: newWeeklyResult));

      // Reload weekly results
      add(LoadWeeklyResultsByPatientId(patientId: event.weeklyResult.patientId));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error adding weekly result: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateWeeklyResult(
    UpdateWeeklyResult event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Updating weekly result');
    try {
      final updatedWeeklyResult = await _weeklyResultsRepository.updateWeeklyResult(event.weeklyResult);
      debugPrint('✅ WeeklyResultsBloc: Successfully updated weekly result: ${updatedWeeklyResult.id}');

      emit(WeeklyResultUpdated(weeklyResult: updatedWeeklyResult));

      // Reload weekly results
      add(LoadWeeklyResultsByPatientId(patientId: event.weeklyResult.patientId));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error updating weekly result: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onDeleteWeeklyResult(
    DeleteWeeklyResult event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Deleting weekly result');
    try {
      // Don't emit loading here as it's handled by UI
      await _weeklyResultsRepository.deleteWeeklyResult(event.resultId);
      debugPrint('✅ WeeklyResultsBloc: Successfully deleted weekly result: ${event.resultId}');

      emit(WeeklyResultDeleted(resultId: event.resultId));

      // Reload weekly results using patientId from event
      debugPrint('🔄 WeeklyResultsBloc: Reloading data after deletion for patient: ${event.patientId}');
      add(LoadWeeklyResultsByPatientId(patientId: event.patientId));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error deleting weekly result: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onCheckResultForDate(
    CheckResultForDate event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Checking if result exists for date');
    try {
      final exists = await _weeklyResultsRepository.hasResultForDate(
        event.patientId,
        event.date,
      );

      debugPrint('📊 WeeklyResultsBloc: Result exists for date: $exists');

      emit(ResultExistsForDate(exists: exists, date: event.date));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error checking result for date: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onLoadWeightProgress(
    LoadWeightProgress event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Loading weight progress');
    try {
      final weightProgress = await _weeklyResultsRepository.getWeightProgress(
        event.patientId,
        months: event.months,
      );

      debugPrint('📊 WeeklyResultsBloc: Loaded weight progress: ${weightProgress.length} points');

      emit(WeightProgressLoaded(
        weightProgress: weightProgress,
        patientId: event.patientId,
      ));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error loading weight progress: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onLoadBodyCompositionProgress(
    LoadBodyCompositionProgress event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Loading body composition progress');
    try {
      final bodyCompositionProgress = await _weeklyResultsRepository.getBodyCompositionProgress(
        event.patientId,
        months: event.months,
      );

      debugPrint('📊 WeeklyResultsBloc: Loaded body composition progress: ${bodyCompositionProgress.length} points');

      emit(BodyCompositionProgressLoaded(
        bodyCompositionProgress: bodyCompositionProgress,
        patientId: event.patientId,
      ));
    } catch (e) {
      debugPrint('❌ WeeklyResultsBloc Error loading body composition progress: $e');
      emit(WeeklyResultsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshWeeklyResults(
    RefreshWeeklyResults event,
    Emitter<WeeklyResultsState> emit,
  ) async {
    debugPrint('🔄 WeeklyResultsBloc: Refreshing weekly results');
    add(LoadWeeklyResultsByPatientId(patientId: event.patientId));
  }
}
