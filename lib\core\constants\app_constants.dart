class AppConstants {
  // App Info
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // Database Constants
  static const int adminIdLength = 10;
  static const int patientIdLength = 10;
  
  // Validation Constants
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxEmailLength = 100;
  static const int maxPhoneLength = 20;
  static const int minAge = 1;
  static const int maxAge = 120;
  static const double minWeight = 20.0;
  static const double maxWeight = 300.0;
  static const double minHeight = 100.0;
  static const double maxHeight = 250.0;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Network Constants
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxImageSizeInMB = 5;
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentExtensions = ['pdf', 'doc', 'docx'];
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-dd';
  static const String apiTimeFormat = 'HH:mm:ss';
  static const String apiDateTimeFormat = 'yyyy-MM-ddTHH:mm:ss.SSSZ';
  
  // Gender Options
  static const List<String> genderOptions = ['ذكر', 'أنثى'];
  static const Map<String, String> genderMapping = {
    'ذكر': 'male',
    'أنثى': 'female',
    'male': 'ذكر',
    'female': 'أنثى',
  };
  
  // Appointment Status
  static const List<String> appointmentStatuses = ['مجدول', 'مكتمل', 'ملغي'];
  static const Map<String, String> appointmentStatusMapping = {
    'مجدول': 'scheduled',
    'مكتمل': 'completed',
    'ملغي': 'cancelled',
    'scheduled': 'مجدول',
    'completed': 'مكتمل',
    'cancelled': 'ملغي',
  };
  
  // Product Categories
  static const List<String> productCategories = [
    'مكملات غذائية',
    'فيتامينات',
    'معادن',
    'أعشاب طبيعية',
    'منتجات تخسيس',
    'منتجات رياضية',
    'أخرى',
  ];
  
  // Article Categories
  static const List<String> articleCategories = [
    'التغذية العامة',
    'إنقاص الوزن',
    'زيادة الوزن',
    'التغذية الرياضية',
    'تغذية الأطفال',
    'تغذية كبار السن',
    'الأمراض المزمنة',
    'الحساسية الغذائية',
    'أخرى',
  ];
  
  // BMI Categories
  static const Map<String, Map<String, dynamic>> bmiCategories = {
    'نقص في الوزن': {
      'min': 0.0,
      'max': 18.5,
      'color': 'warning',
    },
    'وزن طبيعي': {
      'min': 18.5,
      'max': 25.0,
      'color': 'success',
    },
    'زيادة في الوزن': {
      'min': 25.0,
      'max': 30.0,
      'color': 'warning',
    },
    'سمنة': {
      'min': 30.0,
      'max': 100.0,
      'color': 'error',
    },
  };
  
  // Working Hours
  static const Map<String, String> workingHours = {
    'start': '09:00',
    'end': '17:00',
    'breakStart': '13:00',
    'breakEnd': '14:00',
  };
  
  // Appointment Duration (in minutes)
  static const int defaultAppointmentDuration = 30;
  static const List<int> appointmentDurations = [15, 30, 45, 60];
  
  // Days of Week
  static const List<String> daysOfWeek = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
  ];
  
  static const Map<String, int> dayToNumber = {
    'الأحد': 7,
    'الاثنين': 1,
    'الثلاثاء': 2,
    'الأربعاء': 3,
    'الخميس': 4,
    'الجمعة': 5,
    'السبت': 6,
  };
  
  // Months
  static const List<String> months = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];
  
  // Physical Activity Levels
  static const List<String> activityLevels = [
    'قليل الحركة',
    'نشاط خفيف',
    'نشاط متوسط',
    'نشاط عالي',
    'نشاط مكثف',
  ];
  
  // Medical Conditions (Common)
  static const List<String> commonMedicalConditions = [
    'السكري',
    'ضغط الدم المرتفع',
    'أمراض القلب',
    'الكوليسترول المرتفع',
    'أمراض الغدة الدرقية',
    'متلازمة الأيض',
    'مقاومة الأنسولين',
    'أمراض الكلى',
    'أمراض الكبد',
    'هشاشة العظام',
  ];
  
  // Common Allergies
  static const List<String> commonAllergies = [
    'الحليب ومنتجات الألبان',
    'البيض',
    'الفول السوداني',
    'المكسرات',
    'الأسماك',
    'المحار والقشريات',
    'القمح والجلوتين',
    'الصويا',
    'السمسم',
    'الفراولة',
  ];
  
  // Lab Test Types
  static const List<String> labTestTypes = [
    'تحليل دم شامل',
    'وظائف الكلى',
    'وظائف الكبد',
    'الدهون الثلاثية',
    'الكوليسترول',
    'السكر التراكمي',
    'فيتامين د',
    'فيتامين ب12',
    'الحديد',
    'الغدة الدرقية',
    'أخرى',
  ];
  
  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String noDataMessage = 'لا توجد بيانات';
  static const String loadingMessage = 'جاري التحميل...';
  
  // Success Messages
  static const String saveSuccessMessage = 'تم الحفظ بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String logoutSuccessMessage = 'تم تسجيل الخروج بنجاح';
}
