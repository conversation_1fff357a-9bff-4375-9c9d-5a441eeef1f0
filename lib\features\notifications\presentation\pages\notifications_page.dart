import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/services/notification_manager.dart';
import '../../../../core/services/permissions_manager.dart';
import '../../../../core/enums/user_role.dart';
import '../../../../core/models/notification_log_model.dart';
import '../../data/repositories/notifications_repository.dart';

class EnhancedNotificationsPage extends StatefulWidget {
  const EnhancedNotificationsPage({super.key});

  @override
  State<EnhancedNotificationsPage> createState() => _EnhancedNotificationsPageState();
}

class _EnhancedNotificationsPageState extends State<EnhancedNotificationsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final NotificationManager _notificationManager = NotificationManager();
  final PermissionsManager _permissionsManager = PermissionsManager();
  final NotificationsRepository _notificationsRepository = NotificationsRepository();

  List<NotificationLogModel> _notifications = [];
  bool _isLoading = false;
  int _unreadCount = 0;
  Map<String, int> _stats = {};

  @override
  void initState() {
    super.initState();
    
    final userRole = _permissionsManager.currentUserRole;
    
    // تحديد عدد التابات حسب دور المستخدم
    // للمدير: تابين (الإشعارات + الإرسال الجماعي)
    // للباقي: تاب واحد فقط (الإشعارات)
    final tabCount = (userRole == UserRole.admin || userRole == UserRole.superAdmin) ? 2 : 1;
    _tabController = TabController(length: tabCount, vsync: this);
    
    _loadNotifications();
    _loadUnreadCount();
    _loadStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notifications = await _notificationsRepository.getUserNotifications();
      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل الإشعارات: $e');
    }
  }

  Future<void> _loadUnreadCount() async {
    try {
      final count = await _notificationsRepository.getUnreadCount();
      setState(() {
        _unreadCount = count;
      });
    } catch (e) {
      // Silent fail for unread count
    }
  }

  Future<void> _loadStats() async {
    try {
      final stats = await _notificationsRepository.getNotificationsStats();
      setState(() {
        _stats = stats;
      });
    } catch (e) {
      // Silent fail for stats
    }
  }

  @override
  Widget build(BuildContext context) {
    final userRole = _permissionsManager.currentUserRole;
    final isAdmin = userRole == UserRole.admin || userRole == UserRole.superAdmin;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الإشعارات',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          if (_unreadCount > 0)
            TextButton.icon(
              onPressed: _markAllAsRead,
              icon: Icon(Icons.done_all, color: AppColors.white, size: 18.sp),
              label: Text(
                'قراءة الكل',
                style: TextStyle(color: AppColors.white, fontSize: 14.sp),
              ),
            ),
        ],
        bottom: isAdmin
            ? TabBar(
                controller: _tabController,
                labelColor: AppColors.white,
                unselectedLabelColor: AppColors.white.withValues(alpha: 0.7),
                indicatorColor: AppColors.white,
                tabs: const [
                  Tab(text: 'الإشعارات'),
                  Tab(text: 'إرسال جماعي'),
                ],
              )
            : null,
      ),
      body: isAdmin
          ? TabBarView(
              controller: _tabController,
              children: [
                _buildNotificationsTab(),
                _buildBroadcastTab(),
              ],
            )
          : _buildNotificationsTab(),
    );
  }

  Widget _buildNotificationsTab() {
    return RefreshIndicator(
      onRefresh: () async {
        await _loadNotifications();
        await _loadUnreadCount();
        await _loadStats();
      },
      child: Column(
        children: [
          // Statistics header
          if (_stats.isNotEmpty) _buildStatsHeader(),
          
          // Notifications list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _notifications.isEmpty
                    ? _buildEmptyNotifications()
                    : ListView.builder(
                        padding: EdgeInsets.all(16.w),
                        itemCount: _notifications.length,
                        itemBuilder: (context, index) {
                          final notification = _notifications[index];
                          return _buildNotificationCard(notification);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsHeader() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات الإشعارات',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'الإجمالي',
                  _stats['total']?.toString() ?? '0',
                  AppColors.primary,
                  Icons.notifications,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  'غير مقروءة',
                  _stats['unread']?.toString() ?? '0',
                  AppColors.error,
                  Icons.mark_email_unread,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  'الحجوزات',
                  _stats['appointment']?.toString() ?? '0',
                  AppColors.success,
                  Icons.calendar_today,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  'المهام',
                  _stats['task']?.toString() ?? '0',
                  AppColors.warning,
                  Icons.task,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String count, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16.w),
          SizedBox(height: 4.h),
          Text(
            count,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 9.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(NotificationLogModel notification) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: notification.isRead ? 1 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: notification.isRead
            ? BorderSide.none
            : BorderSide(color: AppColors.primary.withValues(alpha: 0.3), width: 1),
      ),
      child: InkWell(
        onTap: () => _markAsRead(notification),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getTypeColor(notification.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(
                        color: _getTypeColor(notification.type).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      notification.getTypeDisplayName(),
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: _getTypeColor(notification.type),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (!notification.isRead)
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                  SizedBox(width: 8.w),
                  Text(
                    notification.getFormattedDate(),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              Text(
                notification.title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                notification.message,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
              ),
              if (!notification.isRead) ...[
                SizedBox(height: 12.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => _markAsRead(notification),
                      icon: Icon(Icons.done, size: 16.sp, color: AppColors.primary),
                      label: Text(
                        'تم القراءة',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyNotifications() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64.sp,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBroadcastTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildBroadcastSection(),
        ],
      ),
    );
  }

  Widget _buildBroadcastSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.campaign,
                  color: Colors.orange,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إرسال إشعار جماعي',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            _buildBroadcastButton(
              'إشعار لجميع المستخدمين',
              Icons.people,
              () => _showBroadcastDialog('all'),
            ),
            
            SizedBox(height: 8.h),
            
            _buildBroadcastButton(
              'إشعار للأخصائيين',
              Icons.medical_services,
              () => _showBroadcastDialog('specialist'),
            ),
            
            SizedBox(height: 8.h),
            
            _buildBroadcastButton(
              'إشعار للريسبشنست',
              Icons.phone,
              () => _showBroadcastDialog('receptionist'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBroadcastButton(String title, IconData icon, VoidCallback onPressed) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 20.sp),
        label: Text(title),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.orange,
          side: BorderSide(color: Colors.orange),
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'appointment_cancelled':
        return AppColors.error;
      case 'appointment_confirmed':
        return AppColors.success;
      case 'appointment_rescheduled':
        return AppColors.warning;
      case 'appointment_status_changed':
        return AppColors.primary;
      case 'task_assigned':
        return Colors.blue;
      case 'task_completed':
        return AppColors.success;
      case 'general':
        return AppColors.primary;
      default:
        return AppColors.textSecondary;
    }
  }

  Future<void> _markAsRead(NotificationLogModel notification) async {
    if (notification.isRead) return;

    try {
      await _notificationsRepository.markAsRead(notification.id);
      setState(() {
        final index = _notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _notifications[index] = notification.copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
        _unreadCount = (_unreadCount - 1).clamp(0, _unreadCount);
      });
      await _loadStats(); // Refresh stats
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الإشعار: $e');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      await _notificationsRepository.markAllAsRead();
      setState(() {
        _notifications = _notifications.map((n) => n.copyWith(
          isRead: true,
          readAt: DateTime.now(),
        )).toList();
        _unreadCount = 0;
      });
      await _loadStats(); // Refresh stats
      _showSuccessSnackBar('تم تحديد جميع الإشعارات كمقروءة');
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الإشعارات: $e');
    }
  }

  void _showBroadcastDialog(String target) {
    final titleController = TextEditingController();
    final bodyController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إرسال إشعار جماعي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: InputDecoration(
                labelText: 'عنوان الإشعار',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: bodyController,
              decoration: InputDecoration(
                labelText: 'محتوى الإشعار',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sendBroadcastNotification(
                target,
                titleController.text,
                bodyController.text,
              );
            },
            child: Text('إرسال'),
          ),
        ],
      ),
    );
  }

  void _sendBroadcastNotification(String target, String title, String body) async {
    if (title.isEmpty || body.isEmpty) {
      _showErrorSnackBar('يرجى ملء جميع الحقول');
      return;
    }

    try {
      switch (target) {
        case 'all':
          await _notificationManager.broadcastNotification(
            title: title,
            body: body,
            type: 'general',
          );
          break;
        case 'specialist':
          await _notificationManager.notifySpecialists(
            title: title,
            body: body,
            type: 'general',
          );
          break;
        case 'receptionist':
          await _notificationManager.notifyReceptionists(
            title: title,
            body: body,
            type: 'general',
          );
          break;
      }

      _showSuccessSnackBar('تم إرسال الإشعار الجماعي بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في إر��ال الإشعار: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}