import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/services/task_notification_service.dart';
import '../../../../core/services/permissions_manager.dart';
import '../../../../core/utils/logger.dart';

class TaskCreationWidget extends StatefulWidget {
  final Function(Map<String, dynamic> taskData) onTaskCreated;

  const TaskCreationWidget({
    super.key,
    required this.onTaskCreated,
  });

  @override
  State<TaskCreationWidget> createState() => _TaskCreationWidgetState();
}

class _TaskCreationWidgetState extends State<TaskCreationWidget> {
  final Logger _logger = Logger('TaskCreationWidget');
  final TaskNotificationService _notificationService = TaskNotificationService();
  final PermissionsManager _permissionsManager = PermissionsManager();
  
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String? _selectedAssigneeId;
  String? _selectedPriority = 'medium';
  DateTime? _selectedDueDate;
  bool _isCreating = false;

  // Mock data - في التطبيق الحقيقي ستأتي من قاعدة البيانات
  final List<Map<String, dynamic>> _availableAssignees = [
    {'id': 'user1', 'name': 'د. أحمد محمد', 'role': 'specialist'},
    {'id': 'user2', 'name': 'أ. فاطمة علي', 'role': 'specialist'},
    {'id': 'user3', 'name': 'أ. محمد حسن', 'role': 'receptionist'},
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.add_task,
                    color: AppColors.primary,
                    size: 24.sp,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'إنشاء مهمة جديدة',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 20.h),
              
              // عنوان المهمة
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(
                  labelText: 'عنوان المهمة *',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عنوان المهمة';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // وصف المهمة
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: 'وصف المهمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              
              SizedBox(height: 16.h),
              
              // المكلف بالمهمة
              DropdownButtonFormField<String>(
                value: _selectedAssigneeId,
                decoration: InputDecoration(
                  labelText: 'المكلف بالمهمة *',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  prefixIcon: Icon(Icons.person_add),
                ),
                items: _availableAssignees.map((assignee) {
                  return DropdownMenuItem<String>(
                    value: assignee['id'],
                    child: Text('${assignee['name']} (${_getRoleDisplayName(assignee['role'])})'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedAssigneeId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار المكلف بالمهمة';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              Row(
                children: [
                  // الأولوية
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedPriority,
                      decoration: InputDecoration(
                        labelText: 'الأولوية',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        prefixIcon: Icon(Icons.priority_high),
                      ),
                      items: [
                        DropdownMenuItem(value: 'low', child: Text('منخفضة')),
                        DropdownMenuItem(value: 'medium', child: Text('متوسطة')),
                        DropdownMenuItem(value: 'high', child: Text('عالية')),
                        DropdownMenuItem(value: 'urgent', child: Text('عاجلة')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedPriority = value;
                        });
                      },
                    ),
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  // تاريخ الاستحقاق
                  Expanded(
                    child: InkWell(
                      onTap: _selectDueDate,
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'تاريخ الاستحقاق',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          _selectedDueDate != null
                              ? '${_selectedDueDate!.day}/${_selectedDueDate!.month}/${_selectedDueDate!.year}'
                              : 'اختر التاريخ',
                          style: TextStyle(
                            color: _selectedDueDate != null 
                                ? AppColors.textPrimary 
                                : AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 24.h),
              
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isCreating ? null : _resetForm,
                      child: Text('إعادة تعيين'),
                    ),
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isCreating ? null : _createTask,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: _isCreating
                          ? SizedBox(
                              height: 20.h,
                              width: 20.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text('إنشاء المهمة'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDueDate ?? DateTime.now().add(Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );
    
    if (picked != null) {
      final TimeOfDay? time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      
      if (time != null) {
        setState(() {
          _selectedDueDate = DateTime(
            picked.year,
            picked.month,
            picked.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  void _resetForm() {
    _titleController.clear();
    _descriptionController.clear();
    setState(() {
      _selectedAssigneeId = null;
      _selectedPriority = 'medium';
      _selectedDueDate = null;
    });
  }

  Future<void> _createTask() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isCreating = true;
    });

    try {
      final currentUser = _permissionsManager.currentUser;
      if (currentUser == null) {
        throw Exception('لم يتم العثور على بيانات المستخدم الحالي');
      }

      final assignee = _availableAssignees.firstWhere(
        (user) => user['id'] == _selectedAssigneeId,
      );

      // إنشاء معرف فريد للمهمة
      final taskId = DateTime.now().millisecondsSinceEpoch.toString();

      final taskData = {
        'id': taskId,
        'title': _titleController.text.trim(),
        'description': _descriptionController.text.trim(),
        'assignee_id': _selectedAssigneeId,
        'assignee_name': assignee['name'],
        'assigner_id': currentUser.id,
        'assigner_name': currentUser.name,
        'priority': _selectedPriority,
        'due_date': _selectedDueDate?.toIso8601String(),
        'status': 'pending',
        'created_at': DateTime.now().toIso8601String(),
      };

      _logger.info('📋 Creating new task: ${taskData['title']}');

      // حفظ المهمة في قاعدة البيانات (ستحتاج لتنفيذ هذا)
      // await _saveTaskToDatabase(taskData);

      // إرسال إشعار للمكلف بالمهمة
      await _notificationService.notifyNewTask(
        taskId: taskId,
        taskTitle: taskData['title']!,
        taskDescription: taskData['description']!,
        assigneeUserId: taskData['assignee_id']!,
        assignerUserId: taskData['assigner_id']!,
        assignedByName: taskData['assigner_name']!,
        assigneeName: taskData['assignee_name']!,
      );

      // استدعاء callback
      widget.onTaskCreated(taskData);

      // إعادة تعيين النموذج
      _resetForm();

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء المهمة وإرسال إشعار للمكلف بها'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      _logger.error('❌ Failed to create task: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء المهمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'specialist':
        return 'أخصائي';
      case 'receptionist':
        return 'ريسبشنست';
      case 'admin':
        return 'مدير';
      default:
        return role;
    }
  }
}