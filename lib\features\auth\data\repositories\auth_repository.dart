import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/admin_model.dart';

class AuthRepository {
  Future<AdminModel?> login(String email, String password) async {
    try {
      debugPrint('🔄 AuthRepository: Starting login process...');
      debugPrint('📧 AuthRepository: Email: $email');

      // تسجيل الدخول في Supabase Authentication أولاً
      debugPrint('🔐 AuthRepository: Authenticating with Supabase...');
      final authResponse = await SupabaseConfig.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      debugPrint('📥 AuthRepository: Auth response received');
      debugPrint('🆔 AuthRepository: Auth User ID: ${authResponse.user?.id}');

      if (authResponse.user == null) {
        debugPrint('❌ AuthRepository: Auth user is null');
        throw Exception('بيانات الدخول غير صحيحة');
      }

      // جلب بيانات الأدمن من جدول الأدمن باستخدام id (auth.uid)
      debugPrint('💾 AuthRepository: Fetching admin data from database...');
      final response = await SupabaseConfig.admins
          .select('''
            *,
            specializations!specialization_id(id, name)
          ''')
          .eq('id', authResponse.user!.id)
          .single();

      debugPrint('📥 AuthRepository: Database response: $response');

      final admin = AdminModel.fromJson(response);
      debugPrint('✅ AuthRepository: Login completed successfully');
      debugPrint('🎉 AuthRepository: Admin logged in - ID: ${admin.id}, Role: ${admin.role}, EmployeeType: ${admin.employeeType}');

      return admin;
    } catch (e, stackTrace) {
      debugPrint('❌ AuthRepository: Login failed');
      debugPrint('💥 AuthRepository: Error: $e');
      debugPrint('📍 AuthRepository: Stack trace: $stackTrace');
      throw Exception('فشل في تسجيل الدخول: ${e.toString()}');
    }
  }

  Future<AdminModel> register(String name, String email, String password) async {
    try {
      debugPrint('🔄 AuthRepository: Starting registration process...');
      debugPrint('📧 AuthRepository: Email: $email');
      debugPrint('👤 AuthRepository: Name: $name');

      // إنشاء حساب في Supabase Authentication أولاً
      debugPrint('🔐 AuthRepository: Creating auth account...');
      final authResponse = await SupabaseConfig.client.auth.signUp(
        email: email,
        password: password,
      );

      debugPrint('📥 AuthRepository: Auth response received');
      debugPrint('🆔 AuthRepository: Auth User ID: ${authResponse.user?.id}');
      debugPrint('📧 AuthRepository: User Email: ${authResponse.user?.email}');

      if (authResponse.user == null) {
        debugPrint('❌ AuthRepository: Auth user is null');
        throw Exception('فشل في إنشاء حساب المصادقة');
      }

      debugPrint('🔐 AuthRepository: Registration successful, user will be added to admins via trigger');

      // إضافة بيانات الأدمن لجدول الأدمن
      debugPrint('💾 AuthRepository: Inserting admin data to database...');
      final adminData = {
        'id': authResponse.user!.id, // Primary Key - UUID من Authentication
        'name': name,
        'email': email,
        'role': 'admin',
        'is_active': true,
      };
      debugPrint('📤 AuthRepository: Admin data: $adminData');

      final response = await SupabaseConfig.admins
          .insert(adminData)
          .select()
          .single();

      debugPrint('📥 AuthRepository: Database response: $response');

      final admin = AdminModel.fromJson(response);
      debugPrint('✅ AuthRepository: Registration completed successfully');
      debugPrint('🎉 AuthRepository: Admin created with ID: ${admin.id}');

      return admin;
    } catch (e, stackTrace) {
      debugPrint('❌ AuthRepository: Registration failed');
      debugPrint('💥 AuthRepository: Error: $e');
      debugPrint('📍 AuthRepository: Stack trace: $stackTrace');
      throw Exception('فشل في إنشاء الحساب: ${e.toString()}');
    }
  }

  Future<void> logout() async {
    try {
      await SupabaseConfig.client.auth.signOut();
    } catch (e) {
      throw Exception('فشل في تسجيل الخروج: ${e.toString()}');
    }
  }

  Future<bool> checkEmailExists(String email) async {
    try {
      final response = await SupabaseConfig.admins
          .select('id')
          .eq('email', email);

      return response.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  Future<AdminModel?> getAdminById(String adminId) async {
    try {
      final response = await SupabaseConfig.admins
          .select()
          .eq('id', adminId)
          .single();

      return AdminModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<AdminModel?> getCurrentUser() async {
    try {
      // Check if there's a current Supabase session
      final session = SupabaseConfig.client.auth.currentSession;
      if (session != null && session.user.email != null) {
        // Try to get admin by email from the session
        final response = await SupabaseConfig.admins
            .select()
            .eq('email', session.user.email!)
            .single();

        return AdminModel.fromJson(response);
      }
      return null;
    } catch (e) {
      return null;
    }
  }


}