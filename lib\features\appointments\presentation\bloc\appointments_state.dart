import 'package:equatable/equatable.dart';
import '../../../../core/models/appointment_model.dart';

abstract class AppointmentsState extends Equatable {
  const AppointmentsState();

  @override
  List<Object?> get props => [];
}

class AppointmentsInitial extends AppointmentsState {}

class AppointmentsLoading extends AppointmentsState {}

class AppointmentsLoaded extends AppointmentsState {
  final List<AppointmentModel> appointments;
  final List<AppointmentModel> todayAppointments;
  final List<AppointmentModel> upcomingAppointments;
  final DateTime selectedDate;

  const AppointmentsLoaded({
    required this.appointments,
    required this.todayAppointments,
    required this.upcomingAppointments,
    required this.selectedDate,
  });

  @override
  List<Object?> get props => [
        appointments,
        todayAppointments,
        upcomingAppointments,
        selectedDate,
      ];

  AppointmentsLoaded copyWith({
    List<AppointmentModel>? appointments,
    List<AppointmentModel>? todayAppointments,
    List<AppointmentModel>? upcomingAppointments,
    DateTime? selectedDate,
  }) {
    return AppointmentsLoaded(
      appointments: appointments ?? this.appointments,
      todayAppointments: todayAppointments ?? this.todayAppointments,
      upcomingAppointments: upcomingAppointments ?? this.upcomingAppointments,
      selectedDate: selectedDate ?? this.selectedDate,
    );
  }
}

class AppointmentsError extends AppointmentsState {
  final String message;

  const AppointmentsError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AppointmentCreated extends AppointmentsState {
  final AppointmentModel appointment;

  const AppointmentCreated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class AppointmentUpdated extends AppointmentsState {
  final AppointmentModel appointment;

  const AppointmentUpdated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class AppointmentDeleted extends AppointmentsState {
  final String appointmentId;

  const AppointmentDeleted({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

class AppointmentStatusUpdated extends AppointmentsState {
  final AppointmentModel appointment;

  const AppointmentStatusUpdated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}
