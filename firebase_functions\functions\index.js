const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { createClient } = require('@supabase/supabase-js');

// Initialize Firebase Admin
admin.initializeApp();

// Initialize Supabase
const supabaseUrl = 'https://xwxeauofbzedfzaogzzy.supabase.co';
const supabaseKey = 'YOUR_SUPABASE_SERVICE_KEY'; // استبدل بالمفتاح الحقيقي
const supabase = createClient(supabaseUrl, supabaseKey);

// Cloud Function تعمل كل دقيقة
exports.sendScheduledNotifications = functions.pubsub
  .schedule('every 1 minutes')
  .timeZone('Asia/Riyadh') // توقيت السعودية
  .onRun(async (context) => {
    console.log('🔍 Checking for due notifications...');
    
    try {
      // Get current time in HH:mm format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const currentDayOfWeek = now.getDay() === 0 ? 7 : now.getDay(); // Convert Sunday from 0 to 7
      
      console.log(`Current time: ${currentTime}, Day: ${currentDayOfWeek}`);
      
      // Get due notifications from Supabase
      const { data: notifications, error } = await supabase
        .from('scheduled_notifications')
        .select('*')
        .eq('is_active', true)
        .eq('scheduled_time', currentTime);
      
      if (error) {
        console.error('❌ Error fetching notifications:', error);
        return;
      }
      
      // Filter by day of week
      const dueNotifications = notifications.filter(notification => {
        const daysOfWeek = notification.days_of_week || [];
        return daysOfWeek.includes(currentDayOfWeek);
      });
      
      console.log(`📊 Found ${dueNotifications.length} due notifications`);
      
      // Send each notification
      for (const notification of dueNotifications) {
        await sendNotificationToPatient(notification);
      }
      
      if (dueNotifications.length > 0) {
        console.log(`✅ Processed ${dueNotifications.length} notifications`);
      }
      
    } catch (error) {
      console.error('❌ Error in scheduled notifications:', error);
    }
  });

// Function to send notification to a patient
async function sendNotificationToPatient(notification) {
  try {
    const patientId = notification.patient_id;
    
    // Get patient's auth_id
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single();
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`);
      return;
    }
    
    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true);
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId}`);
      return;
    }
    
    // Prepare notification message
    const title = getNotificationTitle(notification.notification_type);
    const body = `مرحباً ${patient.name}، ${notification.body}`;
    
    // Send to all tokens
    for (const tokenData of tokens) {
      const message = {
        token: tokenData.fcm_token,
        notification: {
          title: title,
          body: body,
        },
        data: {
          type: notification.notification_type,
          patient_id: patientId,
          reminder_id: notification.reminder_id || '',
          scheduled_notification_id: notification.id,
        },
        android: {
          priority: 'high',
          notification: {
            channel_id: 'diet_rx_notifications',
            sound: 'default',
            priority: 'high',
          },
        },
        apns: {
          headers: {
            'apns-priority': '10',
          },
          payload: {
            aps: {
              alert: {
                title: title,
                body: body,
              },
              sound: 'default',
              badge: 1,
            },
          },
        },
      };
      
      try {
        const response = await admin.messaging().send(message);
        console.log(`✅ Notification sent to ${tokenData.fcm_token}: ${response}`);
        
        // Log to Supabase
        await logNotification({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'sent',
          firebase_response: { messageId: response },
        });
        
      } catch (sendError) {
        console.error(`❌ Failed to send to ${tokenData.fcm_token}:`, sendError);
        
        // Log error to Supabase
        await logNotification({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'failed',
          error_message: sendError.message,
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error sending notification to patient:', error);
  }
}

// Helper function to get notification title
function getNotificationTitle(notificationType) {
  switch (notificationType) {
    case 'meal':
      return '🍽️ تذكير الوجبة';
    case 'exercise':
      return '🏃‍♂️ تذكير النشاط البدني';
    case 'medication':
      return '💊 تذكير الدواء';
    case 'water':
      return '💧 تذكير شرب الماء';
    default:
      return '🔔 تذكير من Diet Rx';
  }
}

// Helper function to log notifications
async function logNotification(logData) {
  try {
    await supabase.from('notification_logs').insert(logData);
  } catch (error) {
    console.error('❌ Error logging notification:', error);
  }
}

// Test function (يمكن استدعاؤها يدوياً)
exports.testNotification = functions.https.onRequest(async (req, res) => {
  try {
    const patientId = req.query.patientId || '**********';
    
    const testNotification = {
      id: 'test-notification',
      patient_id: patientId,
      notification_type: 'test',
      title: 'اختبار الإشعارات',
      body: 'هذا إشعار تجريبي من Firebase Cloud Functions',
      reminder_id: null,
    };
    
    await sendNotificationToPatient(testNotification);
    
    res.json({ 
      success: true, 
      message: 'Test notification sent',
      patientId: patientId 
    });
    
  } catch (error) {
    console.error('❌ Test notification error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});
