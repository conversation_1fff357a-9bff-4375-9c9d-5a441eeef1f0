import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/specialization_model.dart';

class SpecializationsRepository {
  // Get all specializations
  static Future<List<SpecializationModel>> getAllSpecializations() async {
    try {
      debugPrint('🔍 SpecializationsRepository: Loading all specializations...');
      
      final response = await SupabaseConfig.client
          .from('specializations')
          .select()
          .order('display_order', ascending: true);

      debugPrint('📊 SpecializationsRepository: Raw response length: ${response.length}');

      final specializations = response
          .map((json) => SpecializationModel.fromJson(json))
          .toList();

      debugPrint('✅ SpecializationsRepository: Successfully loaded ${specializations.length} specializations');
      return specializations;
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error loading specializations: $e');
      throw Exception('فشل في جلب التخصصات: $e');
    }
  }

  // Get active specializations only
  static Future<List<SpecializationModel>> getActiveSpecializations() async {
    try {
      debugPrint('🔍 SpecializationsRepository: Loading active specializations...');
      
      final response = await SupabaseConfig.client
          .from('specializations')
          .select()
          .eq('is_active', true)
          .order('display_order', ascending: true);

      final specializations = response
          .map((json) => SpecializationModel.fromJson(json))
          .toList();

      debugPrint('✅ SpecializationsRepository: Successfully loaded ${specializations.length} active specializations');
      return specializations;
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error loading active specializations: $e');
      throw Exception('فشل في جلب التخصصات النشطة: $e');
    }
  }

  // Get specialization by ID
  static Future<SpecializationModel?> getSpecializationById(String id) async {
    try {
      debugPrint('🔍 SpecializationsRepository: Loading specialization by ID: $id');
      
      final response = await SupabaseConfig.client
          .from('specializations')
          .select()
          .eq('id', id)
          .maybeSingle();

      if (response == null) {
        debugPrint('⚠️ SpecializationsRepository: Specialization not found');
        return null;
      }

      final specialization = SpecializationModel.fromJson(response);
      debugPrint('✅ SpecializationsRepository: Successfully loaded specialization');
      return specialization;
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error loading specialization: $e');
      throw Exception('فشل في جلب التخصص: $e');
    }
  }

  // Add new specialization
  static Future<SpecializationModel> addSpecialization(SpecializationModel specialization) async {
    try {
      debugPrint('🔍 SpecializationsRepository: Adding new specialization: ${specialization.name}');
      
      final response = await SupabaseConfig.client
          .from('specializations')
          .insert(specialization.toJson())
          .select()
          .single();

      final newSpecialization = SpecializationModel.fromJson(response);
      debugPrint('✅ SpecializationsRepository: Successfully added specialization');
      
      return newSpecialization;
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error adding specialization: $e');
      throw Exception('فشل في إضافة التخصص: $e');
    }
  }

  // Update specialization
  static Future<SpecializationModel> updateSpecialization(SpecializationModel specialization) async {
    try {
      debugPrint('🔍 SpecializationsRepository: Updating specialization: ${specialization.name}');
      
      final response = await SupabaseConfig.client
          .from('specializations')
          .update(specialization.toJson())
          .eq('id', specialization.id)
          .select()
          .single();

      final updatedSpecialization = SpecializationModel.fromJson(response);
      debugPrint('✅ SpecializationsRepository: Successfully updated specialization');
      
      return updatedSpecialization;
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error updating specialization: $e');
      throw Exception('فشل في تحديث التخصص: $e');
    }
  }

  // Delete specialization
  static Future<void> deleteSpecialization(String specializationId) async {
    try {
      debugPrint('🔍 SpecializationsRepository: Deleting specialization: $specializationId');
      
      await SupabaseConfig.client
          .from('specializations')
          .delete()
          .eq('id', specializationId);

      debugPrint('✅ SpecializationsRepository: Successfully deleted specialization');
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error deleting specialization: $e');
      throw Exception('فشل في حذف التخصص: $e');
    }
  }

  // Toggle specialization active status
  static Future<SpecializationModel> toggleSpecializationStatus(String specializationId, bool isActive) async {
    try {
      debugPrint('🔍 SpecializationsRepository: Toggling specialization status: $specializationId to $isActive');
      
      final response = await SupabaseConfig.client
          .from('specializations')
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', specializationId)
          .select()
          .single();

      final updatedSpecialization = SpecializationModel.fromJson(response);
      debugPrint('✅ SpecializationsRepository: Successfully toggled specialization status');
      
      return updatedSpecialization;
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error toggling specialization status: $e');
      throw Exception('فشل في تغيير حالة التخصص: $e');
    }
  }

  // Update display order
  static Future<void> updateDisplayOrder(List<SpecializationModel> specializations) async {
    try {
      debugPrint('🔍 SpecializationsRepository: Updating display order for ${specializations.length} specializations');
      
      for (int i = 0; i < specializations.length; i++) {
        await SupabaseConfig.client
            .from('specializations')
            .update({
              'display_order': i + 1,
              'updated_at': DateTime.now().toIso8601String()
            })
            .eq('id', specializations[i].id);
      }

      debugPrint('✅ SpecializationsRepository: Successfully updated display order');
    } catch (e) {
      debugPrint('❌ SpecializationsRepository: Error updating display order: $e');
      throw Exception('فشل في تحديث ترتيب التخصصات: $e');
    }
  }
}
