import 'package:equatable/equatable.dart';
import '../../../../core/models/expense_model.dart';

abstract class ExpenseEvent extends Equatable {
  const ExpenseEvent();

  @override
  List<Object?> get props => [];
}

class LoadExpenses extends ExpenseEvent {
  final int page;
  final String? searchQuery;
  final ExpenseCategory? category;
  final DateTime? dateFilter;

  const LoadExpenses({
    this.page = 1,
    this.searchQuery,
    this.category,
    this.dateFilter,
  });

  @override
  List<Object?> get props => [page, searchQuery, category, dateFilter];
}

class CreateExpense extends ExpenseEvent {
  final ExpenseModel expense;

  const CreateExpense({required this.expense});

  @override
  List<Object?> get props => [expense];
}

class UpdateExpense extends ExpenseEvent {
  final ExpenseModel expense;

  const UpdateExpense({required this.expense});

  @override
  List<Object?> get props => [expense];
}

class DeleteExpense extends ExpenseEvent {
  final String expenseId;

  const DeleteExpense({required this.expenseId});

  @override
  List<Object?> get props => [expenseId];
}

class SearchExpenses extends ExpenseEvent {
  final String query;

  const SearchExpenses(this.query);

  @override
  List<Object?> get props => [query];
}

class FilterExpensesByCategory extends ExpenseEvent {
  final ExpenseCategory? category;

  const FilterExpensesByCategory(this.category);

  @override
  List<Object?> get props => [category];
}

class ClearExpenseFilters extends ExpenseEvent {
  const ClearExpenseFilters();
}
