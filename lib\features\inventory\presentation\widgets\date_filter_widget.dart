import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';

class DateFilterWidget extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final Function(DateTime startDate, DateTime endDate) onDateRangeChanged;
  final VoidCallback? onRefresh;

  const DateFilterWidget({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onDateRangeChanged,
    this.onRefresh,
  });

  @override
  State<DateFilterWidget> createState() => _DateFilterWidgetState();
}

class _DateFilterWidgetState extends State<DateFilterWidget> {
  String _selectedFilter = 'current_month';

  final List<Map<String, dynamic>> _filterOptions = [
    {
      'key': 'today',
      'title': 'اليوم',
      'icon': Icons.today,
    },
    {
      'key': 'current_month',
      'title': 'الشهر الحالي',
      'icon': Icons.calendar_month,
    },
    {
      'key': 'last_month',
      'title': 'الشهر الماضي',
      'icon': Icons.calendar_today,
    },
    {
      'key': 'current_year',
      'title': 'السنة الحالية',
      'icon': Icons.calendar_today,
    },
    {
      'key': 'last_week',
      'title': 'آخر 7 أيام',
      'icon': Icons.calendar_view_week,
    },
    {
      'key': 'last_30_days',
      'title': 'آخر 30 يوم',
      'icon': Icons.date_range,
    },
    {
      'key': 'custom',
      'title': 'فترة مخصصة',
      'icon': Icons.edit_calendar,
    },
  ];

  @override
  void initState() {
    super.initState();
    _detectCurrentFilter();
  }

  void _detectCurrentFilter() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final currentMonthStart = DateTime(now.year, now.month, 1);
    final currentMonthEnd = DateTime(now.year, now.month + 1, 0);
    
    final startDay = DateTime(widget.startDate.year, widget.startDate.month, widget.startDate.day);
    final endDay = DateTime(widget.endDate.year, widget.endDate.month, widget.endDate.day);
    
    if (startDay == today && endDay == today) {
      _selectedFilter = 'today';
    } else if (startDay == currentMonthStart && endDay.year == currentMonthEnd.year && 
               endDay.month == currentMonthEnd.month && endDay.day == currentMonthEnd.day) {
      _selectedFilter = 'current_month';
    } else {
      _selectedFilter = 'custom';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عرض الفترة الحالية
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primary.withValues(alpha: 0.1),
                AppColors.primary.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Icon(
                  Icons.date_range,
                  color: AppColors.primary,
                  size: 16.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الفترة المحددة',
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primary.withValues(alpha: 0.8),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'من ${DateFormat('dd/MM/yyyy').format(widget.startDate)} إلى ${DateFormat('dd/MM/yyyy').format(widget.endDate)}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        SizedBox(height: 16.h),
        
        // خيارات الفلترة
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: _filterOptions.map((option) {
            final isSelected = _selectedFilter == option['key'];
            return GestureDetector(
              onTap: () => _onFilterSelected(option['key']),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: EdgeInsets.symmetric(
                  horizontal: 14.w,
                  vertical: 10.h,
                ),
                decoration: BoxDecoration(
                  gradient: isSelected 
                      ? LinearGradient(
                          colors: [
                            AppColors.primary,
                            AppColors.primary.withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                  color: isSelected ? null : AppColors.gray50,
                  borderRadius: BorderRadius.circular(25.r),
                  border: Border.all(
                    color: isSelected 
                        ? AppColors.primary 
                        : AppColors.gray300,
                    width: isSelected ? 2 : 1,
                  ),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      option['icon'],
                      color: isSelected 
                          ? AppColors.white 
                          : AppColors.gray600,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      option['title'],
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                        color: isSelected 
                            ? AppColors.white 
                            : AppColors.gray700,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _onFilterSelected(String filterKey) {
    setState(() {
      _selectedFilter = filterKey;
    });

    final now = DateTime.now();
    DateTime startDate;
    DateTime endDate;

    switch (filterKey) {
      case 'today':
        startDate = DateTime(now.year, now.month, now.day, 0, 0, 0);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
        
      case 'current_month':
        startDate = DateTime(now.year, now.month, 1);
        endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      
      case 'last_month':
        final lastMonth = DateTime(now.year, now.month - 1, 1);
        startDate = DateTime(lastMonth.year, lastMonth.month, 1);
        endDate = DateTime(lastMonth.year, lastMonth.month + 1, 0, 23, 59, 59);
        break;
      
      case 'current_year':
        startDate = DateTime(now.year, 1, 1);
        endDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      
      case 'last_week':
        startDate = DateTime(now.year, now.month, now.day - 7);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      
      case 'last_30_days':
        startDate = DateTime(now.year, now.month, now.day - 30);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      
      case 'custom':
        _showCustomDatePicker();
        return;
      
      default:
        startDate = DateTime(now.year, now.month, 1);
        endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
    }

    widget.onDateRangeChanged(startDate, endDate);
  }

  void _showCustomDatePicker() async {
    final now = DateTime.now();
    final firstDate = DateTime(2020);
    final lastDate = DateTime(now.year, now.month, now.day);
    
    // التأكد من أن التاريخ الابتدائي صحيح
    DateTime initialStart = widget.startDate;
    DateTime initialEnd = widget.endDate;
    
    // التأكد من أن التواريخ ضمن النطاق المسموح
    if (initialStart.isBefore(firstDate)) {
      initialStart = firstDate;
    }
    if (initialEnd.isAfter(lastDate)) {
      initialEnd = lastDate;
    }
    if (initialStart.isAfter(initialEnd)) {
      initialStart = DateTime(lastDate.year, lastDate.month, 1);
      initialEnd = lastDate;
    }

    try {
      final DateTimeRange? picked = await showDateRangePicker(
        context: context,
        firstDate: firstDate,
        lastDate: lastDate,
        initialDateRange: DateTimeRange(
          start: initialStart,
          end: initialEnd,
        ),
        locale: const Locale('ar'),
        helpText: 'اختر الفترة الزمنية',
        cancelText: 'إلغاء',
        confirmText: 'تأكيد',
        saveText: 'حفظ',
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                primary: AppColors.primary,
                onPrimary: AppColors.white,
                surface: AppColors.white,
                onSurface: AppColors.gray900,
              ),
            ),
            child: child!,
          );
        },
      );

      if (picked != null) {
        final startDate = DateTime(
          picked.start.year,
          picked.start.month,
          picked.start.day,
          0, 0, 0,
        );
        final endDate = DateTime(
          picked.end.year,
          picked.end.month,
          picked.end.day,
          23, 59, 59,
        );
        
        widget.onDateRangeChanged(startDate, endDate);
      } else {
        // إذا ألغى المستخدم، ارجع للشهر الحالي
        setState(() {
          _selectedFilter = 'current_month';
        });
      }
    } catch (e) {
      // في حالة حدوث خطأ، ارجع للشهر الحالي
      setState(() {
        _selectedFilter = 'current_month';
      });
      
      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في اختيار التاريخ. تم الرجوع للشهر الحالي.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      // تطبيق الشهر الحالي
      final now = DateTime.now();
      final startDate = DateTime(now.year, now.month, 1);
      final endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      widget.onDateRangeChanged(startDate, endDate);
    }
  }
}