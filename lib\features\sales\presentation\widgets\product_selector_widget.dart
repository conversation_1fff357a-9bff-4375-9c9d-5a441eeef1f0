import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/product_model.dart';
import '../../../products/presentation/bloc/products_bloc.dart';
import '../../../products/presentation/bloc/products_event.dart';
import '../../../products/presentation/bloc/products_state.dart';

class ProductSelectorWidget extends StatefulWidget {
  final Function(ProductModel, int) onProductSelected;

  const ProductSelectorWidget({
    super.key,
    required this.onProductSelected,
  });

  @override
  State<ProductSelectorWidget> createState() => _ProductSelectorWidgetState();
}

class _ProductSelectorWidgetState extends State<ProductSelectorWidget> {
  final _searchController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  ProductModel? _selectedProduct;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    // Load products when widget initializes
    context.read<ProductsBloc>().add(LoadAllProducts());
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field
        _buildSearchField(),

        // Products list
        if (_isExpanded) _buildProductsList(),

        // Selected product and quantity
        if (_selectedProduct != null) _buildSelectedProductSection(),
      ],
    );
  }

  Widget _buildSearchField() {
    return TextFormField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'ابحث عن منتج...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _isExpanded
            ? IconButton(
                onPressed: () {
                  setState(() {
                    _isExpanded = false;
                    _searchController.clear();
                  });
                },
                icon: const Icon(Icons.close),
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
      onTap: () {
        if (!_isExpanded) {
          setState(() {
            _isExpanded = true;
          });
        }
      },
      onChanged: (query) {
        context.read<ProductsBloc>().add(SearchProducts(query: query));
      },
    );
  }

  Widget _buildProductsList() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      constraints: BoxConstraints(maxHeight: 200.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: BlocBuilder<ProductsBloc, ProductsState>(
        builder: (context, state) {
          if (state is ProductsLoading) {
            return Container(
              height: 100.h,
              alignment: Alignment.center,
              child: const CircularProgressIndicator(),
            );
          }

          if (state is ProductsError) {
            return Container(
              height: 100.h,
              alignment: Alignment.center,
              child: Text(
                'خطأ في تحميل المنتجات',
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: 14.sp,
                ),
              ),
            );
          }

          if (state is ProductsLoaded) {
            final products = state.filteredProducts ?? state.products;
            final availableProducts = products.where((p) => p.stock > 0).toList();

            if (availableProducts.isEmpty) {
              return Container(
                height: 100.h,
                alignment: Alignment.center,
                child: Text(
                  'لا توجد منتجات متاحة',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14.sp,
                  ),
                ),
              );
            }

            return ListView.separated(
              shrinkWrap: true,
              itemCount: availableProducts.length,
              separatorBuilder: (context, index) => Divider(
                height: 1.h,
                color: AppColors.border,
              ),
              itemBuilder: (context, index) {
                final product = availableProducts[index];
                return ListTile(
                  leading: Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: product.images.isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.network(
                              product.images.first.imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Icon(
                                Icons.inventory,
                                color: AppColors.primary,
                                size: 20.sp,
                              ),
                            ),
                          )
                        : Icon(
                            Icons.inventory,
                            color: AppColors.primary,
                            size: 20.sp,
                          ),
                  ),
                  title: Text(
                    product.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'كود: ${product.productCode}',
                        style: TextStyle(fontSize: 12.sp),
                      ),
                      Row(
                        children: [
                          Text(
                            '${product.effectivePrice.toStringAsFixed(0)} د.ا',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            'المتوفر: ${product.stock}',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  onTap: () {
                    setState(() {
                      _selectedProduct = product;
                      _isExpanded = false;
                      _searchController.clear();
                    });
                  },
                );
              },
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSelectedProductSection() {
    final product = _selectedProduct!;
    
    return Container(
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primary),
        borderRadius: BorderRadius.circular(12.r),
        color: AppColors.primary.withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50.w,
                height: 50.h,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: product.images.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: Image.network(
                          product.images.first.imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Icon(
                            Icons.inventory,
                            color: AppColors.primary,
                            size: 24.sp,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.inventory,
                        color: AppColors.primary,
                        size: 24.sp,
                      ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'كود: ${product.productCode}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '${product.effectivePrice.toStringAsFixed(0)} د.ا',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedProduct = null;
                    _quantityController.text = '1';
                  });
                },
                icon: Icon(
                  Icons.close,
                  color: AppColors.error,
                  size: 20.sp,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _quantityController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 8.h,
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال الكمية';
                    }
                    final quantity = int.tryParse(value!);
                    if (quantity == null || quantity <= 0) {
                      return 'كمية غير صحيحة';
                    }
                    if (quantity > product.stock) {
                      return 'الكمية أكبر من المتوفر';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(width: 12.w),
              ElevatedButton(
                onPressed: () {
                  final quantity = int.tryParse(_quantityController.text);
                  if (quantity != null && quantity > 0 && quantity <= product.stock) {
                    widget.onProductSelected(product, quantity);
                    setState(() {
                      _selectedProduct = null;
                      _quantityController.text = '1';
                    });
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: Text(
                  'إضافة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
