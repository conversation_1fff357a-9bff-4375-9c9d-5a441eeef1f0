import 'package:equatable/equatable.dart';
import 'invoice_item_model.dart';
import 'product_model.dart';

class ReturnItemModel extends Equatable {
  final String id;
  final String returnId;
  final String invoiceItemId;
  final InvoiceItemModel? invoiceItem;
  final String productId;
  final ProductModel? product;
  final String productName;
  final int quantityReturned;
  final double unitPrice;
  final double totalReturnAmount;
  final String? reason;
  final DateTime createdAt;

  const ReturnItemModel({
    required this.id,
    required this.returnId,
    required this.invoiceItemId,
    this.invoiceItem,
    required this.productId,
    this.product,
    required this.productName,
    required this.quantityReturned,
    required this.unitPrice,
    required this.totalReturnAmount,
    this.reason,
    required this.createdAt,
  });

  factory ReturnItemModel.fromJson(Map<String, dynamic> json) {
    return ReturnItemModel(
      id: json['id'] as String,
      returnId: json['return_id'] as String,
      invoiceItemId: json['invoice_item_id'] as String,
      invoiceItem: json['invoice_items'] != null 
          ? InvoiceItemModel.fromJson(json['invoice_items'] as Map<String, dynamic>)
          : null,
      productId: json['product_id'] as String,
      product: json['products'] != null 
          ? ProductModel.fromJson(json['products'] as Map<String, dynamic>)
          : null,
      productName: json['product_name'] as String,
      quantityReturned: json['quantity_returned'] as int? ?? 1,
      unitPrice: (json['unit_price'] as num?)?.toDouble() ?? 0.0,
      totalReturnAmount: (json['total_return_amount'] as num?)?.toDouble() ?? 0.0,
      reason: json['reason'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // Don't include 'id' when creating new return items, let database generate UUID
      'return_id': returnId,
      'invoice_item_id': invoiceItemId,
      'product_id': productId,
      'product_name': productName,
      'quantity_returned': quantityReturned,
      'unit_price': unitPrice,
      'total_return_amount': totalReturnAmount,
      'reason': reason,
      'created_at': createdAt.toIso8601String(),
    };
  }

  ReturnItemModel copyWith({
    String? id,
    String? returnId,
    String? invoiceItemId,
    InvoiceItemModel? invoiceItem,
    String? productId,
    ProductModel? product,
    String? productName,
    int? quantityReturned,
    double? unitPrice,
    double? totalReturnAmount,
    String? reason,
    DateTime? createdAt,
  }) {
    return ReturnItemModel(
      id: id ?? this.id,
      returnId: returnId ?? this.returnId,
      invoiceItemId: invoiceItemId ?? this.invoiceItemId,
      invoiceItem: invoiceItem ?? this.invoiceItem,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      productName: productName ?? this.productName,
      quantityReturned: quantityReturned ?? this.quantityReturned,
      unitPrice: unitPrice ?? this.unitPrice,
      totalReturnAmount: totalReturnAmount ?? this.totalReturnAmount,
      reason: reason ?? this.reason,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Helper methods
  double get calculatedTotalAmount => quantityReturned * unitPrice;

  @override
  List<Object?> get props => [
        id,
        returnId,
        invoiceItemId,
        invoiceItem,
        productId,
        product,
        productName,
        quantityReturned,
        unitPrice,
        totalReturnAmount,
        reason,
        createdAt,
      ];
}
