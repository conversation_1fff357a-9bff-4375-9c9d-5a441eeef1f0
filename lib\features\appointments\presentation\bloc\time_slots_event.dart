import 'package:equatable/equatable.dart';
import '../../../../core/models/time_slot_model.dart';

abstract class TimeSlotsEvent extends Equatable {
  const TimeSlotsEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllTimeSlots extends TimeSlotsEvent {}

class LoadActiveTimeSlots extends TimeSlotsEvent {}

class LoadTimeSlotsByDay extends TimeSlotsEvent {
  final int dayOfWeek;

  const LoadTimeSlotsByDay({required this.dayOfWeek});

  @override
  List<Object?> get props => [dayOfWeek];
}

class CreateTimeSlot extends TimeSlotsEvent {
  final TimeSlotModel timeSlot;

  const CreateTimeSlot({required this.timeSlot});

  @override
  List<Object?> get props => [timeSlot];
}

class UpdateTimeSlot extends TimeSlotsEvent {
  final TimeSlotModel timeSlot;

  const UpdateTimeSlot({required this.timeSlot});

  @override
  List<Object?> get props => [timeSlot];
}

class DeleteTimeSlot extends TimeSlotsEvent {
  final String timeSlotId;

  const DeleteTimeSlot({required this.timeSlotId});

  @override
  List<Object?> get props => [timeSlotId];
}

class ToggleTimeSlotStatus extends TimeSlotsEvent {
  final String timeSlotId;

  const ToggleTimeSlotStatus({required this.timeSlotId});

  @override
  List<Object?> get props => [timeSlotId];
}

class GenerateAppointments extends TimeSlotsEvent {
  final int weeksAhead;

  const GenerateAppointments({this.weeksAhead = 4});

  @override
  List<Object?> get props => [weeksAhead];
}

class RefreshTimeSlots extends TimeSlotsEvent {}
