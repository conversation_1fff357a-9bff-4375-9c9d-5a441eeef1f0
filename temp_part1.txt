﻿import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/services/permissions_manager.dart';
import '../../../../core/services/enhanced_auth_service.dart';
import '../../../../core/enums/user_role.dart';
import '../../../../core/network/supabase_client.dart';

class RoleBasedSettingsPage extends StatefulWidget {
  const RoleBasedSettingsPage({super.key});

  @override
  State<RoleBasedSettingsPage> createState() => _RoleBasedSettingsPageState();
}

class _RoleBasedSettingsPageState extends State<RoleBasedSettingsPage> {
  PaperSize _selectedPaperSize = PaperSize.mm80;
  String? _connectedPrinterName;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
      _selectedPaperSize = PaperSize.values[paperSizeIndex];
      _connectedPrinterName = prefs.getString('printer_name');
    });
  }

  Future<void> _savePaperSize(PaperSize paperSize) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('paper_size', paperSize.index);
    setState(() {
      _selectedPaperSize = paperSize;
    });
  }

  Future<void> _savePrinterInfo(String address, String name) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('printer_address', address);
    await prefs.setString('printer_name', name);
    setState(() {
      _connectedPrinterName = name;
    });
  }

  Future<void> _connectToPrinter() async {
    try {
      final device = await FlutterBluetoothPrinter.selectDevice(context);
      if (device != null) {
        final deviceName = device.name ?? 'O·OO"O1Oc OºUSO± U.O1O±U^U?Oc';
        await _savePrinterInfo(device.address, deviceName);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('OªU. OU,OOªOµOU, O"OU,O·OO"O1Oc: $deviceName'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OrO·O£ U?US OU,OOªOµOU, O"OU,O·OO"O1Oc: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _disconnectPrinter() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('printer_address');
    await prefs.remove('printer_name');
    setState(() {
      _connectedPrinterName = null;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('OªU. U,O·O1 OU,OOªOµOU, O"OU,O·OO"O1Oc'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _performLogout() async {
    try {
      // Clear permissions
      PermissionsManager().clearPermissions();
      
      // Sign out from auth service
      await EnhancedAuthService.signOut();

      // Clear shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      if (mounted) {
        // Navigate to login page and clear all previous routes
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OªU. OªO3O¬USU, OU,OrO±U^O¬ O"U+O¬OO-'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OrO·O£ U?US OªO3O¬USU, OU,OrO±U^O¬: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final permissionsManager = PermissionsManager();
    final currentUser = permissionsManager.currentUser;
    final userRole = permissionsManager.currentUserRole;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'OU,O¥O1O_OO_OOª',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Section - USO,U╪O± U,U,O¬U.USO1
            _buildProfileSection(currentUser, userRole),

            SizedBox(height: 24.h),

            // Account Settings Section - USO,U╪O± U,U,O¬U.USO1
            _buildAccountSettingsSection(),

            SizedBox(height: 24.h),

            // Printer Settings Section - U,U,U.O_USO± U?U,O·
            if (permissionsManager.canAccessAppSettings) ...[
              _buildPrinterSettingsSection(),
              SizedBox(height: 24.h),
            ],

            // Developer Credit - U?US OU,U+U╪OUSOc
            _buildDeveloperCredit(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(dynamic currentUser, UserRole? userRole) {
    final userName = currentUser?.name ?? 'OU,U.O3OªOrO_U.';
    final userEmail = currentUser?.email ?? '<EMAIL>';
    final roleDisplayName = userRole?.displayName ?? 'U.O3OªOrO_U.';

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary.withValues(alpha: 0.1),
                AppColors.primary.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Row(
              children: [
                // Profile Avatar
                CircleAvatar(
                  radius: 35.r,
                  backgroundColor: AppColors.primary,
                  child: Icon(
                    Icons.person,
                    size: 35.sp,
                    color: AppColors.white,
                  ),
                ),

                SizedBox(width: 16.w),

                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // User Name
                      Text(
                        userName,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),

                      SizedBox(height: 4.h),

                      // User Role
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          roleDisplayName,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                      ),

                      SizedBox(height: 6.h),

                      // User Email
                      Text(
                        userEmail,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAccountSettingsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_circle,
                  color: Colors.orange,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'O¥O1O_OO_OOª OU,O-O3OO"',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ),

          // Change Password - U,U,O¬U.USO1
          _buildSettingsTile(
            icon: Icons.lock_reset,
            title: 'OªOºUSUSO± UƒU,U.Oc OU,U.O±U^O±',
            subtitle: 'OªO-O_USO« UƒU,U.Oc U.O±U^O± OU,O-O3OO"',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showChangePasswordDialog();
            },
          ),

          Divider(height: 1),

          // Logout - U,U,O¬U.USO1
          _buildSettingsTile(
            icon: Icons.logout,
            title: 'OªO3O¬USU, OU,OrO±U^O¬',
            subtitle: 'OU,OrO±U^O¬ U.U+ OU,OªO·O"USU,',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showLogoutDialog();
            },
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPrinterSettingsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.print,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'O¥O1O_OO_OOª OU,O·OO"O1Oc',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          // Settings List
          _buildSettingsTile(
            icon: Icons.bluetooth,
            title: 'OOªOµOU, OU,O·OO"O1Oc',
            subtitle: _connectedPrinterName ?? 'OºUSO± U.OªOµU,',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: _showPrinterConnectionDialog,
          ),

          Divider(height: 1),

          _buildSettingsTile(
            icon: Icons.straighten,
            title: 'U.U,OO3 OU,U^O±U,',
            subtitle: _getPaperSizeName(_selectedPaperSize),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: _showPaperSizeDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildDeveloperCredit() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      margin: EdgeInsets.symmetric(vertical: 16.h),
      decoration: BoxDecoration(
        color: const Color(0xFF0c3c4c).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: const Color(0xFF0c3c4c).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Text(
        'Developed by khwasstech.com',
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFF0c3c4c),
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: isDestructive
              ? Colors.red.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : Colors.grey[700],
          size: 20.sp,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: isDestructive ? Colors.red : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.grey[600],
        ),
      ),
      trailing: trailing,
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
    );
  }

  void _showPrinterConnectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('O¥O1O_OO_OOª OU,O·OO"O1Oc'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_connectedPrinterName != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'U.OªOµU,',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'OU,O·OO"O1Oc: $_connectedPrinterName',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
            ] else ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'OºUSO± U.OªOµU,',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'U,U. USOªU. OU,OOªOµOU, O"O£US O·OO"O1Oc',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
            ],
          ],
        ),
        actions: [
          if (_connectedPrinterName != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _disconnectPrinter();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text('U,O·O1 OU,OOªOµOU,'),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('O¥U,OºOO¡'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _connectToPrinter();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            child: Text(_connectedPrinterName != null ? 'OªOºUSUSO± OU,O·OO"O1Oc' : 'OOªOµOU, O"O·OO"O1Oc'),
          ),
        ],
      ),
    );
  }

  void _showPaperSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('U.U,OO3 OU,U^O±U,'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PaperSize.values.map((paperSize) {
            return RadioListTile<PaperSize>(
              title: Text(_getPaperSizeName(paperSize)),
              subtitle: Text(_getPaperSizeDescription(paperSize)),
              value: paperSize,
              groupValue: _selectedPaperSize,
              onChanged: (value) {
                if (value != null) {
                  Navigator.of(context).pop();
                  _savePaperSize(value);
                }
              },
              activeColor: AppColors.primary,
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('O¥U,OºOO¡'),
          ),
        ],
      ),
    );
  }

