import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/employee_schedule_model.dart';

class EmployeeSchedulesRepository {
  // Get all schedules for an employee
  static Future<List<EmployeeScheduleModel>> getEmployeeSchedules(String employeeId) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Loading schedules for employee: $employeeId');
      
      final response = await SupabaseConfig.client
          .from('employee_schedules')
          .select()
          .eq('employee_id', employeeId)
          .eq('is_active', true)
          .order('day_of_week', ascending: true)
          .order('start_time', ascending: true);

      debugPrint('📊 EmployeeSchedulesRepository: Raw response length: ${response.length}');

      final schedules = response
          .map((json) => EmployeeScheduleModel.fromJson(json))
          .toList();

      debugPrint('✅ EmployeeSchedulesRepository: Successfully loaded ${schedules.length} schedules');
      return schedules;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error loading schedules: $e');
      throw Exception('فشل في جلب جداول الموظف: $e');
    }
  }

  // Get schedules for a specific day
  static Future<List<EmployeeScheduleModel>> getSchedulesByDay(String employeeId, int dayOfWeek) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Loading schedules for employee: $employeeId, day: $dayOfWeek');
      
      final response = await SupabaseConfig.client
          .from('employee_schedules')
          .select()
          .eq('employee_id', employeeId)
          .eq('day_of_week', dayOfWeek)
          .eq('is_active', true)
          .order('start_time', ascending: true);

      final schedules = response
          .map((json) => EmployeeScheduleModel.fromJson(json))
          .toList();

      debugPrint('✅ EmployeeSchedulesRepository: Successfully loaded ${schedules.length} schedules for day $dayOfWeek');
      return schedules;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error loading schedules by day: $e');
      throw Exception('فشل في جلب جداول اليوم: $e');
    }
  }

  // Get all active schedules for all employees
  static Future<List<EmployeeScheduleModel>> getAllActiveSchedules() async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Loading all active schedules...');
      
      final response = await SupabaseConfig.client
          .from('employee_schedules')
          .select()
          .eq('is_active', true)
          .order('day_of_week', ascending: true)
          .order('start_time', ascending: true);

      final schedules = response
          .map((json) => EmployeeScheduleModel.fromJson(json))
          .toList();

      debugPrint('✅ EmployeeSchedulesRepository: Successfully loaded ${schedules.length} active schedules');
      return schedules;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error loading all schedules: $e');
      throw Exception('فشل في جلب جميع الجداول: $e');
    }
  }

  // Add new schedule
  static Future<EmployeeScheduleModel> addSchedule(EmployeeScheduleModel schedule) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Adding new schedule for employee: ${schedule.employeeId}');
      
      final response = await SupabaseConfig.client
          .from('employee_schedules')
          .insert(schedule.toJson())
          .select()
          .single();

      final newSchedule = EmployeeScheduleModel.fromJson(response);
      debugPrint('✅ EmployeeSchedulesRepository: Successfully added schedule');
      
      return newSchedule;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error adding schedule: $e');
      throw Exception('فشل في إضافة الجدول: $e');
    }
  }

  // Update schedule
  static Future<EmployeeScheduleModel> updateSchedule(EmployeeScheduleModel schedule) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Updating schedule: ${schedule.id}');
      
      final response = await SupabaseConfig.client
          .from('employee_schedules')
          .update(schedule.toJson())
          .eq('id', schedule.id)
          .select()
          .single();

      final updatedSchedule = EmployeeScheduleModel.fromJson(response);
      debugPrint('✅ EmployeeSchedulesRepository: Successfully updated schedule');
      
      return updatedSchedule;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error updating schedule: $e');
      throw Exception('فشل في تحديث الجدول: $e');
    }
  }

  // Delete schedule
  static Future<void> deleteSchedule(String scheduleId) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Deleting schedule: $scheduleId');
      
      await SupabaseConfig.client
          .from('employee_schedules')
          .delete()
          .eq('id', scheduleId);

      debugPrint('✅ EmployeeSchedulesRepository: Successfully deleted schedule');
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error deleting schedule: $e');
      throw Exception('فشل في حذف الجدول: $e');
    }
  }

  // Toggle schedule active status
  static Future<EmployeeScheduleModel> toggleScheduleStatus(String scheduleId, bool isActive) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Toggling schedule status: $scheduleId to $isActive');
      
      final response = await SupabaseConfig.client
          .from('employee_schedules')
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', scheduleId)
          .select()
          .single();

      final updatedSchedule = EmployeeScheduleModel.fromJson(response);
      debugPrint('✅ EmployeeSchedulesRepository: Successfully toggled schedule status');
      
      return updatedSchedule;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error toggling schedule status: $e');
      throw Exception('فشل في تغيير حالة الجدول: $e');
    }
  }

  // Get available time slots for an employee on a specific date
  static Future<List<Map<String, dynamic>>> getAvailableTimeSlots(
    String employeeId,
    DateTime date,
  ) async {
    try {
      debugPrint('🔍 EmployeeSchedulesRepository: Getting available slots for employee: $employeeId on ${date.toIso8601String()}');
      
      final dayOfWeek = date.weekday % 7; // Convert to 0-6 format (0=Sunday)
      
      // Get employee schedules for this day
      final schedules = await getSchedulesByDay(employeeId, dayOfWeek);
      
      if (schedules.isEmpty) {
        debugPrint('⚠️ EmployeeSchedulesRepository: No schedules found for day $dayOfWeek');
        return [];
      }

      // Get existing appointments for this date
      final existingAppointments = await SupabaseConfig.appointments
          .select('appointment_time')
          .eq('employee_id', employeeId)
          .eq('appointment_date', date.toIso8601String().split('T')[0])
          .neq('status', 'cancelled');

      final bookedTimes = existingAppointments
          .map((apt) => apt['appointment_time'] as String)
          .toSet();

      // Generate available time slots
      List<Map<String, dynamic>> availableSlots = [];
      
      for (final schedule in schedules) {
        final startTime = _parseTime(schedule.startTime);
        final endTime = _parseTime(schedule.endTime);
        
        DateTime currentSlot = DateTime(
          date.year,
          date.month,
          date.day,
          startTime.hour,
          startTime.minute,
        );
        
        final endDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          endTime.hour,
          endTime.minute,
        );

        while (currentSlot.add(Duration(minutes: schedule.sessionDuration)).isBefore(endDateTime) ||
               currentSlot.add(Duration(minutes: schedule.sessionDuration)).isAtSameMomentAs(endDateTime)) {
          
          final timeString = '${currentSlot.hour.toString().padLeft(2, '0')}:${currentSlot.minute.toString().padLeft(2, '0')}';
          
          if (!bookedTimes.contains(timeString)) {
            availableSlots.add({
              'time': timeString,
              'datetime': currentSlot,
              'duration': schedule.sessionDuration,
              'max_patients': schedule.maxPatientsPerSession,
            });
          }
          
          currentSlot = currentSlot.add(Duration(
            minutes: schedule.sessionDuration + schedule.breakDuration,
          ));
        }
      }

      debugPrint('✅ EmployeeSchedulesRepository: Found ${availableSlots.length} available slots');
      return availableSlots;
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error getting available slots: $e');
      throw Exception('فشل في جلب المواعيد المتاحة: $e');
    }
  }

  static DateTime _parseTime(String time) {
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }

  // Check if employee is available at specific time
  static Future<bool> isEmployeeAvailable(
    String employeeId,
    DateTime dateTime,
  ) async {
    try {
      final availableSlots = await getAvailableTimeSlots(employeeId, dateTime);
      final timeString = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      
      return availableSlots.any((slot) => slot['time'] == timeString);
    } catch (e) {
      debugPrint('❌ EmployeeSchedulesRepository: Error checking availability: $e');
      return false;
    }
  }
}
