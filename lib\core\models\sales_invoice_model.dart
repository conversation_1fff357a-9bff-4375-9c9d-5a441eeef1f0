import 'package:equatable/equatable.dart';
import 'patient_model.dart';
import 'invoice_item_model.dart';
import 'payment_model.dart';
import 'admin_model.dart';

enum PaymentType { cash, installment }
enum PaymentStatus { partial, paid, returned }
enum InvoiceStatus { active, cancelled, returned }

extension PaymentTypeExtension on PaymentType {
  String get value {
    switch (this) {
      case PaymentType.cash:
        return 'cash';
      case PaymentType.installment:
        return 'installment';
    }
  }

  String get arabicName {
    switch (this) {
      case PaymentType.cash:
        return 'كاش';
      case PaymentType.installment:
        return 'قسط';
    }
  }

  static PaymentType fromString(String value) {
    switch (value) {
      case 'cash':
        return PaymentType.cash;
      case 'installment':
        return PaymentType.installment;
      default:
        return PaymentType.cash;
    }
  }
}

extension PaymentStatusExtension on PaymentStatus {
  String get value {
    switch (this) {
      case PaymentStatus.partial:
        return 'partial';
      case PaymentStatus.paid:
        return 'paid';
      case PaymentStatus.returned:
        return 'returned';
    }
  }

  String get arabicName {
    switch (this) {
      case PaymentStatus.partial:
        return 'مدفوع جزئياً';
      case PaymentStatus.paid:
        return 'مدفوع بالكامل';
      case PaymentStatus.returned:
        return 'مرتجع';
    }
  }

  static PaymentStatus fromString(String value) {
    switch (value) {
      case 'partial':
        return PaymentStatus.partial;
      case 'paid':
        return PaymentStatus.paid;
      case 'returned':
        return PaymentStatus.returned;
      default:
        return PaymentStatus.partial;
    }
  }

  /// Calculate payment status based on invoice conditions
  static PaymentStatus calculateStatus({
    required PaymentType paymentType,
    required InvoiceStatus invoiceStatus,
    required double remainingAmount,
  }) {
    // If invoice is returned, status is returned
    if (invoiceStatus == InvoiceStatus.returned) {
      return PaymentStatus.returned;
    }

    // If cash payment or installment with no remaining amount, status is paid
    if (paymentType == PaymentType.cash || remainingAmount <= 0) {
      return PaymentStatus.paid;
    }

    // If installment with remaining amount, status is partial
    return PaymentStatus.partial;
  }
}

extension InvoiceStatusExtension on InvoiceStatus {
  String get value {
    switch (this) {
      case InvoiceStatus.active:
        return 'active';
      case InvoiceStatus.cancelled:
        return 'cancelled';
      case InvoiceStatus.returned:
        return 'returned';
    }
  }

  String get arabicName {
    switch (this) {
      case InvoiceStatus.active:
        return 'نشطة';
      case InvoiceStatus.cancelled:
        return 'ملغية';
      case InvoiceStatus.returned:
        return 'مسترجعة';
    }
  }

  static InvoiceStatus fromString(String value) {
    switch (value) {
      case 'active':
        return InvoiceStatus.active;
      case 'cancelled':
        return InvoiceStatus.cancelled;
      case 'returned':
        return InvoiceStatus.returned;
      default:
        return InvoiceStatus.active;
    }
  }
}

class SalesInvoiceModel extends Equatable {
  final String id;
  final String invoiceNumber;
  final String patientId;
  final PatientModel? patient;
  final double totalAmount;
  final double discountPercentage;
  final double discountAmount;
  final double finalAmount;
  final bool isCommissionSale;
  final double commissionPercentage;
  final double commissionAmount;
  final double returnAmount;
  final String? returnReason;
  final DateTime? returnedAt;
  final String? returnedBy;
  final PaymentType paymentType;
  final PaymentStatus paymentStatus;
  final double paidAmount;
  final double remainingAmount;
  final DateTime? dueDate;
  final String? notes;
  final InvoiceStatus status;
  final String? createdBy;
  final String? specialistId; // ID الأخصائي اللي باع المنتج
  final AdminModel? specialist; // بيانات الأخصائي
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<InvoiceItemModel> items;
  final List<PaymentModel> payments;

  const SalesInvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.patientId,
    this.patient,
    required this.totalAmount,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    required this.finalAmount,
    this.isCommissionSale = false,
    this.commissionPercentage = 0.0,
    this.commissionAmount = 0.0,
    this.returnAmount = 0.0,
    this.returnReason,
    this.returnedAt,
    this.returnedBy,
    required this.paymentType,
    this.paymentStatus = PaymentStatus.partial,
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    this.dueDate,
    this.notes,
    this.status = InvoiceStatus.active,
    this.createdBy,
    this.specialistId,
    this.specialist,
    required this.createdAt,
    required this.updatedAt,
    this.items = const [],
    this.payments = const [],
  });

  factory SalesInvoiceModel.fromJson(Map<String, dynamic> json) {
    try {
      // Parse items safely
      List<InvoiceItemModel> items = [];
      if (json['invoice_items'] != null && json['invoice_items'] is List) {
        for (final itemJson in json['invoice_items'] as List) {
          try {
            if (itemJson is Map<String, dynamic>) {
              items.add(InvoiceItemModel.fromJson(itemJson));
            }
          } catch (e) {
            print('Error parsing invoice item: $e');
            // Skip this item and continue
          }
        }
      }

      // Parse payments safely
      List<PaymentModel> payments = [];
      if (json['payments'] != null && json['payments'] is List) {
        for (final paymentJson in json['payments'] as List) {
          try {
            if (paymentJson is Map<String, dynamic>) {
              payments.add(PaymentModel.fromJson(paymentJson));
            }
          } catch (e) {
            print('Error parsing payment: $e');
            // Skip this payment and continue
          }
        }
      }

      return SalesInvoiceModel(
        id: json['id']?.toString() ?? '',
        invoiceNumber: json['invoice_number']?.toString() ?? '',
        patientId: json['patient_id']?.toString() ?? '',
        patient: _parsePatientModel(json['patients']),
        totalAmount: _parseDouble(json['total_amount']),
        discountPercentage: _parseDouble(json['discount_percentage']),
        discountAmount: _parseDouble(json['discount_amount']),
        finalAmount: _parseDouble(json['final_amount']),
        isCommissionSale: json['is_commission_sale'] == true,
        commissionPercentage: _parseDouble(json['commission_percentage']),
        commissionAmount: _parseDouble(json['commission_amount']),
        returnAmount: _parseDouble(json['return_amount']),
        returnReason: json['return_reason']?.toString(),
        returnedAt: _parseDateTime(json['returned_at']),
        returnedBy: json['returned_by']?.toString(),
        paymentType: PaymentTypeExtension.fromString(json['payment_type']?.toString() ?? 'cash'),
        paymentStatus: PaymentStatusExtension.fromString(json['payment_status']?.toString() ?? 'pending'),
        paidAmount: _parseDouble(json['paid_amount']),
        remainingAmount: _parseDouble(json['remaining_amount']),
        dueDate: _parseDateTime(json['due_date']),
        notes: json['notes']?.toString(),
        status: InvoiceStatusExtension.fromString(json['status']?.toString() ?? 'active'),
        createdBy: json['created_by']?.toString(),
        specialistId: json['specialist_id']?.toString(),
        specialist: _parseAdminModel(json['admins']),
        createdAt: _parseDateTime(json['created_at']) ?? DateTime.now(),
        updatedAt: _parseDateTime(json['updated_at']) ?? DateTime.now(),
        items: items,
        payments: payments,
      );
    } catch (e) {
      print('Error parsing SalesInvoiceModel: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  // Helper method to safely parse double values
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  // Helper method to safely parse DateTime values
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String && value.isNotEmpty) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        print('Error parsing DateTime: $value');
        return null;
      }
    }
    return null;
  }

  // Helper method to safely parse AdminModel
  static AdminModel? _parseAdminModel(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) {
      try {
        return AdminModel.fromJson(value);
      } catch (e) {
        print('Error parsing AdminModel: $e');
        print('AdminModel JSON data: $value');
        return null;
      }
    }
    return null;
  }

  // Helper method to safely parse PatientModel
  static PatientModel? _parsePatientModel(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) {
      try {
        return PatientModel.fromJson(value);
      } catch (e) {
        print('Error parsing PatientModel: $e');
        print('PatientModel JSON data: $value');
        return null;
      }
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      // Don't include 'id' when creating new invoices, let database generate UUID
      'invoice_number': invoiceNumber,
      'patient_id': patientId,
      'total_amount': totalAmount,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'final_amount': finalAmount,
      'is_commission_sale': isCommissionSale,
      'commission_percentage': commissionPercentage,
      'commission_amount': commissionAmount,
      'return_amount': returnAmount,
      'return_reason': returnReason,
      'returned_at': returnedAt?.toIso8601String(),
      'returned_by': returnedBy,
      'payment_type': paymentType.value,
      'payment_status': paymentStatus.value,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'due_date': dueDate?.toIso8601String(),
      'notes': notes,
      'status': status.value,
      'created_by': createdBy,
      'specialist_id': specialistId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SalesInvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? patientId,
    PatientModel? patient,
    double? totalAmount,
    double? discountPercentage,
    double? discountAmount,
    double? finalAmount,
    bool? isCommissionSale,
    double? commissionPercentage,
    double? commissionAmount,
    double? returnAmount,
    String? returnReason,
    DateTime? returnedAt,
    String? returnedBy,
    PaymentType? paymentType,
    PaymentStatus? paymentStatus,
    double? paidAmount,
    double? remainingAmount,
    DateTime? dueDate,
    String? notes,
    InvoiceStatus? status,
    String? createdBy,
    String? specialistId,
    AdminModel? specialist,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<InvoiceItemModel>? items,
    List<PaymentModel>? payments,
  }) {
    return SalesInvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      patientId: patientId ?? this.patientId,
      patient: patient ?? this.patient,
      totalAmount: totalAmount ?? this.totalAmount,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      isCommissionSale: isCommissionSale ?? this.isCommissionSale,
      commissionPercentage: commissionPercentage ?? this.commissionPercentage,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      returnAmount: returnAmount ?? this.returnAmount,
      returnReason: returnReason ?? this.returnReason,
      returnedAt: returnedAt ?? this.returnedAt,
      returnedBy: returnedBy ?? this.returnedBy,
      paymentType: paymentType ?? this.paymentType,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      dueDate: dueDate ?? this.dueDate,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      specialistId: specialistId ?? this.specialistId,
      specialist: specialist ?? this.specialist,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
      payments: payments ?? this.payments,
    );
  }

  @override
  List<Object?> get props => [
        id,
        invoiceNumber,
        patientId,
        patient,
        totalAmount,
        discountPercentage,
        discountAmount,
        finalAmount,
        isCommissionSale,
        commissionPercentage,
        commissionAmount,
        returnAmount,
        returnReason,
        returnedAt,
        returnedBy,
        paymentType,
        paymentStatus,
        paidAmount,
        remainingAmount,
        dueDate,
        notes,
        status,
        createdBy,
        specialistId,
        specialist,
        createdAt,
        updatedAt,
        items,
        payments,
      ];
}