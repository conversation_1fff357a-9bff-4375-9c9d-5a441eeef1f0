import '../../../../core/models/notification_log_model.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/services/enhanced_auth_service.dart';

class NotificationsRepository {
  // Get notifications for current user
  Future<List<NotificationLogModel>> getUserNotifications({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final currentUserId = EnhancedAuthService.currentUserId;
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      final response = await SupabaseConfig.client
          .from('notifications_log')
          .select('*')
          .eq('user_id', currentUserId)
          .order('sent_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => NotificationLogModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch notifications: $e');
    }
  }

  // Get all notifications (admin only)
  Future<List<NotificationLogModel>> getAllNotifications({
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      final response = await SupabaseConfig.client
          .from('notifications_log')
          .select('*')
          .order('sent_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => NotificationLogModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch all notifications: $e');
    }
  }

  // Mark notification as read using the database function
  Future<void> markAsRead(String notificationId) async {
    try {
      await SupabaseConfig.client.rpc('mark_notification_as_read', params: {
        'notification_id': notificationId,
      });
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  // Mark all notifications as read for current user using the database function
  Future<void> markAllAsRead() async {
    try {
      final currentUserId = EnhancedAuthService.currentUserId;
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      await SupabaseConfig.client.rpc('mark_all_notifications_as_read', params: {
        'target_user_id': currentUserId,
      });
    } catch (e) {
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  // Get unread notifications count using the database function
  Future<int> getUnreadCount() async {
    try {
      final currentUserId = EnhancedAuthService.currentUserId;
      if (currentUserId == null) {
        return 0;
      }

      final response = await SupabaseConfig.client.rpc('get_unread_notifications_count', params: {
        'target_user_id': currentUserId,
      });

      return response as int? ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await SupabaseConfig.client
          .from('notifications_log')
          .delete()
          .eq('id', notificationId);
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  // Get notifications by type
  Future<List<NotificationLogModel>> getNotificationsByType({
    required String type,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final currentUserId = EnhancedAuthService.currentUserId;
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      final response = await SupabaseConfig.client
          .from('notifications_log')
          .select('*')
          .eq('user_id', currentUserId)
          .eq('type', type)
          .order('sent_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => NotificationLogModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch notifications by type: $e');
    }
  }

  // Get notifications statistics for current user
  Future<Map<String, int>> getNotificationsStats() async {
    try {
      final currentUserId = EnhancedAuthService.currentUserId;
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      final response = await SupabaseConfig.client
          .from('notifications_log')
          .select('type, is_read')
          .eq('user_id', currentUserId);

      final stats = <String, int>{
        'total': 0,
        'unread': 0,
        'appointment': 0,
        'task': 0,
        'general': 0,
      };

      for (final notification in response) {
        stats['total'] = (stats['total'] ?? 0) + 1;
        
        if (!(notification['is_read'] as bool? ?? false)) {
          stats['unread'] = (stats['unread'] ?? 0) + 1;
        }

        final type = notification['type'] as String;
        if (type.startsWith('appointment')) {
          stats['appointment'] = (stats['appointment'] ?? 0) + 1;
        } else if (type.startsWith('task')) {
          stats['task'] = (stats['task'] ?? 0) + 1;
        } else {
          stats['general'] = (stats['general'] ?? 0) + 1;
        }
      }

      return stats;
    } catch (e) {
      return {
        'total': 0,
        'unread': 0,
        'appointment': 0,
        'task': 0,
        'general': 0,
      };
    }
  }
}