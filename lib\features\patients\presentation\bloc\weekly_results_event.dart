import 'package:equatable/equatable.dart';
import '../../../../core/models/weekly_result_model.dart';

abstract class WeeklyResultsEvent extends Equatable {
  const WeeklyResultsEvent();

  @override
  List<Object?> get props => [];
}

class LoadWeeklyResultsByPatientId extends WeeklyResultsEvent {
  final String patientId;

  const LoadWeeklyResultsByPatientId({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadRecentWeeklyResults extends WeeklyResultsEvent {
  final String patientId;
  final int weeks;

  const LoadRecentWeeklyResults({
    required this.patientId,
    this.weeks = 12,
  });

  @override
  List<Object?> get props => [patientId, weeks];
}

class LoadWeeklyResultsByDateRange extends WeeklyResultsEvent {
  final String patientId;
  final DateTime startDate;
  final DateTime endDate;

  const LoadWeeklyResultsByDateRange({
    required this.patientId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [patientId, startDate, endDate];
}

class AddWeeklyResult extends WeeklyResultsEvent {
  final WeeklyResultModel weeklyResult;

  const AddWeeklyResult({required this.weeklyResult});

  @override
  List<Object?> get props => [weeklyResult];
}

class UpdateWeeklyResult extends WeeklyResultsEvent {
  final WeeklyResultModel weeklyResult;

  const UpdateWeeklyResult({required this.weeklyResult});

  @override
  List<Object?> get props => [weeklyResult];
}

class DeleteWeeklyResult extends WeeklyResultsEvent {
  final String resultId;
  final String patientId;

  const DeleteWeeklyResult({
    required this.resultId,
    required this.patientId,
  });

  @override
  List<Object?> get props => [resultId, patientId];
}

class CheckResultForDate extends WeeklyResultsEvent {
  final String patientId;
  final DateTime date;

  const CheckResultForDate({
    required this.patientId,
    required this.date,
  });

  @override
  List<Object?> get props => [patientId, date];
}

class LoadWeightProgress extends WeeklyResultsEvent {
  final String patientId;
  final int months;

  const LoadWeightProgress({
    required this.patientId,
    this.months = 6,
  });

  @override
  List<Object?> get props => [patientId, months];
}

class LoadBodyCompositionProgress extends WeeklyResultsEvent {
  final String patientId;
  final int months;

  const LoadBodyCompositionProgress({
    required this.patientId,
    this.months = 6,
  });

  @override
  List<Object?> get props => [patientId, months];
}

class RefreshWeeklyResults extends WeeklyResultsEvent {
  final String patientId;

  const RefreshWeeklyResults({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}
