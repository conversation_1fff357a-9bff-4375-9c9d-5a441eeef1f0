import '../utils/logger.dart';
import 'notification_manager.dart';
import '../network/supabase_client.dart';

class TaskNotificationService {
  static final TaskNotificationService _instance = TaskNotificationService._internal();
  factory TaskNotificationService() => _instance;
  TaskNotificationService._internal();

  final Logger _logger = Logger('TaskNotificationService');
  final NotificationManager _notificationManager = NotificationManager();

  // Send notification when new task is assigned
  Future<void> notifyNewTask({
    required String taskId,
    required String taskTitle,
    required String taskDescription,
    required String assigneeUserId,
    required String assignerUserId,
    required String assignedByName,
    required String assigneeName,
  }) async {
    try {
      _logger.info('📋 Sending new task notification for: $taskId');
      
      await _notificationManager.sendCustomNotification(
        userId: assigneeUserId,
        title: 'مهمة جديدة',
        body: 'تم تكليفك بمهمة جديدة: $taskTitle من قبل $assignedByName',
        type: 'task_assigned',
        data: {
          'task_id': taskId,
          'action': 'assigned',
          'task_title': taskTitle,
          'assigned_by': assignedByName,
        },
      );
      
      // Log the notification in database
      await _logNotification(
        userId: assigneeUserId,
        type: 'new_task',
        title: 'مهمة جديدة',
        message: 'تم تكليفك بمهمة جديدة: $taskTitle من قبل $assignedByName',
        relatedId: taskId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send new task notification: $e');
    }
  }

  // Send notification when task status changes
  Future<void> notifyTaskStatusChanged({
    required String taskId,
    required String taskTitle,
    required String assignerUserId,
    required String assigneeUserId,
    required String oldStatus,
    required String newStatus,
    required String changedByName,
    required String changedByUserId,
  }) async {
    try {
      _logger.info('📋 Sending task status changed notification for: $taskId');
      
      // Determine who to notify based on who made the change
      String targetUserId;
      if (changedByUserId == assigneeUserId) {
        // Assignee changed status, notify assigner
        targetUserId = assignerUserId;
      } else {
        // Assigner or admin changed status, notify assignee
        targetUserId = assigneeUserId;
      }

      String statusDisplayName = _getStatusDisplayName(newStatus);
      
      await _notificationManager.sendCustomNotification(
        userId: targetUserId,
        title: 'تم تحديث حالة المهمة',
        body: 'تم تغيير حالة المهمة "$taskTitle" إلى $statusDisplayName بواسطة $changedByName',
        type: 'task_status_changed',
        data: {
          'task_id': taskId,
          'action': 'status_changed',
          'task_title': taskTitle,
          'old_status': oldStatus,
          'new_status': newStatus,
          'changed_by': changedByName,
        },
      );
      
      // Log the notification in database
      await _logNotification(
        userId: targetUserId,
        type: 'task_status_changed',
        title: 'تم تحديث حالة المهمة',
        message: 'تم تغيير حالة المهمة "$taskTitle" إلى $statusDisplayName بواسطة $changedByName',
        relatedId: taskId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send task status changed notification: $e');
    }
  }

  // Send notification when task is completed
  Future<void> notifyTaskCompleted({
    required String taskId,
    required String taskTitle,
    required String assignerUserId,
    required String assigneeUserId,
    required String completedByName,
  }) async {
    try {
      _logger.info('📋 Sending task completed notification for: $taskId');
      
      await _notificationManager.notifyTaskCompleted(
        taskId: taskId,
        taskTitle: taskTitle,
        assignerUserId: assignerUserId,
        completedByName: completedByName,
      );
      
      // Log the notification in database
      await _logNotification(
        userId: assignerUserId,
        type: 'task_completed',
        title: 'تم إنجاز المهمة',
        message: 'تم إنجاز المهمة "$taskTitle" بواسطة $completedByName',
        relatedId: taskId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send task completed notification: $e');
    }
  }

  // Send notification when task is assigned to multiple users
  Future<void> notifyMultipleUsersNewTask({
    required String taskId,
    required String taskTitle,
    required String taskDescription,
    required List<String> assigneeUserIds,
    required String assignerUserId,
    required String assignedByName,
  }) async {
    try {
      _logger.info('📋 Sending new task notification to multiple users for: $taskId');
      
      for (String assigneeUserId in assigneeUserIds) {
        await notifyNewTask(
          taskId: taskId,
          taskTitle: taskTitle,
          taskDescription: taskDescription,
          assigneeUserId: assigneeUserId,
          assignerUserId: assignerUserId,
          assignedByName: assignedByName,
          assigneeName: '', // Will be fetched if needed
        );
      }
      
    } catch (e) {
      _logger.error('❌ Failed to send new task notifications to multiple users: $e');
    }
  }

  // Send notification when task is overdue
  Future<void> notifyTaskOverdue({
    required String taskId,
    required String taskTitle,
    required String assigneeUserId,
    required String assignerUserId,
    required DateTime dueDate,
  }) async {
    try {
      _logger.info('📋 Sending task overdue notification for: $taskId');
      
      // Notify assignee
      await _notificationManager.sendCustomNotification(
        userId: assigneeUserId,
        title: 'مهمة متأخرة',
        body: 'المهمة "$taskTitle" متأخرة عن موعدها المحدد في ${_formatDate(dueDate)}',
        type: 'task',
        data: {
          'task_id': taskId,
          'action': 'overdue',
          'task_title': taskTitle,
          'due_date': dueDate.toIso8601String(),
        },
      );
      
      // Notify assigner
      await _notificationManager.sendCustomNotification(
        userId: assignerUserId,
        title: 'مهمة متأخرة',
        body: 'المهمة "$taskTitle" متأخرة عن موعدها المحدد',
        type: 'task',
        data: {
          'task_id': taskId,
          'action': 'overdue',
          'task_title': taskTitle,
          'due_date': dueDate.toIso8601String(),
        },
      );
      
      // Log notifications
      await _logNotification(
        userId: assigneeUserId,
        type: 'task_overdue',
        title: 'مهمة متأخرة',
        message: 'المهمة "$taskTitle" متأخرة عن موعدها المحدد في ${_formatDate(dueDate)}',
        relatedId: taskId,
      );
      
      await _logNotification(
        userId: assignerUserId,
        type: 'task_overdue',
        title: 'مهمة متأخرة',
        message: 'المهمة "$taskTitle" متأخرة عن موعدها المحدد',
        relatedId: taskId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send task overdue notification: $e');
    }
  }

  // Send notification when task deadline is approaching
  Future<void> notifyTaskDeadlineApproaching({
    required String taskId,
    required String taskTitle,
    required String assigneeUserId,
    required DateTime dueDate,
    required int hoursRemaining,
  }) async {
    try {
      _logger.info('📋 Sending task deadline approaching notification for: $taskId');
      
      String message;
      if (hoursRemaining <= 1) {
        message = 'المهمة "$taskTitle" مطلوبة خلال أقل من ساعة';
      } else if (hoursRemaining <= 24) {
        message = 'المهمة "$taskTitle" مطلوبة خلال $hoursRemaining ساعة';
      } else {
        int daysRemaining = (hoursRemaining / 24).ceil();
        message = 'المهمة "$taskTitle" مطلوبة خلال $daysRemaining يوم';
      }
      
      await _notificationManager.sendCustomNotification(
        userId: assigneeUserId,
        title: 'تذكير بموعد المهمة',
        body: message,
        type: 'task',
        data: {
          'task_id': taskId,
          'action': 'deadline_approaching',
          'task_title': taskTitle,
          'due_date': dueDate.toIso8601String(),
          'hours_remaining': hoursRemaining.toString(),
        },
      );
      
      // Log the notification
      await _logNotification(
        userId: assigneeUserId,
        type: 'task_deadline_approaching',
        title: 'تذكير بموعد المهمة',
        message: message,
        relatedId: taskId,
      );
      
    } catch (e) {
      _logger.error('❌ Failed to send task deadline approaching notification: $e');
    }
  }

  // Send notification when task comment is added
  Future<void> notifyTaskCommentAdded({
    required String taskId,
    required String taskTitle,
    required String assigneeUserId,
    required String assignerUserId,
    required String commentAuthorId,
    required String commentAuthorName,
    required String commentText,
  }) async {
    try {
      _logger.info('📋 Sending task comment notification for: $taskId');
      
      // Determine who to notify (everyone except the comment author)
      List<String> targetUserIds = [assigneeUserId, assignerUserId];
      targetUserIds.removeWhere((id) => id == commentAuthorId);
      
      for (String userId in targetUserIds) {
        await _notificationManager.sendCustomNotification(
          userId: userId,
          title: 'تعليق جديد على المهمة',
          body: 'أضاف $commentAuthorName تعليقاً على المهمة "$taskTitle"',
          type: 'task',
          data: {
            'task_id': taskId,
            'action': 'comment_added',
            'task_title': taskTitle,
            'comment_author': commentAuthorName,
            'comment_text': commentText,
          },
        );
        
        // Log the notification
        await _logNotification(
          userId: userId,
          type: 'task_comment_added',
          title: 'تعليق جديد على المهمة',
          message: 'أضاف $commentAuthorName تعليقاً على المهمة "$taskTitle"',
          relatedId: taskId,
        );
      }
      
    } catch (e) {
      _logger.error('❌ Failed to send task comment notification: $e');
    }
  }

  // Log notification in database for history
  Future<void> _logNotification({
    required String userId,
    required String type,
    required String title,
    required String message,
    String? relatedId,
  }) async {
    try {
      await SupabaseConfig.client
          .from('notifications_log')
          .insert({
            'user_id': userId,
            'type': type,
            'title': title,
            'message': message,
            'related_id': relatedId,
            'sent_at': DateTime.now().toIso8601String(),
            'is_read': false,
          });
    } catch (e) {
      _logger.error('❌ Failed to log notification: $e');
    }
  }

  // Get status display name in Arabic
  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'on_hold':
        return 'معلقة';
      default:
        return status;
    }
  }

  // Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}