import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../cubit/inventory_cubit.dart';
import '../widgets/statistics_card.dart';
import '../widgets/date_filter_widget.dart';
import '../../data/repositories/financial_summary_repository.dart';

class InventoryPage extends StatelessWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const InventoryPage({
    super.key,
    required this.isVisible,
    required this.hasBeenVisited,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => InventoryCubit(FinancialSummaryRepository())
        ..loadCurrentMonth(),
      child: const InventoryView(),
    );
  }
}

class InventoryView extends StatelessWidget {
  const InventoryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'الجرد المالي',
      ),
      body: BlocBuilder<InventoryCubit, InventoryState>(
        builder: (context, state) {
          if (state is InventoryLoading) {
            return const LoadingWidget();
          }
          
          if (state is InventoryError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () {
                context.read<InventoryCubit>().loadCurrentMonth();
              },
            );
          }
          
          if (state is InventoryLoaded) {
            return RefreshIndicator(
              onRefresh: () async {
                await context.read<InventoryCubit>().refreshData();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلتر التواريخ المحسن
                    _buildEnhancedDateFilter(context, state),
                    
                    SizedBox(height: 20.h),
                    
                    // الملخص المالي الرئيسي
                    _buildMainSummarySection(state),
                    
                    SizedBox(height: 20.h),
                    
                    // إحصائيات المبيعات والحجوزات
                    _buildRevenueSection(state),
                    
                    SizedBox(height: 20.h),
                    
                    // إحصائيات المصروفات
                    _buildExpensesSection(state),
                    
                    SizedBox(height: 20.h),
                    
                    // الإحصائيات التفصيلية
                    _buildDetailedStatistics(state),
                    
                    SizedBox(height: 20.h),
                    
                    // مؤشرات الأداء
                    _buildPerformanceIndicators(state),
                  ],
                ),
              ),
            );
          }
          
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildEnhancedDateFilter(BuildContext context, InventoryLoaded state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.date_range,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'فترة التقرير',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gray900,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  context.read<InventoryCubit>().refreshData();
                },
                icon: Icon(
                  Icons.refresh,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                tooltip: 'تحديث البيانات',
              ),
            ],
          ),
          SizedBox(height: 12.h),
          DateFilterWidget(
            startDate: state.startDate,
            endDate: state.endDate,
            onDateRangeChanged: (startDate, endDate) {
              context.read<InventoryCubit>().updateDateRange(
                startDate,
                endDate,
              );
            },
            onRefresh: () {
              context.read<InventoryCubit>().refreshData();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMainSummarySection(InventoryLoaded state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الملخص المالي',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.gray900,
          ),
        ),
        SizedBox(height: 12.h),
        
        // البطاقات الرئيسية مع تدرج لوني
        Row(
          children: [
            Expanded(
              child: EnhancedStatisticsCard(
                title: 'إجمالي الدخل',
                value: '${state.summary.totalIncome.toStringAsFixed(0)} د.ا',
                subtitle: 'المبيعات + الحجوزات',
                icon: Icons.trending_up,
                gradientColors: [
                  Colors.green.shade400,
                  Colors.green.shade600,
                ],
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: EnhancedStatisticsCard(
                title: 'إجمالي المصروفات والعمولات',
                value: '${(state.summary.totalExpenses + state.summary.totalCommissions).toStringAsFixed(0)} د.ا',
                subtitle: 'المصروفات + العمولات',
                icon: Icons.trending_down,
                gradientColors: [
                  Colors.orange.shade400,
                  Colors.orange.shade600,
                ],
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),
        
        Row(
          children: [
            Expanded(
              child: EnhancedStatisticsCard(
                title: 'صافي الربح',
                value: '${state.summary.netProfit.toStringAsFixed(0)} د.ا',
                subtitle: 'إيرادات الحجوزات + أرباح المبيعات - المصروفات',
                icon: Icons.account_balance_wallet,
                gradientColors: state.summary.netProfit >= 0 
                    ? [Colors.blue.shade400, Colors.blue.shade600]
                    : [Colors.red.shade400, Colors.red.shade600],
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: EnhancedStatisticsCard(
                title: 'المبالغ المتبقية',
                value: '${state.summary.totalOutstanding.toStringAsFixed(0)} د.ا',
                subtitle: 'المبيعات المتبقية + الحجوزات المتبقية',
                icon: Icons.schedule,
                gradientColors: [
                  Colors.purple.shade400,
                  Colors.purple.shade600,
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRevenueSection(InventoryLoaded state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات الإيرادات',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.gray900,
          ),
        ),
        SizedBox(height: 12.h),
        
        // المبيعات
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.gray300.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.point_of_sale,
                    color: AppColors.primary,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'المبيعات',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gray900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              _buildStatsGrid([
                FinancialStatisticsCard(
                  title: 'إجمالي المبيعات',
                  amount: state.summary.totalSales,
                  subtitle: '${state.summary.totalSalesCount} فاتورة',
                  icon: Icons.receipt_long,
                  iconColor: AppColors.primary,
                ),
                FinancialStatisticsCard(
                  title: 'المبيعات المدفوعة',
                  amount: state.summary.totalPaidSales,
                  subtitle: 'مدفوع بالكامل',
                  icon: Icons.check_circle,
                  iconColor: Colors.green,
                ),
                FinancialStatisticsCard(
                  title: 'المبالغ المتبقية',
                  amount: state.summary.totalRemainingSales,
                  subtitle: 'غير مدفوع',
                  icon: Icons.pending,
                  iconColor: Colors.orange,
                ),
                FinancialStatisticsCard(
                  title: 'إجمالي العمولات',
                  amount: state.summary.totalCommissions,
                  subtitle: 'عمولات المبيعات',
                  icon: Icons.percent,
                  iconColor: Colors.purple,
                ),
                FinancialStatisticsCard(
                  title: 'إجمالي أصل المبيعات',
                  amount: state.summary.totalOriginalSales,
                  subtitle: '${state.summary.totalProductsCount} منتج',
                  icon: Icons.inventory,
                  iconColor: Colors.indigo,
                ),
                FinancialStatisticsCard(
                  title: 'إجمالي الأرباح',
                  amount: state.summary.totalProfits,
                  subtitle: 'المبيعات - الأصل - العمولات',
                  icon: Icons.trending_up,
                  iconColor: Colors.green,
                ),
              ]),
            ],
          ),
        ),
        
        SizedBox(height: 16.h),
        
        // الحجوزات
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.gray300.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: AppColors.primary,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'الحجوزات',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gray900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              _buildStatsGrid([
                FinancialStatisticsCard(
                  title: 'إيرادات الحجوزات',
                  amount: state.summary.totalAppointmentRevenue,
                  subtitle: '${state.summary.totalAppointmentsCount} حجز',
                  icon: Icons.event_available,
                  iconColor: AppColors.primary,
                ),
                FinancialStatisticsCard(
                  title: 'الحجوزات المدفوعة',
                  amount: state.summary.totalPaidAppointments,
                  subtitle: 'مدفوع بالكامل',
                  icon: Icons.check_circle,
                  iconColor: Colors.green,
                ),
                FinancialStatisticsCard(
                  title: 'الحجوزات المتبقية',
                  amount: state.summary.totalRemainingAppointments,
                  subtitle: 'غير مدفوع',
                  icon: Icons.pending,
                  iconColor: Colors.orange,
                ),
                FinancialStatisticsCard(
                  title: 'فارق الحجوزات',
                  amount: state.summary.appointmentsDifference,
                  subtitle: 'الإيرادات - المتبقي',
                  icon: Icons.calculate,
                  iconColor: Colors.blue,
                ),
              ]),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExpensesSection(InventoryLoaded state) {
    // إنشاء قائمة بالمصروفات الموجودة فقط
    final List<Widget> expenseCards = [];
    
    // إضافة كارت إجمالي المصروفات
    expenseCards.add(
      FinancialStatisticsCard(
        title: 'إجمالي المصروفات',
        amount: state.summary.totalExpenses,
        subtitle: '${state.summary.totalExpensesCount} مصروف',
        icon: Icons.account_balance_wallet,
        iconColor: Colors.red,
      ),
    );
    
    // إضافة الكروت حسب الفئات الموجودة
    if (state.summary.suppliesExpenses > 0) {
      expenseCards.add(
        FinancialStatisticsCard(
          title: 'مستلزمات',
          amount: state.summary.suppliesExpenses,
          subtitle: 'مواد وأدوات',
          icon: Icons.inventory_2,
          iconColor: Colors.blue,
        ),
      );
    }
    
    if (state.summary.maintenanceExpenses > 0) {
      expenseCards.add(
        FinancialStatisticsCard(
          title: 'صيانة',
          amount: state.summary.maintenanceExpenses,
          subtitle: 'إصلاحات وصيانة',
          icon: Icons.build,
          iconColor: Colors.orange,
        ),
      );
    }
    
    if (state.summary.utilitiesExpenses > 0) {
      expenseCards.add(
        FinancialStatisticsCard(
          title: 'خدمات عامة',
          amount: state.summary.utilitiesExpenses,
          subtitle: 'كهرباء وماء وغاز',
          icon: Icons.electrical_services,
          iconColor: Colors.yellow.shade700,
        ),
      );
    }
    
    if (state.summary.servicesExpenses > 0) {
      expenseCards.add(
        FinancialStatisticsCard(
          title: 'خدمات',
          amount: state.summary.servicesExpenses,
          subtitle: 'خدمات متنوعة',
          icon: Icons.miscellaneous_services,
          iconColor: Colors.purple,
        ),
      );
    }
    
    if (state.summary.otherExpenses > 0) {
      expenseCards.add(
        FinancialStatisticsCard(
          title: 'مصروفات أخرى',
          amount: state.summary.otherExpenses,
          subtitle: 'مصروفات متنوعة',
          icon: Icons.more_horiz,
          iconColor: Colors.grey,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_down,
                color: Colors.red,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'تفصيل المصروفات',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gray900,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildStatsGrid(expenseCards),
        ],
      ),
    );
  }

  Widget _buildDetailedStatistics(InventoryLoaded state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'تحليل الأداء المالي',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gray900,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // نسبة الربح
          _buildAnalysisRow(
            'نسبة الربح',
            '${((state.summary.netProfit / (state.summary.totalIncome > 0 ? state.summary.totalIncome : 1)) * 100).toStringAsFixed(1)}%',
            Icons.trending_up,
            state.summary.netProfit >= 0 ? Colors.green : Colors.red,
            (state.summary.netProfit / (state.summary.totalIncome > 0 ? state.summary.totalIncome : 1)) * 100,
          ),
          
          SizedBox(height: 12.h),
          
          // نسبة المصروفات
          _buildAnalysisRow(
            'نسبة المصروفات',
            '${((state.summary.totalExpenses / (state.summary.totalIncome > 0 ? state.summary.totalIncome : 1)) * 100).toStringAsFixed(1)}%',
            Icons.trending_down,
            Colors.red,
            (state.summary.totalExpenses / (state.summary.totalIncome > 0 ? state.summary.totalIncome : 1)) * 100,
          ),
          
          SizedBox(height: 12.h),
          
          // نسبة المت��قي
          _buildAnalysisRow(
            'نسبة المتبقي',
            '${((state.summary.totalOutstanding / (state.summary.totalSales + state.summary.totalAppointmentRevenue > 0 ? state.summary.totalSales + state.summary.totalAppointmentRevenue : 1)) * 100).toStringAsFixed(1)}%',
            Icons.schedule,
            Colors.orange,
            (state.summary.totalOutstanding / (state.summary.totalSales + state.summary.totalAppointmentRevenue > 0 ? state.summary.totalSales + state.summary.totalAppointmentRevenue : 1)) * 100,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceIndicators(InventoryLoaded state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.speed,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'مؤشرات الأداء',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gray900,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          Row(
            children: [
              Expanded(
                child: _buildKPICard(
                  'متوسط قيمة الفاتورة',
                  state.summary.totalSalesCount > 0 
                      ? '${(state.summary.totalSales / state.summary.totalSalesCount).toStringAsFixed(0)} د.ا'
                      : '0 د.ا',
                  Icons.receipt,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildKPICard(
                  'متوسط قيمة الحجز',
                  state.summary.totalAppointmentsCount > 0 
                      ? '${(state.summary.totalAppointmentRevenue / state.summary.totalAppointmentsCount).toStringAsFixed(0)} د.ا'
                      : '0 د.ا',
                  Icons.event,
                  Colors.green,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Expanded(
                child: _buildKPICard(
                  'معدل التحصيل',
                  state.summary.totalSales > 0 
                      ? '${((state.summary.totalPaidSales / state.summary.totalSales) * 100).toStringAsFixed(1)}%'
                      : '0%',
                  Icons.payment,
                  Colors.orange,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildKPICard(
                  'هامش الربح',
                  state.summary.totalIncome > 0 
                      ? '${((state.summary.netProfit / state.summary.totalIncome) * 100).toStringAsFixed(1)}%'
                      : '0%',
                  Icons.trending_up,
                  state.summary.netProfit >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة مساعدة لإنشاء شبكة الإحصائيات
  Widget _buildStatsGrid(List<Widget> children) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = (constraints.maxWidth - 8.w) / 2;
        return Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: children.map((child) => SizedBox(
            width: cardWidth,
            height: 90.h, // ارتفاع أكبر للمبالغ المحسنة
            child: child,
          )).toList(),
        );
      },
    );
  }

  Widget _buildAnalysisRow(String title, String value, IconData icon, Color color, double percentage) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(6.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6.r),
          ),
          child: Icon(
            icon,
            color: color,
            size: 16.sp,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.gray700,
                ),
              ),
              SizedBox(height: 4.h),
              Container(
                height: 6.h,
                decoration: BoxDecoration(
                  color: AppColors.gray200,
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: (percentage / 100).clamp(0.0, 1.0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 12.w),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildKPICard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16.sp,
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}