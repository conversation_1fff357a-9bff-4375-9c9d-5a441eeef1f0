import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/clinic_info_repository.dart';
import 'clinic_info_event.dart';
import 'clinic_info_state.dart';

class ClinicInfoBloc extends Bloc<ClinicInfoEvent, ClinicInfoState> {
  final ClinicInfoRepository _clinicInfoRepository;

  ClinicInfoBloc({required ClinicInfoRepository clinicInfoRepository})
      : _clinicInfoRepository = clinicInfoRepository,
        super(const ClinicInfoInitial()) {
    on<LoadAllClinicInfo>(_onLoadAllClinicInfo);
    on<LoadClinicInfoByType>(_onLoadClinicInfoByType);
    on<LoadPhoneNumbers>(_onLoadPhoneNumbers);
    on<LoadEmailAddresses>(_onLoadEmailAddresses);
    on<LoadSocialMediaLinks>(_onLoadSocialMediaLinks);
    on<LoadAddresses>(_onLoadAddresses);
    on<LoadWorkingHours>(_onLoadWorkingHours);
    on<LoadWebsiteInfo>(_onLoadWebsiteInfo);
    on<LoadClinicInfoGrouped>(_onLoadClinicInfoGrouped);
    on<AddClinicInfo>(_onAddClinicInfo);
    on<UpdateClinicInfo>(_onUpdateClinicInfo);
    on<DeleteClinicInfo>(_onDeleteClinicInfo);
    on<ToggleClinicInfoStatus>(_onToggleClinicInfoStatus);
    on<SearchClinicInfo>(_onSearchClinicInfo);
    on<RefreshClinicInfo>(_onRefreshClinicInfo);
  }

  Future<void> _onLoadAllClinicInfo(
    LoadAllClinicInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Starting to load all clinic info...');
    emit(const ClinicInfoLoading());
    try {
      final clinicInfoList = await _clinicInfoRepository.getAllClinicInfo();
      debugPrint('📊 ClinicInfoBloc: Loaded ${clinicInfoList.length} clinic info items');
      
      emit(ClinicInfoLoaded(clinicInfoList: clinicInfoList));
      debugPrint('✅ ClinicInfoBloc: Successfully emitted ClinicInfoLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ ClinicInfoBloc Error: $e');
      debugPrint('📍 ClinicInfoBloc Stack trace: $stackTrace');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onLoadClinicInfoByType(
    LoadClinicInfoByType event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Loading clinic info by type: ${event.infoType}');
    emit(const ClinicInfoLoading());
    try {
      final clinicInfoList = await _clinicInfoRepository.getClinicInfoByType(event.infoType);
      debugPrint('📊 ClinicInfoBloc: Loaded ${clinicInfoList.length} items for type ${event.infoType}');
      
      emit(ClinicInfoByTypeLoaded(
        infoType: event.infoType,
        clinicInfoList: clinicInfoList,
      ));
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error loading by type: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onLoadPhoneNumbers(
    LoadPhoneNumbers event,
    Emitter<ClinicInfoState> emit,
  ) async {
    add(const LoadClinicInfoByType(infoType: 'phone'));
  }

  Future<void> _onLoadEmailAddresses(
    LoadEmailAddresses event,
    Emitter<ClinicInfoState> emit,
  ) async {
    add(const LoadClinicInfoByType(infoType: 'email'));
  }

  Future<void> _onLoadSocialMediaLinks(
    LoadSocialMediaLinks event,
    Emitter<ClinicInfoState> emit,
  ) async {
    add(const LoadClinicInfoByType(infoType: 'social_media'));
  }

  Future<void> _onLoadAddresses(
    LoadAddresses event,
    Emitter<ClinicInfoState> emit,
  ) async {
    add(const LoadClinicInfoByType(infoType: 'address'));
  }

  Future<void> _onLoadWorkingHours(
    LoadWorkingHours event,
    Emitter<ClinicInfoState> emit,
  ) async {
    add(const LoadClinicInfoByType(infoType: 'working_hours'));
  }

  Future<void> _onLoadWebsiteInfo(
    LoadWebsiteInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    add(const LoadClinicInfoByType(infoType: 'website'));
  }

  Future<void> _onLoadClinicInfoGrouped(
    LoadClinicInfoGrouped event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Loading clinic info grouped by type...');
    emit(const ClinicInfoLoading());
    try {
      final groupedInfo = await _clinicInfoRepository.getClinicInfoGroupedByType();
      debugPrint('📊 ClinicInfoBloc: Loaded clinic info grouped by ${groupedInfo.keys.length} types');
      
      emit(ClinicInfoGroupedLoaded(groupedInfo: groupedInfo));
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error loading grouped info: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onAddClinicInfo(
    AddClinicInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Adding new clinic info...');
    try {
      final newClinicInfo = await _clinicInfoRepository.addClinicInfo(event.clinicInfo);
      debugPrint('✅ ClinicInfoBloc: Successfully added clinic info: ${newClinicInfo.displayName}');
      
      emit(ClinicInfoCreated(clinicInfo: newClinicInfo));
      
      // Reload all clinic info
      add(const LoadAllClinicInfo());
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error adding clinic info: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onUpdateClinicInfo(
    UpdateClinicInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Updating clinic info...');
    try {
      final updatedClinicInfo = await _clinicInfoRepository.updateClinicInfo(event.clinicInfo);
      debugPrint('✅ ClinicInfoBloc: Successfully updated clinic info: ${updatedClinicInfo.displayName}');
      
      emit(ClinicInfoUpdated(clinicInfo: updatedClinicInfo));
      
      // Reload all clinic info
      add(const LoadAllClinicInfo());
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error updating clinic info: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onDeleteClinicInfo(
    DeleteClinicInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Deleting clinic info...');
    try {
      await _clinicInfoRepository.deleteClinicInfo(event.clinicInfoId);
      debugPrint('✅ ClinicInfoBloc: Successfully deleted clinic info: ${event.clinicInfoId}');
      
      emit(ClinicInfoDeleted(clinicInfoId: event.clinicInfoId));
      
      // Reload all clinic info
      add(const LoadAllClinicInfo());
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error deleting clinic info: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onToggleClinicInfoStatus(
    ToggleClinicInfoStatus event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Toggling clinic info status...');
    try {
      final updatedClinicInfo = await _clinicInfoRepository.toggleClinicInfoStatus(
        event.clinicInfoId,
        event.isActive,
      );
      debugPrint('✅ ClinicInfoBloc: Successfully toggled clinic info status');
      
      emit(ClinicInfoStatusToggled(clinicInfo: updatedClinicInfo));
      
      // Reload all clinic info
      add(const LoadAllClinicInfo());
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error toggling clinic info status: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onSearchClinicInfo(
    SearchClinicInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Searching clinic info with query: ${event.query}');
    try {
      if (event.query.isEmpty) {
        // If query is empty, reload all clinic info
        add(const LoadAllClinicInfo());
        return;
      }

      final searchResults = await _clinicInfoRepository.searchClinicInfo(event.query);
      debugPrint('📊 ClinicInfoBloc: Found ${searchResults.length} clinic info items matching query');
      
      if (state is ClinicInfoLoaded) {
        final currentState = state as ClinicInfoLoaded;
        emit(currentState.copyWith(
          searchResults: searchResults,
          searchQuery: event.query,
        ));
      } else {
        emit(ClinicInfoLoaded(
          clinicInfoList: searchResults,
          searchResults: searchResults,
          searchQuery: event.query,
        ));
      }
    } catch (e) {
      debugPrint('❌ ClinicInfoBloc Error searching clinic info: $e');
      emit(ClinicInfoError(message: e.toString()));
    }
  }

  Future<void> _onRefreshClinicInfo(
    RefreshClinicInfo event,
    Emitter<ClinicInfoState> emit,
  ) async {
    debugPrint('🔄 ClinicInfoBloc: Refreshing clinic info...');
    add(const LoadAllClinicInfo());
  }
}
