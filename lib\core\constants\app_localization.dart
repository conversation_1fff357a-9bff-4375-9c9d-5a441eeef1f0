import 'package:flutter/material.dart';

class AppLocalization {
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('ar', 'SA'), // Arabic (Saudi Arabia)
    Locale('en', 'US'), // English (United States)
  ];

  // Default locale
  static const Locale defaultLocale = Locale('ar', 'SA');

  // RTL languages
  static const List<String> rtlLanguages = ['ar', 'he', 'fa', 'ur'];

  // Check if language is RTL
  static bool isRTL(String languageCode) {
    return rtlLanguages.contains(languageCode);
  }

  // Get text direction based on locale
  static TextDirection getTextDirection(Locale locale) {
    return isRTL(locale.languageCode) ? TextDirection.rtl : TextDirection.ltr;
  }

  // Arabic number formatting
  static String formatArabicNumbers(String input) {
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = input;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], arabicNumbers[i]);
    }
    return result;
  }

  // Convert Arabic numbers to English
  static String formatEnglishNumbers(String input) {
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = input;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }

  // Format currency for Arabic
  static String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ج.م';
  }

  // Format date for Arabic
  static String formatArabicDate(DateTime date) {
    const arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const arabicDays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];

    final dayName = arabicDays[date.weekday - 1];
    final monthName = arabicMonths[date.month - 1];
    final day = formatArabicNumbers(date.day.toString());
    final year = formatArabicNumbers(date.year.toString());

    return '$dayName، $day $monthName $year';
  }

  // Format time for Arabic
  static String formatArabicTime(TimeOfDay time) {
    final hour = formatArabicNumbers(time.hour.toString().padLeft(2, '0'));
    final minute = formatArabicNumbers(time.minute.toString().padLeft(2, '0'));
    return '$hour:$minute';
  }

  // Get appropriate font family for Arabic (using system font)
  static String? getArabicFontFamily() {
    return null; // Uses system font which supports Arabic
  }

  // Text alignment for RTL
  static TextAlign getTextAlign(TextDirection direction) {
    return direction == TextDirection.rtl ? TextAlign.right : TextAlign.left;
  }

  // Edge insets for RTL
  static EdgeInsets getEdgeInsets({
    required double start,
    required double top,
    required double end,
    required double bottom,
    required TextDirection direction,
  }) {
    if (direction == TextDirection.rtl) {
      return EdgeInsets.fromLTRB(end, top, start, bottom);
    } else {
      return EdgeInsets.fromLTRB(start, top, end, bottom);
    }
  }

  // Border radius for RTL
  static BorderRadius getBorderRadius({
    required double topStart,
    required double topEnd,
    required double bottomStart,
    required double bottomEnd,
    required TextDirection direction,
  }) {
    if (direction == TextDirection.rtl) {
      return BorderRadius.only(
        topLeft: Radius.circular(topEnd),
        topRight: Radius.circular(topStart),
        bottomLeft: Radius.circular(bottomEnd),
        bottomRight: Radius.circular(bottomStart),
      );
    } else {
      return BorderRadius.only(
        topLeft: Radius.circular(topStart),
        topRight: Radius.circular(topEnd),
        bottomLeft: Radius.circular(bottomStart),
        bottomRight: Radius.circular(bottomEnd),
      );
    }
  }
}
