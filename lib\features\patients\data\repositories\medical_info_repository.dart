import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/medical_info_model.dart';

class MedicalInfoRepository {
  // Get all medical info for a patient
  Future<List<MedicalInfoModel>> getMedicalInfoByPatientId(String patientId) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Loading medical info for patient: $patientId');
      final response = await SupabaseConfig.medicalInfo
          .select()
          .eq('patient_id', patientId)
          .order('created_at', ascending: false);

      debugPrint('📊 MedicalInfoRepository: Raw response: $response');
      debugPrint('📊 MedicalInfoRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ MedicalInfoRepository: No medical info found for patient: $patientId');
        return [];
      }

      final medicalInfoList = response.map((json) => MedicalInfoModel.fromJson(json)).toList();
      debugPrint('✅ MedicalInfoRepository: Successfully parsed ${medicalInfoList.length} medical info items');
      
      return medicalInfoList;
    } catch (e, stackTrace) {
      debugPrint('❌ MedicalInfoRepository: Error loading medical info: $e');
      debugPrint('📍 MedicalInfoRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب المعلومات الطبية: ${e.toString()}');
    }
  }

  // Get medical info by type
  Future<List<MedicalInfoModel>> getMedicalInfoByType(String patientId, String infoType) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Loading medical info by type: $infoType for patient: $patientId');
      final response = await SupabaseConfig.medicalInfo
          .select()
          .eq('patient_id', patientId)
          .eq('info_type', infoType)
          .order('created_at', ascending: false);

      final medicalInfoList = response.map((json) => MedicalInfoModel.fromJson(json)).toList();
      debugPrint('✅ MedicalInfoRepository: Found ${medicalInfoList.length} medical info items of type: $infoType');
      
      return medicalInfoList;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error loading medical info by type: $e');
      throw Exception('فشل في جلب المعلومات الطبية حسب النوع: ${e.toString()}');
    }
  }

  // Get medications
  Future<List<MedicalInfoModel>> getMedications(String patientId) async {
    return getMedicalInfoByType(patientId, 'medication');
  }

  // Get supplements
  Future<List<MedicalInfoModel>> getSupplements(String patientId) async {
    return getMedicalInfoByType(patientId, 'supplement');
  }

  // Get medical conditions
  Future<List<MedicalInfoModel>> getMedicalConditions(String patientId) async {
    return getMedicalInfoByType(patientId, 'condition');
  }

  // Get allergies
  Future<List<MedicalInfoModel>> getAllergies(String patientId) async {
    return getMedicalInfoByType(patientId, 'allergy');
  }

  // Get physical activities
  Future<List<MedicalInfoModel>> getPhysicalActivities(String patientId) async {
    return getMedicalInfoByType(patientId, 'activity');
  }

  // Get active medical info
  Future<List<MedicalInfoModel>> getActiveMedicalInfo(String patientId) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Loading active medical info for patient: $patientId');
      final response = await SupabaseConfig.medicalInfo
          .select()
          .eq('patient_id', patientId)
          .eq('is_active', true)
          .order('created_at', ascending: false);

      final medicalInfoList = response.map((json) => MedicalInfoModel.fromJson(json)).toList();
      debugPrint('✅ MedicalInfoRepository: Found ${medicalInfoList.length} active medical info items');
      
      return medicalInfoList;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error loading active medical info: $e');
      throw Exception('فشل في جلب المعلومات الطبية النشطة: ${e.toString()}');
    }
  }

  // Add new medical info
  Future<MedicalInfoModel> addMedicalInfo(MedicalInfoModel medicalInfo) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Adding new medical info: ${medicalInfo.name}');
      
      final response = await SupabaseConfig.medicalInfo
          .insert(medicalInfo.toJson())
          .select()
          .single();

      final newMedicalInfo = MedicalInfoModel.fromJson(response);
      debugPrint('✅ MedicalInfoRepository: Successfully added medical info: ${newMedicalInfo.id}');
      
      return newMedicalInfo;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error adding medical info: $e');
      throw Exception('فشل في إضافة المعلومات الطبية: ${e.toString()}');
    }
  }

  // Update medical info
  Future<MedicalInfoModel> updateMedicalInfo(MedicalInfoModel medicalInfo) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Updating medical info: ${medicalInfo.id}');
      
      final response = await SupabaseConfig.medicalInfo
          .update(medicalInfo.toJson())
          .eq('id', medicalInfo.id)
          .select()
          .single();

      final updatedMedicalInfo = MedicalInfoModel.fromJson(response);
      debugPrint('✅ MedicalInfoRepository: Successfully updated medical info: ${updatedMedicalInfo.id}');
      
      return updatedMedicalInfo;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error updating medical info: $e');
      throw Exception('فشل في تحديث المعلومات الطبية: ${e.toString()}');
    }
  }

  // Delete medical info
  Future<void> deleteMedicalInfo(String medicalInfoId) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Deleting medical info: $medicalInfoId');
      
      await SupabaseConfig.medicalInfo
          .delete()
          .eq('id', medicalInfoId);

      debugPrint('✅ MedicalInfoRepository: Successfully deleted medical info: $medicalInfoId');
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error deleting medical info: $e');
      throw Exception('فشل في حذف المعلومات الطبية: ${e.toString()}');
    }
  }

  // Toggle medical info status
  Future<MedicalInfoModel> toggleMedicalInfoStatus(String medicalInfoId, bool isActive) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Toggling medical info status: $medicalInfoId to $isActive');
      
      final response = await SupabaseConfig.medicalInfo
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', medicalInfoId)
          .select()
          .single();

      final updatedMedicalInfo = MedicalInfoModel.fromJson(response);
      debugPrint('✅ MedicalInfoRepository: Successfully toggled medical info status');
      
      return updatedMedicalInfo;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error toggling medical info status: $e');
      throw Exception('فشل في تغيير حالة المعلومات الطبية: ${e.toString()}');
    }
  }

  // Search medical info
  Future<List<MedicalInfoModel>> searchMedicalInfo(String patientId, String query) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Searching medical info for patient: $patientId with query: $query');
      
      final response = await SupabaseConfig.medicalInfo
          .select()
          .eq('patient_id', patientId)
          .or('name.ilike.%$query%,description.ilike.%$query%,notes.ilike.%$query%')
          .order('created_at', ascending: false);

      final medicalInfoList = response.map((json) => MedicalInfoModel.fromJson(json)).toList();
      debugPrint('✅ MedicalInfoRepository: Found ${medicalInfoList.length} medical info items matching query');
      
      return medicalInfoList;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error searching medical info: $e');
      throw Exception('فشل في البحث عن المعلومات الطبية: ${e.toString()}');
    }
  }

  // Get medical info grouped by type
  Future<Map<String, List<MedicalInfoModel>>> getMedicalInfoGroupedByType(String patientId) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Loading medical info grouped by type for patient: $patientId');
      
      final allMedicalInfo = await getMedicalInfoByPatientId(patientId);
      final Map<String, List<MedicalInfoModel>> grouped = {};
      
      for (final info in allMedicalInfo) {
        if (!grouped.containsKey(info.infoType)) {
          grouped[info.infoType] = [];
        }
        grouped[info.infoType]!.add(info);
      }
      
      debugPrint('✅ MedicalInfoRepository: Successfully grouped medical info by ${grouped.keys.length} types');
      return grouped;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error grouping medical info: $e');
      throw Exception('فشل في تجميع المعلومات الطبية: ${e.toString()}');
    }
  }

  // Get current medications (active and not expired)
  Future<List<MedicalInfoModel>> getCurrentMedications(String patientId) async {
    try {
      debugPrint('🔍 MedicalInfoRepository: Loading current medications for patient: $patientId');
      
      final medications = await getMedications(patientId);
      final currentMedications = medications.where((med) => 
        med.isActive && med.isOngoing
      ).toList();
      
      debugPrint('✅ MedicalInfoRepository: Found ${currentMedications.length} current medications');
      return currentMedications;
    } catch (e) {
      debugPrint('❌ MedicalInfoRepository: Error loading current medications: $e');
      throw Exception('فشل في جلب الأدوية الحالية: ${e.toString()}');
    }
  }
}
