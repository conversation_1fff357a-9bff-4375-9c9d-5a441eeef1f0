import 'package:equatable/equatable.dart';
import '../../data/repositories/appointment_booking_repository.dart';

abstract class AppointmentBookingState extends Equatable {
  const AppointmentBookingState();

  @override
  List<Object?> get props => [];
}

class AppointmentBookingInitial extends AppointmentBookingState {
  const AppointmentBookingInitial();
}

class AppointmentBookingLoading extends AppointmentBookingState {
  const AppointmentBookingLoading();
}

class AppointmentBookingLoaded extends AppointmentBookingState {
  final AppointmentBookingResult bookingResult;
  final List<PatientAppointmentWithDetails> upcomingAppointments;
  final List<PatientAppointmentWithDetails> pastAppointments;

  const AppointmentBookingLoaded({
    required this.bookingResult,
    required this.upcomingAppointments,
    this.pastAppointments = const [],
  });

  @override
  List<Object?> get props => [bookingResult, upcomingAppointments, pastAppointments];
}

class AppointmentBookingError extends AppointmentBookingState {
  final String message;

  const AppointmentBookingError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AppointmentBooked extends AppointmentBookingState {
  final String appointmentId;

  const AppointmentBooked({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}
