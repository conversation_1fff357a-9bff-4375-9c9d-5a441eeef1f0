import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/models/reminder_model.dart';
import '../../data/repositories/reminders_repository.dart';
import '../../../../core/services/notification_automation_service.dart';
import 'reminders_event.dart';
import 'reminders_state.dart';

class RemindersBloc extends Bloc<RemindersEvent, RemindersState> {
  final RemindersRepository _remindersRepository;

  RemindersBloc({required RemindersRepository remindersRepository})
    : _remindersRepository = remindersRepository,
      super(const RemindersInitial()) {
    on<LoadRemindersByPatientId>(_onLoadRemindersByPatientId);
    on<LoadRemindersByType>(_onLoadRemindersByType);
    on<LoadActiveReminders>(_onLoadActiveReminders);
    on<AddReminder>(_onAddReminder);
    on<UpdateReminder>(_onUpdateReminder);
    on<DeleteReminder>(_onDeleteReminder);
    on<ToggleReminderStatus>(_onToggleReminderStatus);
    on<SearchReminders>(_onSearchReminders);
    on<RefreshReminders>(_onRefreshReminders);
  }

  Future<void> _onLoadRemindersByPatientId(
    LoadRemindersByPatientId event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint(
      '🔄 RemindersBloc: Loading reminders for patient: ${event.patientId}',
    );
    emit(const RemindersLoading());
    try {
      final reminders = await _remindersRepository.getRemindersByPatientId(
        event.patientId,
      );

      // Group by type
      final groupedByType = <String, List<ReminderModel>>{};
      for (final reminder in reminders) {
        final type = reminder.reminderType;
        if (!groupedByType.containsKey(type)) {
          groupedByType[type] = [];
        }
        groupedByType[type]!.add(reminder);
      }

      debugPrint('📊 RemindersBloc: Loaded ${reminders.length} reminders');

      emit(
        RemindersLoaded(
          reminders: reminders,
          patientId: event.patientId,
          groupedByType: groupedByType,
        ),
      );
      debugPrint('✅ RemindersBloc: Successfully emitted RemindersLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ RemindersBloc Error: $e');
      debugPrint('📍 RemindersBloc Stack trace: $stackTrace');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onLoadRemindersByType(
    LoadRemindersByType event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint(
      '🔄 RemindersBloc: Loading reminders by type: ${event.reminderType}',
    );
    emit(const RemindersLoading());
    try {
      final reminders = await _remindersRepository.getRemindersByType(
        event.patientId,
        event.reminderType,
      );

      debugPrint(
        '📊 RemindersBloc: Loaded ${reminders.length} reminders of type: ${event.reminderType}',
      );

      emit(
        RemindersByTypeLoaded(
          reminders: reminders,
          patientId: event.patientId,
          reminderType: event.reminderType,
        ),
      );
    } catch (e) {
      debugPrint('❌ RemindersBloc Error loading by type: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onLoadActiveReminders(
    LoadActiveReminders event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint('🔄 RemindersBloc: Loading active reminders');
    emit(const RemindersLoading());
    try {
      final activeReminders = await _remindersRepository.getActiveReminders(
        event.patientId,
      );

      debugPrint(
        '📊 RemindersBloc: Loaded ${activeReminders.length} active reminders',
      );

      emit(
        ActiveRemindersLoaded(
          activeReminders: activeReminders,
          patientId: event.patientId,
        ),
      );
    } catch (e) {
      debugPrint('❌ RemindersBloc Error loading active reminders: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onAddReminder(
    AddReminder event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint('🔄 RemindersBloc: Adding new reminder');
    try {
      final newReminder = await _remindersRepository.addReminder(
        event.reminder,
      );
      debugPrint(
        '✅ RemindersBloc: Successfully added reminder: ${newReminder.id}',
      );

      // Create notification for the new reminder
      await NotificationAutomationService.createNotificationForReminder(
        patientId: newReminder.patientId,
        reminderId: newReminder.id,
        reminderType: newReminder.reminderType,
        reminderContent: newReminder.description ?? newReminder.title,
        scheduledTime: newReminder.reminderTime,
        daysOfWeek: newReminder.daysOfWeek,
      );
      debugPrint(
        '✅ RemindersBloc: Notification created for reminder: ${newReminder.id}',
      );

      emit(ReminderCreated(reminder: newReminder));

      // Reload reminders
      add(LoadRemindersByPatientId(patientId: event.reminder.patientId));
    } catch (e) {
      debugPrint('❌ RemindersBloc Error adding reminder: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onUpdateReminder(
    UpdateReminder event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint('🔄 RemindersBloc: Updating reminder');
    try {
      final updatedReminder = await _remindersRepository.updateReminder(
        event.reminder,
      );
      debugPrint(
        '✅ RemindersBloc: Successfully updated reminder: ${updatedReminder.id}',
      );

      // Update notification for the reminder
      await NotificationAutomationService.updateNotificationForReminder(
        reminderId: updatedReminder.id,
        reminderContent: updatedReminder.description ?? updatedReminder.title,
        scheduledTime: updatedReminder.reminderTime,
        daysOfWeek: updatedReminder.daysOfWeek,
      );
      debugPrint(
        '✅ RemindersBloc: Notification updated for reminder: ${updatedReminder.id}',
      );

      emit(ReminderUpdated(reminder: updatedReminder));

      // Reload reminders
      add(LoadRemindersByPatientId(patientId: event.reminder.patientId));
    } catch (e) {
      debugPrint('❌ RemindersBloc Error updating reminder: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onDeleteReminder(
    DeleteReminder event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint('🔄 RemindersBloc: Deleting reminder');
    try {
      // Don't emit loading here as it's handled by UI
      await _remindersRepository.deleteReminder(event.reminderId);
      debugPrint(
        '✅ RemindersBloc: Successfully deleted reminder: ${event.reminderId}',
      );

      // Delete notification for the reminder
      await NotificationAutomationService.deleteNotificationForReminder(
        event.reminderId,
      );
      debugPrint(
        '✅ RemindersBloc: Notification deleted for reminder: ${event.reminderId}',
      );

      emit(ReminderDeleted(reminderId: event.reminderId));

      // Reload reminders using patientId from event
      debugPrint(
        '🔄 RemindersBloc: Reloading data after deletion for patient: ${event.patientId}',
      );
      add(LoadRemindersByPatientId(patientId: event.patientId));
    } catch (e) {
      debugPrint('❌ RemindersBloc Error deleting reminder: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onToggleReminderStatus(
    ToggleReminderStatus event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint('🔄 RemindersBloc: Toggling reminder status');
    try {
      // Don't emit loading here as it's handled by UI
      await _remindersRepository.toggleReminderStatus(
        event.reminderId,
        event.isActive,
      );
      debugPrint('✅ RemindersBloc: Successfully toggled reminder status');

      emit(
        ReminderStatusToggled(
          reminderId: event.reminderId,
          isActive: event.isActive,
        ),
      );

      // Reload reminders using patientId from event
      debugPrint(
        '🔄 RemindersBloc: Reloading data after status toggle for patient: ${event.patientId}',
      );
      add(LoadRemindersByPatientId(patientId: event.patientId));
    } catch (e) {
      debugPrint('❌ RemindersBloc Error toggling status: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onSearchReminders(
    SearchReminders event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint(
      '🔄 RemindersBloc: Searching reminders with query: ${event.query}',
    );
    try {
      final searchResults = await _remindersRepository.searchReminders(
        event.patientId,
        event.query,
      );

      debugPrint(
        '📊 RemindersBloc: Found ${searchResults.length} reminders matching query',
      );

      if (state is RemindersLoaded) {
        final currentState = state as RemindersLoaded;
        // For search, we can update the current state or emit a new search state
        // Here we'll just reload with search results
        final groupedByType = <String, List<ReminderModel>>{};
        for (final reminder in searchResults) {
          final type = reminder.reminderType;
          if (!groupedByType.containsKey(type)) {
            groupedByType[type] = [];
          }
          groupedByType[type]!.add(reminder);
        }

        emit(
          currentState.copyWith(
            reminders: searchResults,
            groupedByType: groupedByType,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ RemindersBloc Error searching reminders: $e');
      emit(RemindersError(message: e.toString()));
    }
  }

  Future<void> _onRefreshReminders(
    RefreshReminders event,
    Emitter<RemindersState> emit,
  ) async {
    debugPrint('🔄 RemindersBloc: Refreshing reminders');
    add(LoadRemindersByPatientId(patientId: event.patientId));
  }
}
