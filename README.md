# IIHC Admin - تطبيق لوحة تحكم معهد الصحة المتكاملة

تطبيق Flutter لإدارة معهد الصحة المتكاملة مع لوحة تحكم شاملة للأطباء والإداريين.

## المميزات الرئيسية

### 🔐 نظام المصادقة
- تسجيل الدخول والتسجيل للإداريين
- حماية كلمات المرور بالتشفير
- إدارة الجلسات

### 📅 إدارة الحجوزات
- عرض مواعيد اليوم الحالي
- إنشاء مواعيد وفترات جديدة
- إدارة حالة المواعيد

### 👥 إدارة المرضى
- **المرضى المميزون**: إدارة خاصة للمرضى المميزين
- **جميع المرضى**: عرض وإدارة جميع المرضى
- ترقية المرضى إلى حسابات مميزة

### 📊 ملف المريض الشخصي
- **البيانات الأساسية**: الوزن، الطول، العمر
- **النتائج الأسبوعية**: تتبع التقدم (الوزن، الدهون، العضلات، السوائل)
- **الفحوصات المخبرية**: إضافة وإدارة صور الفحوصات
- **المعلومات الطبية**: الأدوية، المكملات، الحساسية، النشاط البدني
- **إدارة المواعيد**: حجز وإدارة مواعيد المريض

### 🛍️ إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- إدارة كاملة لكتالوج المنتجات
- تصنيف المنتجات والأسعار

### 📝 إدارة المحتوى
- إضافة وتعديل المقالات العلمية
- إدارة حالة النشر
- تصنيف المقالات

### ⚙️ الملف الشخصي
- إعدادات حساب الإداري
- تغيير كلمة المرور
- إعدادات الأمان

## التقنيات المستخدمة

- **Flutter & Dart**: إطار العمل الأساسي
- **Flutter Bloc**: إدارة الحالات
- **Supabase**: قاعدة البيانات والخلفية
- **Flutter ScreenUtil**: التصميم المتجاوب
- **Clean Architecture**: هيكل المشروع

## إعداد المشروع

### 1. متطلبات النظام
```bash
Flutter SDK >= 3.7.2
Dart SDK >= 3.7.2
```

### 2. تثبيت المكتبات
```bash
flutter pub get
```

### 3. إعداد Supabase
1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. افتح مشروع `diet_rx`
3. اذهب إلى Settings > API
4. انسخ `Project URL` و `anon public key`
5. حدث الملف `lib/core/network/supabase_client.dart`:

```dart
class SupabaseConfig {
  static const String supabaseUrl = 'YOUR_PROJECT_URL';
  static const String supabaseAnonKey = 'YOUR_ANON_KEY';
  // ...
}
```

### 4. تشغيل التطبيق
```bash
flutter run
```

## هيكل قاعدة البيانات

### الجداول المنشأة:
- `admins`: بيانات الإداريين (معرف 10 خانات)
- `patients`: بيانات المرضى (معرف 10 خانات)
- `appointments`: المواعيد والحجوزات
- `time_slots`: المواعيد الثابتة الأسبوعية
- `weekly_results`: النتائج الأسبوعية للمرضى
- `lab_tests`: الفحوصات المخبرية
- `products`: منتجات العيادة
- `articles`: المقالات العلمية
- `categories`: فئات المنتجات والمقالات

## نظام الألوان

- **الألوان الأساسية**: الأبيض والأسود
- **الألوان الثانوية**:
  - الأخضر: `#80AC5C`
  - الرمادي الأخضر: `#B4BCB0`

## الاستخدام

### تسجيل الدخول
1. افتح التطبيق
2. أدخل البريد الإلكتروني وكلمة المرور
3. اضغط "تسجيل الدخول"

### إنشاء حساب جديد
1. من صفحة تسجيل الدخول، اضغط "إنشاء حساب جديد"
2. أدخل الاسم والبريد الإلكتروني وكلمة المرور
3. اضغط "إنشاء الحساب"

### التنقل في التطبيق
استخدم شريط التنقل السفلي للانتقال بين:
- الحجوزات
- المرضى
- المنتجات
- المقالات
- الملف الشخصي

## المساهمة

هذا المشروع مطور خصيصاً لعيادة التغذية والحمية الغذائية.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**ملاحظة**: تأكد من تحديث مفاتيح Supabase قبل تشغيل التطبيق.
