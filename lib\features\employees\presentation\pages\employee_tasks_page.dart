import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/employee_task_model.dart';
import '../bloc/employees_bloc.dart';
import '../bloc/employee_tasks_bloc.dart';
import '../bloc/employee_tasks_event.dart';
import '../bloc/employee_tasks_state.dart';
import '../widgets/add_task_dialog.dart';
import '../widgets/edit_task_dialog.dart';
import '../widgets/task_card.dart';

class EmployeeTasksPage extends StatefulWidget {
  const EmployeeTasksPage({super.key});

  @override
  State<EmployeeTasksPage> createState() => _EmployeeTasksPageState();
}

class _EmployeeTasksPageState extends State<EmployeeTasksPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<EmployeeModel> _employees = [];
  String _currentFilter = 'all';
  String _searchQuery = '';
  String? _selectedEmployeeId;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadEmployees();
    _loadTasks();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadEmployees() {
    context.read<EmployeesBloc>().add(LoadAllEmployees());
  }

  void _loadTasks() {
    if (_selectedEmployeeId != null) {
      context.read<EmployeeTasksBloc>().add(LoadTasksByEmployee(_selectedEmployeeId!));
      return;
    }

    switch (_currentFilter) {
      case 'all':
        context.read<EmployeeTasksBloc>().add(const LoadAllTasks());
        break;
      case 'pending':
        context.read<EmployeeTasksBloc>().add(const LoadTasksByStatus('pending'));
        break;
      case 'in_progress':
        context.read<EmployeeTasksBloc>().add(const LoadTasksByStatus('in_progress'));
        break;
      case 'completed':
        context.read<EmployeeTasksBloc>().add(const LoadTasksByStatus('completed'));
        break;
      case 'overdue':
        context.read<EmployeeTasksBloc>().add(const LoadOverdueTasks());
        break;
    }
  }

  void _onTabChanged(int index) {
    setState(() {
      switch (index) {
        case 0:
          _currentFilter = 'all';
          break;
        case 1:
          _currentFilter = 'pending';
          break;
        case 2:
          _currentFilter = 'in_progress';
          break;
        case 3:
          _currentFilter = 'completed';
          break;
        case 4:
          _currentFilter = 'overdue';
          break;
      }
    });
    _loadTasks();
  }

  void _showAddTaskDialog() {
    if (_employees.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد موظفين متاحين'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final bloc = context.read<EmployeeTasksBloc>();
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: bloc,
        child: AddTaskDialog(
          employees: _employees,
          onTaskAdded: (task) {
            bloc.add(AddTask(task));
          },
        ),
      ),
    );
  }

  void _showEditTaskDialog(EmployeeTaskModel task) {
    final bloc = context.read<EmployeeTasksBloc>();
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: bloc,
        child: EditTaskDialog(
          task: task,
          employees: _employees,
          onTaskUpdated: (updatedTask) {
            bloc.add(UpdateTask(updatedTask));
          },
        ),
      ),
    );
  }

  void _deleteTask(EmployeeTaskModel task) {
    final bloc = context.read<EmployeeTasksBloc>();
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المهمة "${task.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              bloc.add(DeleteTask(task.id));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _updateTaskStatus(EmployeeTaskModel task, String newStatus) {
    context.read<EmployeeTasksBloc>().add(
      UpdateTaskStatus(taskId: task.id, status: newStatus),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header with Add Task Button
          Container(
            padding: EdgeInsets.all(16.w),
            color: AppColors.white,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'إدارة مهام الموظفين',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: _showAddTaskDialog,
                      icon: Icon(Icons.add, size: 18.sp),
                      label: const Text('إضافة مهمة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  ),
                ),
              ],
            ),

                SizedBox(height: 12.h),

                // Employee Search
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(color: AppColors.border),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedEmployeeId,
                            hint: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 12.w),
                              child: Text(
                                'بحث بالموظف',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ),
                            isExpanded: true,
                            items: [
                              DropdownMenuItem<String>(
                                value: null,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                                  child: Text(
                                    'جميع الموظفين',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                ),
                              ),
                              ..._employees.map((employee) => DropdownMenuItem<String>(
                                value: employee.id,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                                  child: Text(
                                    employee.name,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                ),
                              )),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedEmployeeId = value;
                              });
                              _loadTasks();
                            },
                          ),
                        ),
                      ),
                    ),
                    if (_selectedEmployeeId != null) ...[
                      SizedBox(width: 8.w),
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedEmployeeId = null;
                          });
                          _loadTasks();
                        },
                        icon: Icon(
                          Icons.clear,
                          color: AppColors.error,
                          size: 20.sp,
                        ),
                        tooltip: 'مسح البحث',
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Filter Tabs
          Container(
            color: AppColors.surface,
            child: TabBar(
              controller: _tabController,
              onTap: _onTabChanged,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              tabs: const [
                Tab(text: 'الكل'),
                Tab(text: 'في الانتظار'),
                Tab(text: 'قيد التنفيذ'),
                Tab(text: 'مكتملة'),
                Tab(text: 'متأخرة'),
              ],
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primary,
            ),
          ),

          // Tasks List
          Expanded(
            child: MultiBlocListener(
              listeners: [
                BlocListener<EmployeesBloc, EmployeesState>(
                  listener: (context, state) {
                    if (state is EmployeesLoaded) {
                      setState(() {
                        _employees = state.employees;
                      });
                    }
                  },
                ),
                BlocListener<EmployeeTasksBloc, EmployeeTasksState>(
                  listener: (context, state) {
                    if (state is EmployeeTasksError) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(state.message),
                          backgroundColor: AppColors.error,
                        ),
                      );
                    } else if (state is TaskAdded) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إضافة المهمة بنجاح'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    } else if (state is TaskUpdated) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم تحديث المهمة بنجاح'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    } else if (state is TaskDeleted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم حذف المهمة بنجاح'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  },
                ),
              ],
              child: BlocBuilder<EmployeeTasksBloc, EmployeeTasksState>(
                builder: (context, state) {
                  if (state is EmployeeTasksLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (state is EmployeeTasksError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64.sp,
                            color: AppColors.error,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'حدث خطأ في تحميل المهام',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          ElevatedButton(
                            onPressed: _loadTasks,
                            child: const Text('إعادة المحاولة'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state is EmployeeTasksLoaded) {
                    if (state.tasks.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.task_alt,
                              size: 64.sp,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              'لا توجد مهام',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        _loadTasks();
                      },
                      child: ListView.builder(
                        padding: EdgeInsets.all(16.w),
                        itemCount: state.tasks.length,
                        itemBuilder: (context, index) {
                          final task = state.tasks[index];
                          final employee = _employees.firstWhere(
                            (e) => e.id == task.employeeId,
                            orElse: () => EmployeeModel(
                              id: '',
                              name: 'موظف غير معروف',
                              email: '',
                              role: 'admin',
                              employeeType: 'admin',
                              phone: '',
                              isActive: false,
                              createdAt: DateTime.now(),
                              updatedAt: DateTime.now(),
                            ),
                          );

                          return TaskCard(
                            task: task,
                            employee: employee,
                            onEdit: () => _showEditTaskDialog(task),
                            onDelete: () => _deleteTask(task),
                            onStatusChanged: (newStatus) => _updateTaskStatus(task, newStatus),
                          );
                        },
                      ),
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
