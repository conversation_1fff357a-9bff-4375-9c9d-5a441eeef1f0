import 'package:equatable/equatable.dart';

class FinancialSummaryModel extends Equatable {
  // إحصائيات المبيعات
  final double totalSales; // إجمالي المبيعات (final_amount)
  final double totalPaidSales; // المبيعات المدفوعة (paid_amount)
  final double totalRemainingSales; // المبالغ المتبقية (remaining_amount)
  final double totalCommissions; // إجمالي العمولات (commission_amount)
  final double totalOriginalSales; // إجمالي أصل المبيعات (original_price)
  final double totalProfits; // إجمالي الأرباح (المبيعات - أصل المبيعات - العمولات)
  final int totalSalesCount; // عدد الفواتير
  final int totalProductsCount; // عدد المنتجات المباعة

  // إحصائيات المصروفات
  final double totalExpenses; // إجمالي المصروفات
  final double suppliesExpenses; // مصروفات المستلزمات
  final double maintenanceExpenses; // مصروفات الصيانة
  final double utilitiesExpenses; // مصر��فات الخدمات العامة
  final double servicesExpenses; // مصروفات الخدمات
  final double otherExpenses; // مصروفات أخرى
  final int totalExpensesCount; // عدد المصروفات

  // إحصائيات الحجوزات
  final double totalAppointmentRevenue; // إجمالي إيرادات الحجوزات (جميع الحجوزات)
  final double totalPaidAppointments; // الحجوزات المدفوعة (جميع الحجوزات)
  final double totalRemainingAppointments; // الحجوزات المتبقية
  final double appointmentsDifference; // فارق الحجوزات (الإيرادات - المتبقي)
  final int totalAppointmentsCount; // عدد الحجوزات

  // الإجماليات النهائية
  final double totalIncome; // إجمالي الدخل (مبيعات + حجوزات)
  final double netProfit; // صافي الربح (الدخل - المصروفات)
  final double totalOutstanding; // إجمالي المتبقي (مبيعات + حجوزات)

  const FinancialSummaryModel({
    // المبيعات
    this.totalSales = 0.0,
    this.totalPaidSales = 0.0,
    this.totalRemainingSales = 0.0,
    this.totalCommissions = 0.0,
    this.totalOriginalSales = 0.0,
    this.totalProfits = 0.0,
    this.totalSalesCount = 0,
    this.totalProductsCount = 0,
    
    // ال��صروفات
    this.totalExpenses = 0.0,
    this.suppliesExpenses = 0.0,
    this.maintenanceExpenses = 0.0,
    this.utilitiesExpenses = 0.0,
    this.servicesExpenses = 0.0,
    this.otherExpenses = 0.0,
    this.totalExpensesCount = 0,
    
    // الحجوزات
    this.totalAppointmentRevenue = 0.0,
    this.totalPaidAppointments = 0.0,
    this.totalRemainingAppointments = 0.0,
    this.appointmentsDifference = 0.0,
    this.totalAppointmentsCount = 0,
    
    // الإجماليات
    this.totalIncome = 0.0,
    this.netProfit = 0.0,
    this.totalOutstanding = 0.0,
  });

  factory FinancialSummaryModel.fromData({
    required List<Map<String, dynamic>> salesData,
    required List<Map<String, dynamic>> expensesData,
    required List<Map<String, dynamic>> appointmentsData,
    required List<Map<String, dynamic>> salesWithProductsData,
  }) {
    // حساب إحصائيات المبيعات من جميع الفواتير
    double totalSales = 0.0; // إجمالي المبيعات من final_amount
    double totalPaidSales = 0.0; // إجمالي المدفوع من paid_amount
    double totalRemainingSales = 0.0; // إجمالي المتبقي من remaining_amount
    double totalCommissions = 0.0; // إجمالي العمولات من commission_amount
    int totalSalesCount = salesData.length;

    // حسا�� البيانات الأساسية من جدول sales_invoices
    for (final sale in salesData) {
      final finalAmount = (sale['final_amount'] as num?)?.toDouble() ?? 0.0;
      final paidAmount = (sale['paid_amount'] as num?)?.toDouble() ?? 0.0;
      final remainingAmount = (sale['remaining_amount'] as num?)?.toDouble() ?? 0.0;
      final commissionAmount = (sale['commission_amount'] as num?)?.toDouble() ?? 0.0;
      
      totalSales += finalAmount;
      totalPaidSales += paidAmount;
      totalRemainingSales += remainingAmount;
      totalCommissions += commissionAmount;
    }

    // حساب أصل المبيعات وعدد المنتجات من بيانات المنتجات
    double totalOriginalSales = 0.0;
    int totalProductsCount = 0;

    for (final invoice in salesWithProductsData) {
      final items = invoice['invoice_items'] as List? ?? [];
      for (final item in items) {
        final quantity = (item['quantity'] as num?)?.toInt() ?? 0;
        final product = item['products'] as Map<String, dynamic>?;
        final originalPrice = (product?['original_price'] as num?)?.toDouble() ?? 0.0;
        
        totalOriginalSales += (originalPrice * quantity);
        totalProductsCount += quantity;
      }
    }

    // حساب الأرباح الصحيح: المبيعات - أصل المبيعات - العمول��ت
    final totalProfits = totalSales - totalOriginalSales - totalCommissions;

    // حساب إحصائيات المصروفات
    double totalExpenses = 0.0;
    double suppliesExpenses = 0.0;
    double maintenanceExpenses = 0.0;
    double utilitiesExpenses = 0.0;
    double servicesExpenses = 0.0;
    double otherExpenses = 0.0;
    int totalExpensesCount = expensesData.length;

    for (final expense in expensesData) {
      final amount = (expense['amount'] as num?)?.toDouble() ?? 0.0;
      final category = expense['category'] as String? ?? 'other';
      
      totalExpenses += amount;
      
      switch (category) {
        case 'supplies':
          suppliesExpenses += amount;
          break;
        case 'maintenance':
          maintenanceExpenses += amount;
          break;
        case 'utilities':
          utilitiesExpenses += amount;
          break;
        case 'services':
          servicesExpenses += amount;
          break;
        default:
          otherExpenses += amount;
          break;
      }
    }

    // حساب إحصائيات الحجوزات (جميع الحجوزات حتى الملغية)
    double totalAppointmentRevenue = 0.0;
    double totalPaidAppointments = 0.0;
    double totalRemainingAppointments = 0.0;
    int totalAppointmentsCount = appointmentsData.length;

    for (final appointment in appointmentsData) {
      final consultationFee = (appointment['consultation_fee'] as num?)?.toDouble() ?? 0.0;
      final paidAmount = (appointment['paid_amount'] as num?)?.toDouble() ?? 0.0;
      final remainingAmount = (appointment['remaining_amount'] as num?)?.toDouble() ?? 0.0;
      
      // حساب جميع الحجوزات حتى الملغية
      totalAppointmentRevenue += consultationFee;
      totalPaidAppointments += paidAmount;
      totalRemainingAppointments += remainingAmount;
    }

    // حساب فارق الحجوزات
    final appointmentsDifference = totalAppointmentRevenue - totalRemainingAppointments;

    // حساب الإجماليات النهائية الجديدة
    final totalIncome = totalSales + totalAppointmentRevenue; // المبيعات + الحجوزات
    final netProfit = totalAppointmentRevenue + totalProfits - totalExpenses; // إيرادات الحجوزات + أرباح المبيعات - المصروفات
    final totalOutstanding = totalRemainingSales + totalRemainingAppointments; // المبالغ المتبقية في المبيعات + الحجوزات المتبقية

    return FinancialSummaryModel(
      // المبيعات
      totalSales: totalSales,
      totalPaidSales: totalPaidSales,
      totalRemainingSales: totalRemainingSales,
      totalCommissions: totalCommissions,
      totalOriginalSales: totalOriginalSales,
      totalProfits: totalProfits,
      totalSalesCount: totalSalesCount,
      totalProductsCount: totalProductsCount,
      
      // المصروفات
      totalExpenses: totalExpenses,
      suppliesExpenses: suppliesExpenses,
      maintenanceExpenses: maintenanceExpenses,
      utilitiesExpenses: utilitiesExpenses,
      servicesExpenses: servicesExpenses,
      otherExpenses: otherExpenses,
      totalExpensesCount: totalExpensesCount,
      
      // الحجوزات
      totalAppointmentRevenue: totalAppointmentRevenue,
      totalPaidAppointments: totalPaidAppointments,
      totalRemainingAppointments: totalRemainingAppointments,
      appointmentsDifference: appointmentsDifference,
      totalAppointmentsCount: totalAppointmentsCount,
      
      // الإجماليات
      totalIncome: totalIncome,
      netProfit: netProfit,
      totalOutstanding: totalOutstanding,
    );
  }

  @override
  List<Object?> get props => [
    totalSales,
    totalPaidSales,
    totalRemainingSales,
    totalCommissions,
    totalOriginalSales,
    totalProfits,
    totalSalesCount,
    totalProductsCount,
    totalExpenses,
    suppliesExpenses,
    maintenanceExpenses,
    utilitiesExpenses,
    servicesExpenses,
    otherExpenses,
    totalExpensesCount,
    totalAppointmentRevenue,
    totalPaidAppointments,
    totalRemainingAppointments,
    appointmentsDifference,
    totalAppointmentsCount,
    totalIncome,
    netProfit,
    totalOutstanding,
  ];
}