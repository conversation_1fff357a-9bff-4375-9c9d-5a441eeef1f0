import 'package:equatable/equatable.dart';
import '../../../../core/models/clinic_info_model.dart';

abstract class ClinicInfoEvent extends Equatable {
  const ClinicInfoEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllClinicInfo extends ClinicInfoEvent {
  const LoadAllClinicInfo();
}

class LoadClinicInfoByType extends ClinicInfoEvent {
  final String infoType;

  const LoadClinicInfoByType({required this.infoType});

  @override
  List<Object?> get props => [infoType];
}

class LoadPhoneNumbers extends ClinicInfoEvent {
  const LoadPhoneNumbers();
}

class LoadEmailAddresses extends ClinicInfoEvent {
  const LoadEmailAddresses();
}

class LoadSocialMediaLinks extends ClinicInfoEvent {
  const LoadSocialMediaLinks();
}

class LoadAddresses extends ClinicInfoEvent {
  const LoadAddresses();
}

class LoadWorkingHours extends ClinicInfoEvent {
  const LoadWorkingHours();
}

class LoadWebsiteInfo extends ClinicInfoEvent {
  const LoadWebsiteInfo();
}

class LoadClinicInfoGrouped extends ClinicInfoEvent {
  const LoadClinicInfoGrouped();
}

class AddClinicInfo extends ClinicInfoEvent {
  final ClinicInfoModel clinicInfo;

  const AddClinicInfo({required this.clinicInfo});

  @override
  List<Object?> get props => [clinicInfo];
}

class UpdateClinicInfo extends ClinicInfoEvent {
  final ClinicInfoModel clinicInfo;

  const UpdateClinicInfo({required this.clinicInfo});

  @override
  List<Object?> get props => [clinicInfo];
}

class DeleteClinicInfo extends ClinicInfoEvent {
  final String clinicInfoId;

  const DeleteClinicInfo({required this.clinicInfoId});

  @override
  List<Object?> get props => [clinicInfoId];
}

class ToggleClinicInfoStatus extends ClinicInfoEvent {
  final String clinicInfoId;
  final bool isActive;

  const ToggleClinicInfoStatus({
    required this.clinicInfoId,
    required this.isActive,
  });

  @override
  List<Object?> get props => [clinicInfoId, isActive];
}

class SearchClinicInfo extends ClinicInfoEvent {
  final String query;

  const SearchClinicInfo({required this.query});

  @override
  List<Object?> get props => [query];
}

class RefreshClinicInfo extends ClinicInfoEvent {
  const RefreshClinicInfo();
}
