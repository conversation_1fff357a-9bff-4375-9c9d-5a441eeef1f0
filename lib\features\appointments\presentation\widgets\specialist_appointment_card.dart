import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/utils/time_utils.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../data/repositories/appointments_repository.dart';

class SpecialistAppointmentCard extends StatefulWidget {
  final AppointmentModel appointment;
  final PatientModel patient;
  final VoidCallback? onTap;
  final VoidCallback? onStatusChange;
  final VoidCallback? onMedicalRecord;

  const SpecialistAppointmentCard({
    super.key,
    required this.appointment,
    required this.patient,
    this.onTap,
    this.onStatusChange,
    this.onMedicalRecord,
  });

  @override
  State<SpecialistAppointmentCard> createState() => _SpecialistAppointmentCardState();
}

class _SpecialistAppointmentCardState extends State<SpecialistAppointmentCard> {
  List<AppointmentModel>? _groupAppointments;
  bool _isLoadingGroup = false;
  String? _employeeName;
  String? _employeeSpecialization;
  String? _treatmentType;
  String? _timeSlotStart;
  String? _timeSlotEnd;
  bool _isLoadingDetails = false;

  @override
  void initState() {
    super.initState();
    debugPrint('🔄 SpecialistAppointmentCard: initState for appointment ${widget.appointment.id}');
    _initializeData();
  }

  @override
  void didUpdateWidget(SpecialistAppointmentCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reload data if appointment changed
    if (oldWidget.appointment.id != widget.appointment.id ||
        oldWidget.appointment.timeSlotId != widget.appointment.timeSlotId ||
        oldWidget.appointment.employeeId != widget.appointment.employeeId ||
        oldWidget.appointment.updatedAt != widget.appointment.updatedAt) {
      debugPrint('🔄 SpecialistAppointmentCard: Appointment data changed, reloading...');
      _initializeData();
    }
  }

  void _initializeData() {
    debugPrint('🔄 SpecialistAppointmentCard: _initializeData for appointment ${widget.appointment.id}, timeSlot: ${widget.appointment.timeSlotId}');

    // Reset all data and force rebuild
    setState(() {
      _employeeName = null;
      _employeeSpecialization = null;
      _treatmentType = null;
      _timeSlotStart = null;
      _timeSlotEnd = null;
      _isLoadingDetails = true;

      if (widget.appointment.isMultipleBooking && widget.appointment.multipleBookingGroupId != null) {
        _loadGroupAppointments();
      }
    });

    _loadEmployeeAndTreatmentDetails();
  }

  Future<void> _loadGroupAppointments() async {
    if (_isLoadingGroup) return;
    
    setState(() {
      _isLoadingGroup = true;
    });

    try {
      final appointmentsRepo = AppointmentsRepository();
      final groupAppointments = await appointmentsRepo.getAppointmentsByGroupId(
        widget.appointment.multipleBookingGroupId.toString(),
      );

      if (mounted) {
        setState(() {
          _groupAppointments = groupAppointments;
          _isLoadingGroup = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingGroup = false;
        });
      }
      debugPrint('❌ Error loading group appointments: $e');
    }
  }

  Future<void> _loadEmployeeAndTreatmentDetails() async {
    if (!mounted) return;

    debugPrint('🔄 SpecialistAppointmentCard: _loadEmployeeAndTreatmentDetails for appointment ${widget.appointment.id}, timeSlot: ${widget.appointment.timeSlotId}');

    setState(() {
      _isLoadingDetails = true;
    });

    try {
      // Load employee details from admins table
      if (widget.appointment.employeeId != null) {
        debugPrint('🔍 Loading employee details for ID: ${widget.appointment.employeeId}');
        final employeeResponse = await Supabase.instance.client
            .from('admins')
            .select('''
              name,
              specialization_id,
              specializations:specialization_id (
                name
              )
            ''')
            .eq('id', widget.appointment.employeeId!)
            .maybeSingle();

        if (employeeResponse != null) {
          _employeeName = employeeResponse['name'];
          debugPrint('✅ Employee loaded: $_employeeName');

          // Get specialization name from the joined data
          if (employeeResponse['specializations'] != null) {
            _employeeSpecialization = employeeResponse['specializations']['name'];
            debugPrint('✅ Specialization loaded: $_employeeSpecialization');
          } else {
            debugPrint('❌ No specialization found for employee');
          }
        } else {
          debugPrint('❌ No employee found for ID: ${widget.appointment.employeeId}');
        }
      } else {
        debugPrint('❌ No employeeId in appointment: ${widget.appointment.id}');
      }

      // Load treatment types from patient
      final treatmentResponse = await Supabase.instance.client
          .from('patients')
          .select('treatment_types')
          .eq('id', widget.patient.id)
          .maybeSingle();

      if (treatmentResponse != null && treatmentResponse['treatment_types'] != null) {
        final List<dynamic> treatmentTypes = treatmentResponse['treatment_types'] as List<dynamic>;
        if (treatmentTypes.isNotEmpty) {
          _treatmentType = _getTreatmentTypeDisplayName(treatmentTypes.first.toString());
        }
      }

      // Load time slot details
      if (widget.appointment.timeSlotId != null) {
        debugPrint('🔍 Loading time slot details for ID: ${widget.appointment.timeSlotId}');
        final timeSlotResponse = await Supabase.instance.client
            .from('time_slots')
            .select('start_time, end_time')
            .eq('id', widget.appointment.timeSlotId!)
            .maybeSingle();

        if (timeSlotResponse != null) {
          _timeSlotStart = timeSlotResponse['start_time'];
          _timeSlotEnd = timeSlotResponse['end_time'];
          debugPrint('✅ Time slot loaded: $_timeSlotStart - $_timeSlotEnd');
          debugPrint('✅ Formatted time: ${_formatTime(_timeSlotStart!)} - ${_formatTime(_timeSlotEnd!)}');
        } else {
          debugPrint('❌ No time slot found for ID: ${widget.appointment.timeSlotId}');
        }
      } else {
        debugPrint('❌ No timeSlotId in appointment: ${widget.appointment.id}');
      }

      if (mounted) {
        setState(() {
          _isLoadingDetails = false;
        });
        debugPrint('✅ SpecialistAppointmentCard: Finished loading details for appointment ${widget.appointment.id}');
        debugPrint('   Employee: $_employeeName, Specialization: $_employeeSpecialization');
        debugPrint('   Time: $_timeSlotStart - $_timeSlotEnd');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingDetails = false;
        });
      }
      debugPrint('❌ Error loading employee and treatment details: $e');
    }
  }

  String _getTreatmentTypeDisplayName(String treatmentType) {
    switch (treatmentType) {
      case 'speech':
        return 'علاج النطق';
      case 'hearing':
        return 'علاج السمع';
      case 'behavior':
        return 'علاج السلوك';
      case 'occupational':
        return 'العلاج الوظيفي';
      case 'physiotherapy':
        return 'العلاج الطبيعي';
      case 'speech_therapy':
        return 'علاج النطق';
      case 'hearing_therapy':
        return 'علاج السمع';
      case 'behavior_therapy':
        return 'علاج السلوك';
      case 'occupational_therapy':
        return 'العلاج الوظيفي';
      default:
        return 'علاج عام';
    }
  }

  String _formatTime(String timeString) {
    try {
      final time = DateTime.parse('2000-01-01 $timeString');
      final hour = time.hour;
      final minute = time.minute;
      final period = hour >= 12 ? 'م' : 'ص';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return timeString;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: _getStatusColor().withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Status Indicator
                  Container(
                    width: 12.w,
                    height: 12.h,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  
                  // Patient Name
                  Expanded(
                    child: Text(
                      widget.patient.name,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // Multiple Booking Badge
                  if (widget.appointment.isMultipleBooking) ...[
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        _groupAppointments != null
                            ? 'متعدد (${_groupAppointments!.length}) - جلسة ${widget.appointment.bookingSequence}'
                            : 'متعدد - جلسة ${widget.appointment.bookingSequence}',
                        style: TextStyle(
                          fontSize: 9.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                  ],
                  
                  // Status Badge
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      _getStatusDisplayName(),
                      style: TextStyle(
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                        color: _getStatusColor(),
                      ),
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 8.h),

              // Patient Info Row
              Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    size: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    'رقم المريض: ${widget.patient.patientId ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  if (_treatmentType != null) ...[
                    Icon(
                      Icons.medical_services_outlined,
                      size: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Text(
                        _treatmentType!,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),
              
              SizedBox(height: 6.h),

              // Date and Time Row
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '${widget.appointment.appointmentDate.day}/${widget.appointment.appointmentDate.month}/${widget.appointment.appointmentDate.year}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Icon(
                    Icons.access_time_outlined,
                    size: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    TimeUtils.getArabicDayName(widget.appointment.appointmentDate.weekday),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  if (_timeSlotStart != null && _timeSlotEnd != null) ...[
                    SizedBox(width: 16.w),
                    Icon(
                      Icons.schedule_outlined,
                      size: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      '${_formatTime(_timeSlotStart!)} - ${_formatTime(_timeSlotEnd!)}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),

              SizedBox(height: 6.h),

              // Employee Info Row
              if (_isLoadingDetails) ...[
                Row(
                  children: [
                    SizedBox(
                      width: 12.w,
                      height: 12.h,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'جاري تحميل بيانات الأخصائي...',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 6.h),
              ] else if (_employeeName != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.person_pin_outlined,
                      size: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'د. $_employeeName',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (_employeeSpecialization != null) ...[
                      SizedBox(width: 16.w),
                      Icon(
                        Icons.work_outline,
                        size: 16.sp,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Text(
                          _employeeSpecialization!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(height: 6.h),
              ],

              // إخفاء المعلومات المالية للأخصائي
              // Financial Info - Hidden for specialists
              
              SizedBox(height: 8.h),

              // Action Buttons - للأخصائي فقط تغيير الحالة والسجل الطبي
              Row(
                children: [
                  if (widget.onStatusChange != null)
                    Expanded(
                      child: TextButton.icon(
                        onPressed: widget.onStatusChange,
                        icon: Icon(Icons.edit_note_outlined, size: 14.sp),
                        label: Text('تغيير الحالة'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.warning,
                          textStyle: TextStyle(fontSize: 10.sp),
                          padding: EdgeInsets.zero,
                        ),
                      ),
                    ),
                  if (widget.onMedicalRecord != null)
                    Expanded(
                      child: TextButton.icon(
                        onPressed: widget.onMedicalRecord,
                        icon: Icon(Icons.medical_information_outlined, size: 14.sp),
                        label: Text('السجل الطبي'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.info,
                          textStyle: TextStyle(fontSize: 10.sp),
                          padding: EdgeInsets.zero,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.appointment.status) {
      case 'confirmed':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      case 'no_show':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusDisplayName() {
    switch (widget.appointment.status) {
      case 'confirmed':
        return 'مؤكدة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'no_show':
        return 'لم يحضر';
      default:
        return 'غير محدد';
    }
  }
}