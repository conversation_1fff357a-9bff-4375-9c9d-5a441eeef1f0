const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { createClient } = require('@supabase/supabase-js');

// Initialize Firebase Admin
admin.initializeApp();

// Initialize Supabase
const supabaseUrl = 'https://xwxeauofbzedfzaogzzy.supabase.co';
const supabaseKey = functions.config().supabase.key;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cloud Function تعمل كل دقيقة
exports.sendScheduledNotifications = functions
  .pubsub
  .schedule('every 1 minutes')
  .timeZone('Asia/Riyadh')
  .onRun(async (context) => {
    console.log('🔍 Checking for due notifications...');

    try {
      // Get current time in HH:mm format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const currentDayOfWeek = now.getDay() === 0 ? 7 : now.getDay(); // Convert Sunday from 0 to 7

      console.log(`⏰ Current time: ${currentTime}, Day: ${currentDayOfWeek}`);

      // Get due notifications from Supabase
      const { data: notifications, error } = await supabase
        .from('scheduled_notifications')
        .select('*')
        .eq('is_active', true)
        .eq('scheduled_time', currentTime);

      if (error) {
        console.error('❌ Error fetching notifications:', error);
        return null;
      }

      // Filter by day of week
      const dueNotifications = notifications.filter(notification => {
        const daysOfWeek = notification.days_of_week || [];
        return daysOfWeek.includes(currentDayOfWeek);
      });

      console.log(`📊 Found ${dueNotifications.length} due notifications`);

      // Send each notification
      let successCount = 0;
      for (const notification of dueNotifications) {
        const success = await sendNotificationToPatient(notification);
        if (success) successCount++;
      }

      if (dueNotifications.length > 0) {
        console.log(`✅ Successfully sent ${successCount}/${dueNotifications.length} notifications`);
      }

      return {
        processed: dueNotifications.length,
        successful: successCount,
        time: currentTime
      };

    } catch (error) {
      console.error('❌ Error in scheduled notifications:', error);
      return null;
    }
  });

// Function to send notification to a patient
async function sendNotificationToPatient(notification) {
  try {
    const patientId = notification.patient_id;
    console.log(`📤 Processing notification for patient: ${patientId}`);

    // Get patient's auth_id
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single();

    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`, patientError);
      return false;
    }

    console.log(`👤 Found patient: ${patient.name} (auth_id: ${patient.auth_id})`);

    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true);

    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId}`, tokensError);
      return false;
    }

    console.log(`📱 Found ${tokens.length} FCM tokens for patient`);

    // Prepare notification message
    const title = getNotificationTitle(notification.notification_type);
    const body = `مرحباً ${patient.name}، ${notification.body}`;

    // Send to all tokens
    let sentCount = 0;
    for (const tokenData of tokens) {
      const message = {
        token: tokenData.fcm_token,
        notification: {
          title: title,
          body: body,
        },
        data: {
          type: notification.notification_type,
          patient_id: patientId,
          reminder_id: notification.reminder_id || '',
          scheduled_notification_id: notification.id,
        },
        android: {
          priority: 'high',
          notification: {
            channel_id: 'diet_rx_notifications',
            sound: 'default',
            priority: 'high',
          },
        },
        apns: {
          headers: {
            'apns-priority': '10',
          },
          payload: {
            aps: {
              alert: {
                title: title,
                body: body,
              },
              sound: 'default',
              badge: 1,
            },
          },
        },
      };

      try {
        const response = await admin.messaging().send(message);
        console.log(`✅ Notification sent successfully: ${response}`);
        sentCount++;

        // Log to Supabase
        await logNotification({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'sent',
          firebase_response: { messageId: response },
        });

      } catch (sendError) {
        console.error(`❌ Failed to send notification:`, sendError);

        // Log error to Supabase
        await logNotification({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'failed',
          error_message: sendError.message,
        });
      }
    }

    return sentCount > 0;

  } catch (error) {
    console.error('❌ Error sending notification to patient:', error);
    return false;
  }
}

// Helper function to get notification title
function getNotificationTitle(notificationType) {
  switch (notificationType) {
    case 'meal':
      return '🍽️ تذكير الوجبة';
    case 'exercise':
      return '🏃‍♂️ تذكير النشاط البدني';
    case 'medication':
      return '💊 تذكير الدواء';
    case 'water':
      return '💧 تذكير شرب الماء';
    default:
      return '🔔 تذكير من Diet Rx';
  }
}

// Helper function to log notifications
async function logNotification(logData) {
  try {
    const { error } = await supabase.from('notification_logs').insert({
      ...logData,
      created_at: new Date().toISOString(),
    });

    if (error) {
      console.error('❌ Error logging notification:', error);
    }
  } catch (error) {
    console.error('❌ Error logging notification:', error);
  }
}

// Test function للاختبار اليدوي
exports.testNotification = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    const patientId = req.query.patientId || (req.body && req.body.patientId) || '**********';

    console.log(`🧪 Testing notification for patient: ${patientId}`);

    const testNotification = {
      id: 'test-notification-' + Date.now(),
      patient_id: patientId,
      notification_type: 'test',
      title: 'اختبار الإشعارات',
      body: 'هذا إشعار تجريبي من Firebase Cloud Functions',
      reminder_id: null,
    };

    const success = await sendNotificationToPatient(testNotification);

    res.json({
      success: success,
      message: success ? 'تم إرسال الإشعار التجريبي بنجاح' : 'فشل في إرسال الإشعار التجريبي',
      patientId: patientId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Test notification error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Function للتحقق من حالة النظام
exports.healthCheck = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');

  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('scheduled_notifications')
      .select('count(*)')
      .eq('is_active', true);

    const activeNotifications = (data && data[0] && data[0].count) || 0;

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      supabase_connected: !error,
      active_notifications: activeNotifications,
      firebase_admin_initialized: !!admin.apps.length,
    });

  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});
