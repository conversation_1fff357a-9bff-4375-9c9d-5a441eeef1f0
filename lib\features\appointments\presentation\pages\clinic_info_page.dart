import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/clinic_info_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/clinic_info_bloc.dart';
import '../bloc/clinic_info_event.dart';
import '../bloc/clinic_info_state.dart';
import '../widgets/clinic_info_form_dialog.dart';

class ClinicInfoPage extends StatefulWidget {
  const ClinicInfoPage({super.key});

  @override
  State<ClinicInfoPage> createState() => _ClinicInfoPageState();
}

class _ClinicInfoPageState extends State<ClinicInfoPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Load data when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<ClinicInfoBloc>().add(const LoadAllClinicInfo());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocConsumer<ClinicInfoBloc, ClinicInfoState>(
        listener: (context, state) {
          if (state is ClinicInfoLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is ClinicInfoLoaded) {
            LoadingDialog.hide(context);
          } else if (state is ClinicInfoError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ClinicInfoCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة المعلومات بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ClinicInfoUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث المعلومات بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ClinicInfoDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف المعلومات بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is ClinicInfoLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ClinicInfoLoaded) {
            return _buildClinicInfoView(state);
          }

          if (state is ClinicInfoError) {
            return _buildErrorView(state.message);
          }

          // Initial state
          return _buildInitialView();
        },
      ),
    );
  }

  Widget _buildClinicInfoView(ClinicInfoLoaded state) {
    return RefreshIndicator(
      onRefresh: _onRefreshClinicInfo,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          // Add button and search bar
          _buildTopActionBar(),
          SizedBox(height: 16.h),

          // Header (smaller)
          _buildCompactHeader(),
          SizedBox(height: 20.h),

          // Phone Numbers Section
          if (state.phoneNumbers.isNotEmpty) ...[
            _buildSectionHeader('أرقام الهواتف', Icons.phone, AppColors.primary),
            SizedBox(height: 12.h),
            ...state.phoneNumbers.map((info) => _buildInfoCard(info)),
            SizedBox(height: 20.h),
          ],

          // Email Addresses Section
          if (state.emailAddresses.isNotEmpty) ...[
            _buildSectionHeader('البريد الإلكتروني', Icons.email, AppColors.secondary),
            SizedBox(height: 12.h),
            ...state.emailAddresses.map((info) => _buildInfoCard(info)),
            SizedBox(height: 20.h),
          ],

          // Social Media Section
          if (state.socialMediaLinks.isNotEmpty) ...[
            _buildSectionHeader('وسائل التواصل الاجتماعي', Icons.share, AppColors.success),
            SizedBox(height: 12.h),
            ...state.socialMediaLinks.map((info) => _buildInfoCard(info)),
            SizedBox(height: 20.h),
          ],

          // Address Section
          if (state.addresses.isNotEmpty) ...[
            _buildSectionHeader('العنوان', Icons.location_on, AppColors.warning),
            SizedBox(height: 12.h),
            ...state.addresses.map((info) => _buildInfoCard(info)),
            SizedBox(height: 20.h),
          ],

          // Working Hours Section
          if (state.workingHours.isNotEmpty) ...[
            _buildSectionHeader('مواعيد العمل', Icons.access_time, AppColors.info),
            SizedBox(height: 12.h),
            ...state.workingHours.map((info) => _buildInfoCard(info)),
            SizedBox(height: 20.h),
          ],

          // Website Section
          if (state.websiteInfo.isNotEmpty) ...[
            _buildSectionHeader('الموقع الإلكتروني', Icons.web, AppColors.primary),
            SizedBox(height: 12.h),
            ...state.websiteInfo.map((info) => _buildInfoCard(info)),
            SizedBox(height: 20.h),
          ],
        ],
        ),
      ),
    );
  }

  Widget _buildTopActionBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button
          ElevatedButton.icon(
            onPressed: _showAddClinicInfoDialog,
            icon: const Icon(Icons.add, size: 20),
            label: const Text('إضافة معلومات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),

          const Spacer(),

          // Refresh button
          IconButton(
            onPressed: () => context.read<ClinicInfoBloc>().add(const RefreshClinicInfo()),
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildCompactHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_hospital,
            size: 32.w,
            color: AppColors.white,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات العيادة',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                Text(
                  'جميع المعلومات اللازمة للتواصل',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(icon, color: color, size: 20.w),
        ),
        SizedBox(width: 12.w),
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(ClinicInfoModel info) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: _getInfoTypeColor(info.infoType).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Icon(
              _getInfoIcon(info),
              color: _getInfoTypeColor(info.infoType),
              size: 16.w,
            ),
          ),
          SizedBox(width: 10.w),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  info.displayName,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  info.infoValue,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Copy button
              IconButton(
                onPressed: () => _copyToClipboard(info.infoValue),
                icon: Icon(
                  Icons.copy,
                  size: 20.w,
                  color: AppColors.textSecondary,
                ),
                tooltip: 'نسخ',
              ),

              // Action button (call, email, open link)
              if (info.isPhoneNumber || info.isEmailAddress || info.isUrl)
                IconButton(
                  onPressed: () => _performAction(info),
                  icon: Icon(
                    _getActionIcon(info),
                    size: 20.w,
                    color: _getInfoTypeColor(info.infoType),
                  ),
                  tooltip: _getActionTooltip(info),
                ),

              // More options menu
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showEditClinicInfoDialog(info);
                      break;
                    case 'delete':
                      _deleteClinicInfo(info);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => context.read<ClinicInfoBloc>().add(const LoadAllClinicInfo()),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_hospital,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل معلومات العيادة...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => context.read<ClinicInfoBloc>().add(const LoadAllClinicInfo()),
            child: const Text('تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  Color _getInfoTypeColor(String infoType) {
    switch (infoType) {
      case 'phone':
        return AppColors.primary;
      case 'email':
        return AppColors.secondary;
      case 'social_media':
        return AppColors.success;
      case 'address':
        return AppColors.warning;
      case 'working_hours':
        return AppColors.info;
      case 'website':
        return AppColors.primary;
      default:
        return AppColors.gray400;
    }
  }

  IconData _getInfoIcon(ClinicInfoModel info) {
    switch (info.infoType) {
      case 'phone':
        return Icons.phone;
      case 'email':
        return Icons.email;
      case 'social_media':
        return _getSocialMediaIcon(info.infoKey);
      case 'address':
        return Icons.location_on;
      case 'working_hours':
        return Icons.access_time;
      case 'website':
        return Icons.web;
      default:
        return Icons.info;
    }
  }

  IconData _getSocialMediaIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return Icons.facebook;
      case 'instagram':
        return Icons.camera_alt;
      case 'twitter':
        return Icons.alternate_email;
      case 'whatsapp':
        return Icons.chat;
      default:
        return Icons.share;
    }
  }

  IconData _getActionIcon(ClinicInfoModel info) {
    if (info.isPhoneNumber) {
      return Icons.call;
    } else if (info.isEmailAddress) {
      return Icons.mail;
    } else if (info.isUrl) {
      return Icons.open_in_new;
    }
    return Icons.info;
  }

  String _getActionTooltip(ClinicInfoModel info) {
    if (info.isPhoneNumber) {
      return 'اتصال';
    } else if (info.isEmailAddress) {
      return 'إرسال بريد';
    } else if (info.isUrl) {
      return 'فتح الرابط';
    }
    return 'معلومات';
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ النص'),
        backgroundColor: AppColors.success,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _performAction(ClinicInfoModel info) async {
    try {
      if (info.isPhoneNumber) {
        final uri = Uri(scheme: 'tel', path: info.infoValue);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          _showErrorSnackBar('لا يمكن فتح تطبيق الهاتف');
        }
      } else if (info.isEmailAddress) {
        final uri = Uri(
          scheme: 'mailto',
          path: info.infoValue,
          query: 'subject=استفسار من تطبيق العيادة',
        );
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          _showErrorSnackBar('لا يمكن فتح تطبيق البريد الإلكتروني');
        }
      } else if (info.isUrl) {
        final uri = Uri.parse(info.infoValue);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          _showErrorSnackBar('لا يمكن فتح الرابط');
        }
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تنفيذ العملية');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showAddClinicInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<ClinicInfoBloc>(),
        child: const ClinicInfoFormDialog(),
      ),
    );
  }

  void _showEditClinicInfoDialog(ClinicInfoModel clinicInfo) {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<ClinicInfoBloc>(),
        child: ClinicInfoFormDialog(clinicInfo: clinicInfo),
      ),
    );
  }

  void _deleteClinicInfo(ClinicInfoModel clinicInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${clinicInfo.displayName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ClinicInfoBloc>().add(
                DeleteClinicInfo(clinicInfoId: clinicInfo.id),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefreshClinicInfo() async {
    // تحديث بيانات معلومات العيادة
    context.read<ClinicInfoBloc>().add(const LoadAllClinicInfo());
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}