import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/returns_repository.dart';
import 'returns_event.dart';
import 'returns_state.dart';

class ReturnsBloc extends Bloc<ReturnsEvent, ReturnsState> {
  final ReturnsRepository _returnsRepository;
  static const int _pageSize = 20;

  ReturnsBloc({required ReturnsRepository returnsRepository})
      : _returnsRepository = returnsRepository,
        super(ReturnsInitial()) {
    on<LoadReturns>(_onLoadReturns);
    on<LoadMoreReturns>(_onLoadMoreReturns);
    on<RefreshReturns>(_onRefreshReturns);
    on<CreateReturn>(_onCreateReturn);
    on<LoadReturnDetails>(_onLoadReturnDetails);
    on<ApproveReturn>(_onApproveReturn);
    on<CompleteReturn>(_onCompleteReturn);
    on<CancelReturn>(_onCancelReturn);
    on<LoadReturnableItems>(_onLoadReturnableItems);
    on<SearchReturns>(_onSearchReturns);
    on<FilterReturnsByStatus>(_onFilterReturnsByStatus);
    on<ClearReturnsFilters>(_onClearReturnsFilters);
    on<GenerateReturnNumber>(_onGenerateReturnNumber);
  }

  Future<void> _onLoadReturns(LoadReturns event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Loading returns - Page: ${event.page}', name: 'ReturnsBloc');
      emit(ReturnsLoading());

      final returns = await _returnsRepository.getAllReturns(
        page: event.page,
        limit: _pageSize,
        searchQuery: event.searchQuery,
        status: event.status,
      );

      emit(ReturnsLoaded(
        returns: returns,
        hasReachedMax: returns.length < _pageSize,
        currentPage: event.page,
        searchQuery: event.searchQuery,
        statusFilter: event.status,
      ));

      developer.log('Loaded ${returns.length} returns', name: 'ReturnsBloc');
    } catch (e) {
      developer.log('Error loading returns: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في تحميل المرتجعات: $e'));
    }
  }

  Future<void> _onLoadMoreReturns(LoadMoreReturns event, Emitter<ReturnsState> emit) async {
    final currentState = state;
    if (currentState is! ReturnsLoaded || currentState.hasReachedMax) return;

    try {
      developer.log('Loading more returns - Page: ${currentState.currentPage + 1}', name: 'ReturnsBloc');

      final newReturns = await _returnsRepository.getAllReturns(
        page: currentState.currentPage + 1,
        limit: _pageSize,
        searchQuery: currentState.searchQuery,
        status: currentState.statusFilter,
      );

      emit(currentState.copyWith(
        returns: [...currentState.returns, ...newReturns],
        hasReachedMax: newReturns.length < _pageSize,
        currentPage: currentState.currentPage + 1,
      ));

      developer.log('Loaded ${newReturns.length} more returns', name: 'ReturnsBloc');
    } catch (e) {
      developer.log('Error loading more returns: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في تحميل المزيد من المرتجعات: $e'));
    }
  }

  Future<void> _onRefreshReturns(RefreshReturns event, Emitter<ReturnsState> emit) async {
    final currentState = state;
    if (currentState is ReturnsLoaded) {
      add(LoadReturns(
        searchQuery: currentState.searchQuery,
        status: currentState.statusFilter,
      ));
    } else {
      add(const LoadReturns());
    }
  }

  Future<void> _onCreateReturn(CreateReturn event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Creating return: ${event.returnModel.returnNumber}', name: 'ReturnsBloc');
      emit(ReturnsLoading());

      final createdReturn = await _returnsRepository.createReturn(
        event.returnModel,
        event.items,
      );

      emit(ReturnCreated(createdReturn));
      developer.log('Return created successfully: ${createdReturn.id}', name: 'ReturnsBloc');

      // Refresh returns list
      add(RefreshReturns());
    } catch (e) {
      developer.log('Error creating return: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في إنشاء الاسترجاع: $e'));
    }
  }

  Future<void> _onLoadReturnDetails(LoadReturnDetails event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Loading return details: ${event.returnId}', name: 'ReturnsBloc');
      emit(ReturnsLoading());

      final returnModel = await _returnsRepository.getReturnById(event.returnId);
      emit(ReturnDetailsLoaded(returnModel));

      developer.log('Return details loaded successfully', name: 'ReturnsBloc');
    } catch (e) {
      developer.log('Error loading return details: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في تحميل تفاصيل الاسترجاع: $e'));
    }
  }

  Future<void> _onApproveReturn(ApproveReturn event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Approving return: ${event.returnId}', name: 'ReturnsBloc');

      final approvedReturn = await _returnsRepository.approveReturn(
        event.returnId,
        event.approvedBy,
      );

      emit(ReturnApproved(approvedReturn));
      developer.log('Return approved successfully', name: 'ReturnsBloc');

      // Refresh returns list
      add(RefreshReturns());
    } catch (e) {
      developer.log('Error approving return: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في الموافقة على الاسترجاع: $e'));
    }
  }

  Future<void> _onCompleteReturn(CompleteReturn event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Completing return: ${event.returnId}', name: 'ReturnsBloc');

      final completedReturn = await _returnsRepository.completeReturn(event.returnId);

      emit(ReturnCompleted(completedReturn));
      developer.log('Return completed successfully', name: 'ReturnsBloc');

      // Refresh returns list
      add(RefreshReturns());
    } catch (e) {
      developer.log('Error completing return: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في إكمال الاسترجاع: $e'));
    }
  }

  Future<void> _onCancelReturn(CancelReturn event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Cancelling return: ${event.returnId}', name: 'ReturnsBloc');

      final cancelledReturn = await _returnsRepository.cancelReturn(event.returnId);

      emit(ReturnCancelled(cancelledReturn));
      developer.log('Return cancelled successfully', name: 'ReturnsBloc');

      // Refresh returns list
      add(RefreshReturns());
    } catch (e) {
      developer.log('Error cancelling return: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في إلغاء الاسترجاع: $e'));
    }
  }

  Future<void> _onLoadReturnableItems(LoadReturnableItems event, Emitter<ReturnsState> emit) async {
    try {
      developer.log('Loading returnable items for invoice: ${event.invoiceId}', name: 'ReturnsBloc');
      emit(ReturnsLoading());

      final items = await _returnsRepository.getReturnableItems(event.invoiceId);

      emit(ReturnableItemsLoaded(
        items: items,
        invoiceId: event.invoiceId,
      ));

      developer.log('Loaded ${items.length} returnable items', name: 'ReturnsBloc');
    } catch (e) {
      developer.log('Error loading returnable items: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في تحميل العناصر القابلة للاسترجاع: $e'));
    }
  }

  Future<void> _onSearchReturns(SearchReturns event, Emitter<ReturnsState> emit) async {
    final currentState = state;
    if (currentState is ReturnsLoaded) {
      add(LoadReturns(
        searchQuery: event.query.isEmpty ? null : event.query,
        status: currentState.statusFilter,
      ));
    } else {
      add(LoadReturns(searchQuery: event.query.isEmpty ? null : event.query));
    }
  }

  Future<void> _onFilterReturnsByStatus(FilterReturnsByStatus event, Emitter<ReturnsState> emit) async {
    final currentState = state;
    if (currentState is ReturnsLoaded) {
      add(LoadReturns(
        searchQuery: currentState.searchQuery,
        status: event.status,
      ));
    } else {
      add(LoadReturns(status: event.status));
    }
  }

  Future<void> _onClearReturnsFilters(ClearReturnsFilters event, Emitter<ReturnsState> emit) async {
    add(const LoadReturns());
  }

  Future<void> _onGenerateReturnNumber(GenerateReturnNumber event, Emitter<ReturnsState> emit) async {
    try {
      final number = await _returnsRepository.generateReturnNumber();
      emit(ReturnNumberGenerated(number));
    } catch (e) {
      developer.log('Error generating return number: $e', name: 'ReturnsBloc');
      emit(ReturnsError('فشل في توليد رقم الاسترجاع: $e'));
    }
  }
}
