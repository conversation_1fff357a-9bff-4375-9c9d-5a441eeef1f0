import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../patients/presentation/bloc/patients_bloc.dart';
import '../../../patients/presentation/bloc/patients_event.dart';
import '../../../patients/presentation/bloc/patients_state.dart';

class PatientSelectorWidget extends StatefulWidget {
  final PatientModel? selectedPatient;
  final Function(PatientModel) onPatientSelected;

  const PatientSelectorWidget({
    super.key,
    this.selectedPatient,
    required this.onPatientSelected,
  });

  @override
  State<PatientSelectorWidget> createState() => _PatientSelectorWidgetState();
}

class _PatientSelectorWidgetState extends State<PatientSelectorWidget> {
  final _searchController = TextEditingController();
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    // Load patients when widget initializes
    context.read<PatientsBloc>().add(LoadAllPatients());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Selected patient display or search field
        if (widget.selectedPatient != null && !_isExpanded)
          _buildSelectedPatientCard()
        else
          _buildSearchField(),

        // Patients list
        if (_isExpanded) _buildPatientsList(),
      ],
    );
  }

  Widget _buildSelectedPatientCard() {
    final patient = widget.selectedPatient!;
    
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primary),
        borderRadius: BorderRadius.circular(12.r),
        color: AppColors.primary.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20.r,
            backgroundColor: AppColors.primary,
            child: Text(
              patient.name.isNotEmpty ? patient.name[0].toUpperCase() : 'م',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  patient.name,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                if (patient.phone!.isNotEmpty)
                  Text(
                    patient.phone!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                Text(
                  'رقم المريض: ${patient.patientId}',
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _isExpanded = true;
              });
            },
            icon: Icon(
              Icons.edit,
              color: AppColors.primary,
              size: 20.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return TextFormField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'ابحث عن مريض...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _isExpanded
            ? IconButton(
                onPressed: () {
                  setState(() {
                    _isExpanded = false;
                    _searchController.clear();
                  });
                },
                icon: const Icon(Icons.close),
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
      onTap: () {
        if (!_isExpanded) {
          setState(() {
            _isExpanded = true;
          });
        }
      },
      onChanged: (query) {
        context.read<PatientsBloc>().add(SearchPatients(query: query));
      },
      validator: (value) {
        if (widget.selectedPatient == null) {
          return 'يرجى اختيار مريض';
        }
        return null;
      },
    );
  }

  Widget _buildPatientsList() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      constraints: BoxConstraints(maxHeight: 200.h),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: BlocBuilder<PatientsBloc, PatientsState>(
        builder: (context, state) {
          if (state is PatientsLoading) {
            return Container(
              height: 100.h,
              alignment: Alignment.center,
              child: const CircularProgressIndicator(),
            );
          }

          if (state is PatientsError) {
            return Container(
              height: 100.h,
              alignment: Alignment.center,
              child: Text(
                'خطأ في تحميل المرضى',
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: 14.sp,
                ),
              ),
            );
          }

          if (state is PatientsLoaded) {
            // Filter patients based on search query
            final patients = state.searchQuery.isEmpty
                ? state.patients
                : state.patients.where((patient) =>
                    patient.name.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
                    (patient.phone?.contains(state.searchQuery) ?? false) ||
                    (patient.patientId?.toLowerCase().contains(state.searchQuery.toLowerCase()) ?? false)
                  ).toList();

            if (patients.isEmpty) {
              return Container(
                height: 100.h,
                alignment: Alignment.center,
                child: Text(
                  'لا توجد مرضى',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14.sp,
                  ),
                ),
              );
            }

            return ListView.separated(
              shrinkWrap: true,
              itemCount: patients.length,
              separatorBuilder: (context, index) => Divider(
                height: 1.h,
                color: AppColors.border,
              ),
              itemBuilder: (context, index) {
                final patient = patients[index];
                return ListTile(
                  leading: CircleAvatar(
                    radius: 16.r,
                    backgroundColor: AppColors.primary.withValues(alpha: 0.2),
                    child: Text(
                      patient.name.isNotEmpty ? patient.name[0].toUpperCase() : 'م',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    patient.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (patient.phone?.isNotEmpty ?? false)
                        Text(
                          patient.phone!,
                          style: TextStyle(fontSize: 12.sp),
                        ),
                      Text(
                        'رقم المريض: ${patient.patientId ?? 'غير محدد'}',
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    widget.onPatientSelected(patient);
                    setState(() {
                      _isExpanded = false;
                      _searchController.clear();
                    });
                  },
                );
              },
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}
