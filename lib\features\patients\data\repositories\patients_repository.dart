import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/patient_model.dart';

class PatientsRepository {
  Future<List<PatientModel>> getAllPatients() async {
    try {
      final response = await SupabaseConfig.patients.select().order(
        'created_at',
        ascending: false,
      );

      return response
          .map<PatientModel>((json) => PatientModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب بيانات المرضى: ${e.toString()}');
    }
  }

  Future<List<PatientModel>> getPremiumPatients() async {
    try {
      final response = await SupabaseConfig.patients
          .select()
          .eq('is_premium', true)
          .order('created_at', ascending: false);

      return response
          .map<PatientModel>((json) => PatientModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب بيانات المرضى المميزين: ${e.toString()}');
    }
  }

  Future<PatientModel> getPatientById(String id) async {
    try {
      final response =
          await SupabaseConfig.patients.select().eq('id', id).single();

      return PatientModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب بيانات المريض: ${e.toString()}');
    }
  }

  Future<PatientModel> createPatient(PatientModel patient) async {
    try {
      final response =
          await SupabaseConfig.patients
              .insert(patient.toJson())
              .select()
              .single();

      return PatientModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إنشاء المريض: ${e.toString()}');
    }
  }

  Future<PatientModel> updatePatient(PatientModel patient) async {
    try {
      final response =
          await SupabaseConfig.patients
              .update(patient.toJson())
              .eq('id', patient.id)
              .select()
              .single();

      return PatientModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المريض: ${e.toString()}');
    }
  }

  Future<void> deletePatient(String id) async {
    try {
      await SupabaseConfig.patients.delete().eq('id', id);
    } catch (e) {
      throw Exception('فشل في حذف المريض: ${e.toString()}');
    }
  }

  Future<PatientModel> upgradeToPremium(String patientId) async {
    try {
      final response =
          await SupabaseConfig.patients
              .update({'is_premium': true})
              .eq('id', patientId)
              .select()
              .single();

      return PatientModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في ترقية المريض إلى مميز: ${e.toString()}');
    }
  }

  Future<PatientModel> downgradeFromPremium(String patientId) async {
    try {
      final response =
          await SupabaseConfig.patients
              .update({'is_premium': false})
              .eq('id', patientId)
              .select()
              .single();

      return PatientModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إلغاء العضوية المميزة للمريض: ${e.toString()}');
    }
  }

  Future<PatientModel> updatePatientWeight(
    String patientId,
    double weight,
  ) async {
    try {
      debugPrint(
        '🔄 PatientsRepository: Updating weight for patient: $patientId to $weight kg',
      );

      final response =
          await SupabaseConfig.patients
              .update({
                'weight': weight,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', patientId)
              .select()
              .single();

      final updatedPatient = PatientModel.fromJson(response);
      debugPrint('✅ PatientsRepository: Successfully updated patient weight');

      return updatedPatient;
    } catch (e) {
      debugPrint('❌ PatientsRepository: Error updating patient weight: $e');
      throw Exception('فشل في تحديث وزن المريض: ${e.toString()}');
    }
  }

  Future<List<PatientModel>> searchPatients(String query) async {
    try {
      debugPrint(
        '🔍 PatientsRepository: Searching patients with query: $query',
      );

      final response = await SupabaseConfig.patients
          .select()
          .or(
            'name.ilike.%$query%,phone.ilike.%$query%,patient_id.ilike.%$query%',
          )
          .order('created_at', ascending: false);

      final patients =
          response
              .map<PatientModel>((json) => PatientModel.fromJson(json))
              .toList();
      debugPrint(
        '✅ PatientsRepository: Found ${patients.length} patients matching query: $query',
      );

      return patients;
    } catch (e) {
      debugPrint('❌ PatientsRepository: Error searching patients: $e');
      throw Exception('فشل في البحث عن المرضى: ${e.toString()}');
    }
  }
}
