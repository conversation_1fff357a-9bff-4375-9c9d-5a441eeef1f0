import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/appointment_booking_repository.dart';
import '../../data/repositories/appointments_repository.dart';
import 'appointment_booking_event.dart';
import 'appointment_booking_state.dart';

class AppointmentBookingBloc
    extends Bloc<AppointmentBookingEvent, AppointmentBookingState> {
  final AppointmentBookingRepository _appointmentBookingRepository;
  final AppointmentsRepository _appointmentsRepository;

  AppointmentBookingBloc({
    required AppointmentBookingRepository appointmentBookingRepository,
    required AppointmentsRepository appointmentsRepository,
  }) : _appointmentBookingRepository = appointmentBookingRepository,
       _appointmentsRepository = appointmentsRepository,
       super(const AppointmentBookingInitial()) {
    on<LoadAvailableSlotsForDate>(_onLoadAvailableSlotsForDate);
    on<LoadPatientUpcomingAppointments>(_onLoadPatientUpcomingAppointments);
    on<LoadPatientPastAppointments>(_onLoadPatientPastAppointments);
    on<BookAppointmentSlot>(_onBookAppointmentSlot);
    on<RefreshBookingData>(_onRefreshBookingData);
  }

  Future<void> _onLoadAvailableSlotsForDate(
    LoadAvailableSlotsForDate event,
    Emitter<AppointmentBookingState> emit,
  ) async {
    try {
      debugPrint(
        '🔄 AppointmentBookingBloc: Loading available slots for date: ${event.date}',
      );
      emit(const AppointmentBookingLoading());

      final bookingResult = await _appointmentBookingRepository
          .getAvailableTimeSlotsForDate(event.date);

      debugPrint(
        '✅ AppointmentBookingBloc: Successfully loaded booking result',
      );
      debugPrint(
        '📊 AppointmentBookingBloc: Day: ${bookingResult.dayName}, Holiday: ${bookingResult.isHoliday}',
      );
      debugPrint(
        '📊 AppointmentBookingBloc: Available slots: ${bookingResult.availableSlots.length}',
      );
      debugPrint(
        '📊 AppointmentBookingBloc: Booked slots: ${bookingResult.bookedSlots.length}',
      );

      // Keep existing upcoming appointments if available
      List<PatientAppointmentWithDetails> upcomingAppointments = [];
      if (state is AppointmentBookingLoaded) {
        upcomingAppointments =
            (state as AppointmentBookingLoaded).upcomingAppointments;
      }

      emit(
        AppointmentBookingLoaded(
          bookingResult: bookingResult,
          upcomingAppointments: upcomingAppointments,
        ),
      );
    } catch (e) {
      debugPrint('❌ AppointmentBookingBloc: Error loading available slots: $e');
      emit(AppointmentBookingError(message: e.toString()));
    }
  }

  Future<void> _onLoadPatientUpcomingAppointments(
    LoadPatientUpcomingAppointments event,
    Emitter<AppointmentBookingState> emit,
  ) async {
    try {
      debugPrint(
        '🔄 AppointmentBookingBloc: Loading patient upcoming appointments: ${event.patientId}',
      );

      final upcomingAppointments = await _appointmentBookingRepository
          .getPatientUpcomingAppointments(event.patientId);

      debugPrint(
        '✅ AppointmentBookingBloc: Successfully loaded ${upcomingAppointments.length} upcoming appointments',
      );

      // Keep existing booking result and past appointments if available
      if (state is AppointmentBookingLoaded) {
        final currentState = state as AppointmentBookingLoaded;
        emit(
          AppointmentBookingLoaded(
            bookingResult: currentState.bookingResult,
            upcomingAppointments: upcomingAppointments,
            pastAppointments: currentState.pastAppointments,
          ),
        );
      } else {
        // If no booking result yet, load past appointments too
        final pastAppointments = await _appointmentBookingRepository
            .getPatientPastAppointments(event.patientId);
        emit(
          AppointmentBookingLoaded(
            bookingResult: AppointmentBookingResult(
              date: DateTime.now(),
              dayOfWeek: DateTime.now().weekday % 7,
              dayName: _getDayName(DateTime.now().weekday % 7),
              isHoliday: false,
              availableSlots: [],
              bookedSlots: [],
            ),
            upcomingAppointments: upcomingAppointments,
            pastAppointments: pastAppointments,
          ),
        );
      }
    } catch (e) {
      debugPrint(
        '❌ AppointmentBookingBloc: Error loading patient appointments: $e',
      );
      emit(AppointmentBookingError(message: e.toString()));
    }
  }

  Future<void> _onLoadPatientPastAppointments(
    LoadPatientPastAppointments event,
    Emitter<AppointmentBookingState> emit,
  ) async {
    try {
      debugPrint(
        '🔄 AppointmentBookingBloc: Loading patient past appointments: ${event.patientId}',
      );

      final pastAppointments = await _appointmentBookingRepository
          .getPatientPastAppointments(event.patientId);

      debugPrint(
        '✅ AppointmentBookingBloc: Successfully loaded ${pastAppointments.length} past appointments',
      );

      // Keep existing data if available
      if (state is AppointmentBookingLoaded) {
        final currentState = state as AppointmentBookingLoaded;
        emit(
          AppointmentBookingLoaded(
            bookingResult: currentState.bookingResult,
            upcomingAppointments: currentState.upcomingAppointments,
            pastAppointments: pastAppointments,
          ),
        );
      } else {
        // If no other data yet, emit with empty result
        emit(
          AppointmentBookingLoaded(
            bookingResult: AppointmentBookingResult(
              date: DateTime.now(),
              dayOfWeek: DateTime.now().weekday % 7,
              dayName: _getDayName(DateTime.now().weekday % 7),
              isHoliday: false,
              availableSlots: [],
              bookedSlots: [],
            ),
            upcomingAppointments: [],
            pastAppointments: pastAppointments,
          ),
        );
      }
    } catch (e) {
      debugPrint(
        '❌ AppointmentBookingBloc: Error loading past appointments: $e',
      );
      emit(AppointmentBookingError(message: e.toString()));
    }
  }

  Future<void> _onBookAppointmentSlot(
    BookAppointmentSlot event,
    Emitter<AppointmentBookingState> emit,
  ) async {
    try {
      debugPrint('🔄 AppointmentBookingBloc: Booking appointment slot');
      emit(const AppointmentBookingLoading()); // عرض loading

      final bookedAppointment = await _appointmentsRepository.createAppointment(
        event.appointment,
      );

      debugPrint(
        '✅ AppointmentBookingBloc: Successfully booked appointment: ${bookedAppointment.id}',
      );
      emit(AppointmentBooked(appointmentId: bookedAppointment.id));

      // Refresh data after booking
      if (event.appointment.patientId != null) {
        add(
          RefreshBookingData(
            selectedDate: event.appointment.appointmentDate,
            patientId: event.appointment.patientId!,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ AppointmentBookingBloc: Error booking appointment: $e');
      emit(AppointmentBookingError(message: e.toString()));
    }
  }

  Future<void> _onRefreshBookingData(
    RefreshBookingData event,
    Emitter<AppointmentBookingState> emit,
  ) async {
    try {
      debugPrint('🔄 AppointmentBookingBloc: Refreshing booking data');

      // Load available slots, upcoming appointments, and past appointments
      final bookingResult = await _appointmentBookingRepository
          .getAvailableTimeSlotsForDate(event.selectedDate);
      final upcomingAppointments = await _appointmentBookingRepository
          .getPatientUpcomingAppointments(event.patientId);
      final pastAppointments = await _appointmentBookingRepository
          .getPatientPastAppointments(event.patientId);

      debugPrint(
        '✅ AppointmentBookingBloc: Successfully refreshed booking data',
      );

      emit(
        AppointmentBookingLoaded(
          bookingResult: bookingResult,
          upcomingAppointments: upcomingAppointments,
          pastAppointments: pastAppointments,
        ),
      );
    } catch (e) {
      debugPrint('❌ AppointmentBookingBloc: Error refreshing booking data: $e');
      emit(AppointmentBookingError(message: e.toString()));
    }
  }

  String _getDayName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 0:
        return 'الأحد';
      case 1:
        return 'الإثنين';
      case 2:
        return 'الثلاثاء';
      case 3:
        return 'الأربعاء';
      case 4:
        return 'الخميس';
      case 5:
        return 'الجمعة';
      case 6:
        return 'السبت';
      default:
        return 'غير محدد';
    }
  }
}
