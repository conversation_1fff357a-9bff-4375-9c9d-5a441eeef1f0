import 'package:equatable/equatable.dart';

class WeeklyResultModel extends Equatable {
  final String id;
  final String patientId;
  final double? weight;
  final double? bodyFat;
  final double? visceralFat;
  final double? waterPercentage;
  final double? muscleMass;
  final DateTime recordedDate;
  final String? notes;
  final DateTime createdAt;

  const WeeklyResultModel({
    required this.id,
    required this.patientId,
    this.weight,
    this.bodyFat,
    this.visceralFat,
    this.waterPercentage,
    this.muscleMass,
    required this.recordedDate,
    this.notes,
    required this.createdAt,
  });

  factory WeeklyResultModel.fromJson(Map<String, dynamic> json) {
    return WeeklyResultModel(
      id: json['id'] as String,
      patientId: json['patient_id'] as String,
      weight: json['weight'] != null ? (json['weight'] as num).toDouble() : null,
      bodyFat: json['body_fat'] != null ? (json['body_fat'] as num).toDouble() : null,
      visceralFat: json['visceral_fat'] != null ? (json['visceral_fat'] as num).toDouble() : null,
      waterPercentage: json['water_percentage'] != null ? (json['water_percentage'] as num).toDouble() : null,
      muscleMass: json['muscle_mass'] != null ? (json['muscle_mass'] as num).toDouble() : null,
      recordedDate: DateTime.parse(json['recorded_date'] as String),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson({bool includeId = true}) {
    final json = {
      'patient_id': patientId,
      'weight': weight,
      'body_fat': bodyFat,
      'visceral_fat': visceralFat,
      'water_percentage': waterPercentage,
      'muscle_mass': muscleMass,
      'recorded_date': recordedDate.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };

    // Only include ID if it's not empty and includeId is true
    if (includeId && id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  WeeklyResultModel copyWith({
    String? id,
    String? patientId,
    double? weight,
    double? bodyFat,
    double? visceralFat,
    double? waterPercentage,
    double? muscleMass,
    DateTime? recordedDate,
    String? notes,
    DateTime? createdAt,
  }) {
    return WeeklyResultModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      weight: weight ?? this.weight,
      bodyFat: bodyFat ?? this.bodyFat,
      visceralFat: visceralFat ?? this.visceralFat,
      waterPercentage: waterPercentage ?? this.waterPercentage,
      muscleMass: muscleMass ?? this.muscleMass,
      recordedDate: recordedDate ?? this.recordedDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        patientId,
        weight,
        bodyFat,
        visceralFat,
        waterPercentage,
        muscleMass,
        recordedDate,
        notes,
        createdAt,
      ];
}
