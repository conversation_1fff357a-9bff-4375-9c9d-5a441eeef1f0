import 'package:equatable/equatable.dart';
import 'treatment_type.dart';

class PatientModel extends Equatable {
  final String id; // This is now the auth.uid from Supabase Authentication
  final String? patientId; // Unique 10-digit patient ID
  final String name;
  final String? email;
  final String? phone;
  final int? age;
  final DateTime? birthDate;
  final String? gender;
  final List<TreatmentType> treatmentTypes; // New: Multiple treatment types
  final bool isPremium;
  final String? medicalConditions;
  final String? allergies;
  final String? medications;
  final String? supplements;
  final String? physicalActivity;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PatientModel({
    required this.id, // This is now the auth.uid
    this.patientId, // Unique 10-digit patient ID
    required this.name,
    this.email,
    this.phone,
    this.age,
    this.birthDate,
    this.gender,
    this.treatmentTypes = const [], // New: Multiple treatment types
    this.isPremium = false,
    this.medicalConditions,
    this.allergies,
    this.medications,
    this.supplements,
    this.physicalActivity,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PatientModel.fromJson(Map<String, dynamic> json) {
    // Parse treatment types from JSON array
    List<TreatmentType> treatmentTypes = [];
    if (json['treatment_types'] != null) {
      final List<dynamic> typesList = json['treatment_types'] as List<dynamic>;
      treatmentTypes = TreatmentType.fromStringList(
        typesList.map((e) => e.toString()).toList(),
      );
    }

    return PatientModel(
      id: json['id'] as String? ?? '', // This is now the auth.uid
      patientId: json['patient_id'] as String?, // Unique 10-digit patient ID
      name: json['name'] as String? ?? '',
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      age: json['age'] as int?,
      birthDate: json['birth_date'] != null ? DateTime.parse(json['birth_date'] as String) : null,
      gender: json['gender'] as String?,
      treatmentTypes: treatmentTypes,
      isPremium: json['is_premium'] as bool? ?? false,
      medicalConditions: json['medical_conditions'] as String?,
      allergies: json['allergies'] as String?,
      medications: json['medications'] as String?,
      supplements: json['supplements'] as String?,
      physicalActivity: json['physical_activity'] as String?,
      notes: json['notes'] as String?,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : DateTime.now(),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at'] as String) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id, // This is now the auth.uid
      'patient_id': patientId, // Unique 10-digit patient ID
      'name': name,
      'email': email,
      'phone': phone,
      'age': age,
      'birth_date': birthDate?.toIso8601String().split('T')[0],
      'gender': gender,
      'treatment_types': TreatmentType.toStringList(treatmentTypes),
      'is_premium': isPremium,
      'medical_conditions': medicalConditions,
      'allergies': allergies,
      'medications': medications,
      'supplements': supplements,
      'physical_activity': physicalActivity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PatientModel copyWith({
    String? id,
    String? patientId,
    String? name,
    String? email,
    String? phone,
    int? age,
    DateTime? birthDate,
    String? gender,
    List<TreatmentType>? treatmentTypes,
    bool? isPremium,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PatientModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      age: age ?? this.age,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      treatmentTypes: treatmentTypes ?? this.treatmentTypes,
      isPremium: isPremium ?? this.isPremium,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      allergies: allergies ?? this.allergies,
      medications: medications ?? this.medications,
      supplements: supplements ?? this.supplements,
      physicalActivity: physicalActivity ?? this.physicalActivity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id, // This is now the auth.uid
        patientId, // Unique 10-digit patient ID
        name,
        email,
        phone,
        age,
        birthDate,
        gender,
        treatmentTypes,
        isPremium,
        medicalConditions,
        allergies,
        medications,
        supplements,
        physicalActivity,
        notes,
        createdAt,
        updatedAt,
      ];
}
