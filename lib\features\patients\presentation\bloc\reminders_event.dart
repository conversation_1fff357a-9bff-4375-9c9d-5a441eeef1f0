import 'package:equatable/equatable.dart';
import '../../../../core/models/reminder_model.dart';

abstract class RemindersEvent extends Equatable {
  const RemindersEvent();

  @override
  List<Object?> get props => [];
}

class LoadRemindersByPatientId extends RemindersEvent {
  final String patientId;

  const LoadRemindersByPatientId({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadRemindersByType extends RemindersEvent {
  final String patientId;
  final String reminderType;

  const LoadRemindersByType({
    required this.patientId,
    required this.reminderType,
  });

  @override
  List<Object?> get props => [patientId, reminderType];
}

class LoadActiveReminders extends RemindersEvent {
  final String patientId;

  const LoadActiveReminders({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class AddReminder extends RemindersEvent {
  final ReminderModel reminder;

  const AddReminder({required this.reminder});

  @override
  List<Object?> get props => [reminder];
}

class UpdateReminder extends RemindersEvent {
  final ReminderModel reminder;

  const UpdateReminder({required this.reminder});

  @override
  List<Object?> get props => [reminder];
}

class DeleteReminder extends RemindersEvent {
  final String reminderId;
  final String patientId;

  const DeleteReminder({
    required this.reminderId,
    required this.patientId,
  });

  @override
  List<Object?> get props => [reminderId, patientId];
}

class ToggleReminderStatus extends RemindersEvent {
  final String reminderId;
  final bool isActive;
  final String patientId;

  const ToggleReminderStatus({
    required this.reminderId,
    required this.isActive,
    required this.patientId,
  });

  @override
  List<Object?> get props => [reminderId, isActive, patientId];
}

class SearchReminders extends RemindersEvent {
  final String patientId;
  final String query;

  const SearchReminders({
    required this.patientId,
    required this.query,
  });

  @override
  List<Object?> get props => [patientId, query];
}

class RefreshReminders extends RemindersEvent {
  final String patientId;

  const RefreshReminders({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}
