import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/financial_summary_model.dart';

class FinancialSummaryRepository {
  
  /// جلب الملخص المالي للفترة المحددة
  Future<FinancialSummaryModel> getFinancialSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      debugPrint('🔍 FinancialSummaryRepository: Loading financial summary...');
      
      // تحديد الفترة الزمنية (افتراضياً الشهر الحالي)
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      
      final startDateStr = start.toIso8601String().split('T')[0];
      final endDateStr = end.toIso8601String().split('T')[0];
      
      debugPrint('📅 Date range: $startDateStr to $endDateStr');

      // جلب بيانات المبيعات الأساسية
      final salesData = await _getSalesData(startDateStr, endDateStr);
      debugPrint('💰 Sales data loaded: ${salesData.length} records');

      // جلب بيانات المبيعات مع المنتجات لحساب أصل المبيعات
      final salesWithProductsData = await _getSalesWithProductsData(startDateStr, endDateStr);
      debugPrint('🛍️ Sales with products data loaded: ${salesWithProductsData.length} records');

      // جلب بيانات المصروفات
      final expensesData = await _getExpensesData(startDateStr, endDateStr);
      debugPrint('💸 Expenses data loaded: ${expensesData.length} records');

      // جلب بيانات الحجوزات
      final appointmentsData = await _getAppointmentsData(startDateStr, endDateStr);
      debugPrint('📅 Appointments data loaded: ${appointmentsData.length} records');

      // إنشاء الملخص المالي
      final summary = FinancialSummaryModel.fromData(
        salesData: salesData,
        expensesData: expensesData,
        appointmentsData: appointmentsData,
        salesWithProductsData: salesWithProductsData,
      );

      debugPrint('✅ Financial summary created successfully');
      return summary;
      
    } catch (e, stackTrace) {
      debugPrint('❌ Error loading financial summary: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      rethrow;
    }
  }

  /// جلب بيانات المبيعات للفترة المحددة (جميع الفواتير)
  Future<List<Map<String, dynamic>>> _getSalesData(String startDate, String endDate) async {
    try {
      final response = await SupabaseConfig.salesInvoices
          .select('''
            id,
            final_amount,
            paid_amount,
            remaining_amount,
            commission_amount,
            status,
            created_at
          ''')
          .gte('created_at', startDate)
          .lte('created_at', '$endDate 23:59:59');
          // إزالة فلتر status لجلب جميع الفواتير

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error loading sales data: $e');
      return [];
    }
  }

  /// جلب بيانات المبيعات مع المنتجات لحساب أصل المبيعات
  Future<List<Map<String, dynamic>>> _getSalesWithProductsData(String startDate, String endDate) async {
    try {
      final response = await SupabaseConfig.salesInvoices
          .select('''
            id,
            invoice_items (
              quantity,
              unit_price,
              total_price,
              products (
                original_price
              )
            )
          ''')
          .gte('created_at', startDate)
          .lte('created_at', '$endDate 23:59:59');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error loading sales with products data: $e');
      return [];
    }
  }

  /// جلب بيانات المصروفات للفترة المحددة
  Future<List<Map<String, dynamic>>> _getExpensesData(String startDate, String endDate) async {
    try {
      final response = await SupabaseConfig.expenses
          .select('''
            id,
            amount,
            category,
            expense_date
          ''')
          .gte('expense_date', startDate)
          .lte('expense_date', endDate);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error loading expenses data: $e');
      return [];
    }
  }

  /// جلب بيانات الحجوزات للفترة المحددة
  Future<List<Map<String, dynamic>>> _getAppointmentsData(String startDate, String endDate) async {
    try {
      final response = await SupabaseConfig.appointments
          .select('''
            id,
            consultation_fee,
            paid_amount,
            remaining_amount,
            status,
            appointment_date
          ''')
          .gte('appointment_date', startDate)
          .lte('appointment_date', endDate);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error loading appointments data: $e');
      return [];
    }
  }

  /// جلب إحصائيات المبيعات حسب الفئة
  Future<Map<String, double>> getSalesByCategory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      
      final startDateStr = start.toIso8601String().split('T')[0];
      final endDateStr = end.toIso8601String().split('T')[0];

      // جلب بيانات المبيعات مع تفاصيل المنتجات
      final response = await SupabaseConfig.salesInvoices
          .select('''
            invoice_items (
              quantity,
              unit_price,
              total_price,
              products (
                category_id
              )
            )
          ''')
          .gte('created_at', startDateStr)
          .lte('created_at', '$endDateStr 23:59:59')
          .eq('status', 'active');

      final Map<String, double> salesByCategory = {};

      for (final invoice in response) {
        final items = invoice['invoice_items'] as List? ?? [];
        for (final item in items) {
          final product = item['products'] as Map<String, dynamic>?;
          final categoryId = product?['category_id'] as String? ?? 'غير محدد';
          final category = categoryId; // يمكن تحسين هذا لاحقاً لجلب اسم الفئة
          final totalPrice = (item['total_price'] as num?)?.toDouble() ?? 0.0;
          
          salesByCategory[category] = (salesByCategory[category] ?? 0.0) + totalPrice;
        }
      }

      return salesByCategory;
    } catch (e) {
      debugPrint('❌ Error loading sales by category: $e');
      return {};
    }
  }

  /// جلب إحصائيات المصروفات حسب الفئة
  Future<Map<String, double>> getExpensesByCategory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      
      final startDateStr = start.toIso8601String().split('T')[0];
      final endDateStr = end.toIso8601String().split('T')[0];

      final response = await SupabaseConfig.expenses
          .select('category, amount')
          .gte('expense_date', startDateStr)
          .lte('expense_date', endDateStr);

      final Map<String, double> expensesByCategory = {};

      for (final expense in response) {
        final category = expense['category'] as String? ?? 'other';
        final amount = (expense['amount'] as num?)?.toDouble() ?? 0.0;
        
        expensesByCategory[category] = (expensesByCategory[category] ?? 0.0) + amount;
      }

      return expensesByCategory;
    } catch (e) {
      debugPrint('❌ Error loading expenses by category: $e');
      return {};
    }
  }

  /// جلب إحصائيات الحجوزات حسب الحالة
  Future<Map<String, int>> getAppointmentsByStatus({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      
      final startDateStr = start.toIso8601String().split('T')[0];
      final endDateStr = end.toIso8601String().split('T')[0];

      final response = await SupabaseConfig.appointments
          .select('status')
          .gte('appointment_date', startDateStr)
          .lte('appointment_date', endDateStr);

      final Map<String, int> appointmentsByStatus = {};

      for (final appointment in response) {
        final status = appointment['status'] as String? ?? 'available';
        appointmentsByStatus[status] = (appointmentsByStatus[status] ?? 0) + 1;
      }

      return appointmentsByStatus;
    } catch (e) {
      debugPrint('❌ Error loading appointments by status: $e');
      return {};
    }
  }

  /// جلب الإحصائيات اليومية للفترة المحددة
  Future<List<Map<String, dynamic>>> getDailyStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      
      final startDateStr = start.toIso8601String().split('T')[0];
      final endDateStr = end.toIso8601String().split('T')[0];

      // جلب المبيعات اليومية
      final salesResponse = await SupabaseConfig.salesInvoices
          .select('final_amount, created_at')
          .gte('created_at', startDateStr)
          .lte('created_at', '$endDateStr 23:59:59')
          .eq('status', 'active');

      // جلب المصروفات اليومية
      final expensesResponse = await SupabaseConfig.expenses
          .select('amount, expense_date')
          .gte('expense_date', startDateStr)
          .lte('expense_date', endDateStr);

      // جلب الحجوزات اليومية
      final appointmentsResponse = await SupabaseConfig.appointments
          .select('consultation_fee, appointment_date, status')
          .gte('appointment_date', startDateStr)
          .lte('appointment_date', endDateStr)
          .inFilter('status', ['confirmed', 'completed']);

      // تجميع البيانات حسب التاريخ
      final Map<String, Map<String, double>> dailyData = {};

      // معالجة المبيعات
      for (final sale in salesResponse) {
        final date = (sale['created_at'] as String).split('T')[0];
        final amount = (sale['final_amount'] as num?)?.toDouble() ?? 0.0;
        
        dailyData[date] ??= {'sales': 0.0, 'expenses': 0.0, 'appointments': 0.0};
        dailyData[date]!['sales'] = (dailyData[date]!['sales'] ?? 0.0) + amount;
      }

      // معالجة المصروفات
      for (final expense in expensesResponse) {
        final date = expense['expense_date'] as String;
        final amount = (expense['amount'] as num?)?.toDouble() ?? 0.0;
        
        dailyData[date] ??= {'sales': 0.0, 'expenses': 0.0, 'appointments': 0.0};
        dailyData[date]!['expenses'] = (dailyData[date]!['expenses'] ?? 0.0) + amount;
      }

      // معالجة الحجوزات
      for (final appointment in appointmentsResponse) {
        final date = appointment['appointment_date'] as String;
        final amount = (appointment['consultation_fee'] as num?)?.toDouble() ?? 0.0;
        
        dailyData[date] ??= {'sales': 0.0, 'expenses': 0.0, 'appointments': 0.0};
        dailyData[date]!['appointments'] = (dailyData[date]!['appointments'] ?? 0.0) + amount;
      }

      // تحويل إلى قائمة مرتبة
      final List<Map<String, dynamic>> result = [];
      final sortedDates = dailyData.keys.toList()..sort();
      
      for (final date in sortedDates) {
        result.add({
          'date': date,
          'sales': dailyData[date]!['sales'],
          'expenses': dailyData[date]!['expenses'],
          'appointments': dailyData[date]!['appointments'],
          'profit': (dailyData[date]!['sales']! + dailyData[date]!['appointments']!) - dailyData[date]!['expenses']!,
        });
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error loading daily statistics: $e');
      return [];
    }
  }
}