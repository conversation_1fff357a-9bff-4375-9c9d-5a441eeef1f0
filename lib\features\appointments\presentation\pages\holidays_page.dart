import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/holiday_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/holidays_bloc.dart';
import '../bloc/holidays_event.dart';
import '../bloc/holidays_state.dart';
import '../widgets/holiday_form_dialog.dart';

class HolidaysPage extends StatefulWidget {
  const HolidaysPage({super.key});

  @override
  State<HolidaysPage> createState() => _HolidaysPageState();
}

class _HolidaysPageState extends State<HolidaysPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _dateSearchController = TextEditingController();
  String _searchQuery = '';
  DateTime? _searchDate;

  @override
  void initState() {
    super.initState();
    // Load data when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<HolidaysBloc>().add(const LoadAllHolidays());
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _dateSearchController.dispose();
    super.dispose();
  }

  void _showAddHolidayDialog() {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<HolidaysBloc>(),
        child: const HolidayFormDialog(),
      ),
    );
  }

  void _showEditHolidayDialog(HolidayModel holiday) {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<HolidaysBloc>(),
        child: HolidayFormDialog(holiday: holiday),
      ),
    );
  }

  void _deleteHoliday(HolidayModel holiday) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف إجازة "${holiday.occasionName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<HolidaysBloc>().add(DeleteHoliday(holidayId: holiday.id));
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _toggleHolidayStatus(HolidayModel holiday) {
    context.read<HolidaysBloc>().add(ToggleHolidayStatus(
      holidayId: holiday.id,
      isActive: !holiday.isActive,
    ));
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });

    if (query.isEmpty && _searchDate == null) {
      context.read<HolidaysBloc>().add(const LoadAllHolidays());
    } else {
      // Debounce search
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_searchQuery == query && mounted) {
          _performSearch();
        }
      });
    }
  }

  Future<void> _selectSearchDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _searchDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 2)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _searchDate = picked;
        _dateSearchController.text = '${picked.day}/${picked.month}/${picked.year}';
      });
      _performSearch();
    }
  }

  void _clearDateSearch() {
    setState(() {
      _searchDate = null;
      _dateSearchController.clear();
    });
    _performSearch();
  }

  void _performSearch() {
    if (_searchQuery.isEmpty && _searchDate == null) {
      context.read<HolidaysBloc>().add(const LoadAllHolidays());
    } else if (_searchDate != null) {
      // Search by date
      context.read<HolidaysBloc>().add(CheckHolidayByDate(date: _searchDate!));
    } else {
      // Search by text
      context.read<HolidaysBloc>().add(SearchHolidays(query: _searchQuery));
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocConsumer<HolidaysBloc, HolidaysState>(
        listener: (context, state) {
          if (state is HolidaysLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is HolidaysLoaded) {
            LoadingDialog.hide(context);
          } else if (state is HolidaysError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is HolidayCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة الإجازة بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is HolidayUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث الإجازة بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is HolidayDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف الإجازة بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is HolidayStatusToggled) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.holiday.isActive ? 'تم تفعيل الإجازة' : 'تم إلغاء تفعيل الإجازة'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is HolidaysLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is HolidaysLoaded) {
            return _buildHolidaysView(state);
          }

          if (state is HolidayCheckResult) {
            // Handle date search result
            final holidays = state.holiday != null ? [state.holiday!] : <HolidayModel>[];
            return _buildHolidaysView(HolidaysLoaded(
              holidays: holidays,
              searchResults: holidays,
              searchQuery: 'تاريخ: ${state.checkedDate.day}/${state.checkedDate.month}/${state.checkedDate.year}',
            ));
          }

          if (state is HolidaysError) {
            return _buildErrorView(state.message);
          }

          // Initial state
          return _buildInitialView();
        },
      ),
    );
  }

  Widget _buildHolidaysView(HolidaysLoaded state) {
    final holidaysToShow = _searchQuery.isNotEmpty ? state.searchResults : state.holidays;

    return Column(
      children: [
        // Search bar
        _buildSearchBar(),

        // Holidays list
        Expanded(
          child: holidaysToShow.isEmpty
              ? _buildEmptyView()
              : _buildHolidaysList(holidaysToShow),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // First row: Text search and Add button
          Row(
            children: [
              // Text search field
              Expanded(
                flex: 3,
                child: TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'البحث بالاسم...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                            icon: const Icon(Icons.clear),
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(color: AppColors.gray300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(color: AppColors.primary),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // Add holiday button
              ElevatedButton.icon(
                onPressed: _showAddHolidayDialog,
                icon: const Icon(Icons.add, size: 20),
                label: const Text('إضافة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Second row: Date search
          Row(
            children: [
              // Date search field
              Expanded(
                child: InkWell(
                  onTap: _selectSearchDate,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.gray300),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: AppColors.primary,
                          size: 20.w,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            _searchDate == null
                                ? 'البحث بالتاريخ...'
                                : '${_searchDate!.day}/${_searchDate!.month}/${_searchDate!.year}',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: _searchDate == null
                                  ? AppColors.textSecondary
                                  : AppColors.textPrimary,
                            ),
                          ),
                        ),
                        if (_searchDate != null)
                          InkWell(
                            onTap: _clearDateSearch,
                            child: Icon(
                              Icons.clear,
                              color: AppColors.textSecondary,
                              size: 20.w,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // Clear all filters button
              if (_searchQuery.isNotEmpty || _searchDate != null)
                OutlinedButton(
                  onPressed: () {
                    _searchController.clear();
                    _clearDateSearch();
                    setState(() {
                      _searchQuery = '';
                    });
                    context.read<HolidaysBloc>().add(const LoadAllHolidays());
                  },
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: const Text('مسح الكل'),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHolidaysList(List<HolidayModel> holidays) {
    return RefreshIndicator(
      onRefresh: _onRefreshHolidays,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        itemCount: holidays.length,
        itemBuilder: (context, index) {
          final holiday = holidays[index];
          return _buildHolidayCard(holiday);
        },
      ),
    );
  }

  Widget _buildHolidayCard(HolidayModel holiday) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: holiday.isActive
              ? (holiday.isToday ? AppColors.primary : AppColors.gray200)
              : AppColors.gray300,
          width: holiday.isToday ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            // Date and status indicator
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: holiday.isActive
                    ? (holiday.isToday ? AppColors.primary : AppColors.primary.withValues(alpha: 0.1))
                    : AppColors.gray100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    holiday.holidayDate.day.toString(),
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: holiday.isActive
                          ? (holiday.isToday ? AppColors.white : AppColors.primary)
                          : AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    '${holiday.holidayDate.month}/${holiday.holidayDate.year}',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: holiday.isActive
                          ? (holiday.isToday ? AppColors.white : AppColors.primary)
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: 12.w),

            // Holiday details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    holiday.occasionName,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: holiday.isActive ? AppColors.textPrimary : AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    holiday.holidayTypeDisplayName,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  if (holiday.notes?.isNotEmpty == true) ...[
                    SizedBox(height: 4.h),
                    Text(
                      holiday.notes!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),

            // Actions
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditHolidayDialog(holiday);
                    break;
                  case 'toggle':
                    _toggleHolidayStatus(holiday);
                    break;
                  case 'delete':
                    _deleteHoliday(holiday);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 20),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'toggle',
                  child: Row(
                    children: [
                      Icon(
                        holiday.isActive ? Icons.visibility_off : Icons.visibility,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(holiday.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 20, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            (_searchQuery.isNotEmpty || _searchDate != null) ? 'لا توجد نتائج للبحث' : 'لا توجد أيام إجازات',
            style: TextStyle(
              fontSize: 18.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            (_searchQuery.isNotEmpty || _searchDate != null)
                ? 'جرب البحث بكلمات أو تاريخ مختلف'
                : 'اضغط على زر "إضافة" لإضافة يوم إجازة جديد',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => context.read<HolidaysBloc>().add(const LoadAllHolidays()),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل أيام الإجازات...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => context.read<HolidaysBloc>().add(const LoadAllHolidays()),
            child: const Text('تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefreshHolidays() async {
    // تحديث بيانات الإجازات
    context.read<HolidaysBloc>().add(const LoadAllHolidays());
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
