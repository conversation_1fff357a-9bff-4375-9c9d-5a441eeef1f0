import 'package:equatable/equatable.dart';
import 'product_model.dart';

class InvoiceItemModel extends Equatable {
  final String id;
  final String invoiceId;
  final String productId;
  final ProductModel? product;
  final String productName;
  final String? productCode;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final double discountPercentage;
  final double discountAmount;
  final double finalPrice;
  final DateTime createdAt;

  const InvoiceItemModel({
    required this.id,
    required this.invoiceId,
    required this.productId,
    this.product,
    required this.productName,
    this.productCode,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    required this.finalPrice,
    required this.createdAt,
  });

  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) {
    return InvoiceItemModel(
      id: json['id'] as String? ?? '',
      invoiceId: json['invoice_id'] as String? ?? '',
      productId: json['product_id'] as String? ?? '',
      product: json['products'] != null
          ? ProductModel.fromJson(json['products'] as Map<String, dynamic>)
          : null,
      productName: json['product_name'] as String? ?? '',
      productCode: json['product_code'] as String?,
      quantity: json['quantity'] as int? ?? 1,
      unitPrice: (json['unit_price'] as num?)?.toDouble() ?? 0.0,
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      discountPercentage: (json['discount_percentage'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      finalPrice: (json['final_price'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'final_price': finalPrice,
      'created_at': createdAt.toIso8601String(),
    };

    // Only include invoice_id if it's not a temporary ID
    if (invoiceId.isNotEmpty && !invoiceId.startsWith('temp-')) {
      json['invoice_id'] = invoiceId;
    }

    return json;
  }

  InvoiceItemModel copyWith({
    String? id,
    String? invoiceId,
    String? productId,
    ProductModel? product,
    String? productName,
    String? productCode,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountPercentage,
    double? discountAmount,
    double? finalPrice,
    DateTime? createdAt,
  }) {
    return InvoiceItemModel(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      finalPrice: finalPrice ?? this.finalPrice,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Helper methods
  double get totalBeforeDiscount => quantity * unitPrice;
  
  double get calculatedDiscountAmount {
    if (discountPercentage > 0) {
      return totalBeforeDiscount * (discountPercentage / 100);
    }
    return discountAmount;
  }
  
  double get calculatedFinalPrice {
    return totalBeforeDiscount - calculatedDiscountAmount;
  }

  @override
  List<Object?> get props => [
        id,
        invoiceId,
        productId,
        product,
        productName,
        productCode,
        quantity,
        unitPrice,
        totalPrice,
        discountPercentage,
        discountAmount,
        finalPrice,
        createdAt,
      ];
}
