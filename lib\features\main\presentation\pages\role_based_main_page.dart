import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/services/permissions_manager.dart';
import '../../../../core/enums/user_role.dart';
import '../../../../core/widgets/role_based_bottom_navigation.dart';
import '../../../appointments/presentation/pages/appointments_page.dart';
import '../../../patients/presentation/pages/patients_page.dart';
import '../../../products/presentation/pages/products_page.dart';
import '../../../employees/presentation/pages/employees_main_page.dart';
import '../../../sales/presentation/pages/sales_main_page.dart';
import '../../../inventory/presentation/pages/inventory_page.dart';
import '../../../profile/presentation/pages/role_based_settings_page.dart';
import '../../../tasks/presentation/pages/tasks_page.dart';

class RoleBasedMainPage extends StatefulWidget {
  const RoleBasedMainPage({super.key});

  @override
  State<RoleBasedMainPage> createState() => _RoleBasedMainPageState();
}

class _RoleBasedMainPageState extends State<RoleBasedMainPage> {
  int _currentIndex = 0;
  final Set<int> _visitedPages = {0}; // Start with first page visited

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  void _checkPermissions() {
    final permissionsManager = PermissionsManager();
    if (!permissionsManager.isValidUser || !permissionsManager.isAccountActive) {
      // Redirect to login if user is not valid or account is suspended
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/login');
      });
    }
  }

  List<Widget> _getPages() {
    final permissionsManager = PermissionsManager();
    final userRole = permissionsManager.currentUserRole;

    if (userRole == null) {
      return [const Center(child: Text('خطأ في تحديد الصلاحيات'))];
    }

    switch (userRole) {
      case UserRole.manager:
      case UserRole.admin:
      case UserRole.superAdmin:
        // الأدمن: جميع الصفحات
        return [
          AppointmentsPage(isVisible: _currentIndex == 0),
          PatientsPage(
            isVisible: _currentIndex == 1,
            hasBeenVisited: _visitedPages.contains(1),
          ),
          SalesMainPage(
            isVisible: _currentIndex == 2,
            hasBeenVisited: _visitedPages.contains(2),
          ),
          ProductsPage(
            isVisible: _currentIndex == 3,
            hasBeenVisited: _visitedPages.contains(3),
          ),
          EmployeesMainPage(
            isVisible: _currentIndex == 4,
            hasBeenVisited: _visitedPages.contains(4),
          ),
          InventoryPage(
            isVisible: _currentIndex == 5,
            hasBeenVisited: _visitedPages.contains(5),
          ),
          const RoleBasedSettingsPage(),
        ];

      case UserRole.specialist:
        // الأخصائي: الحجوزات + المهام + الإعدادات فقط
        return [
          AppointmentsPage(isVisible: _currentIndex == 0),
          TasksPage(
            isVisible: _currentIndex == 1,
            hasBeenVisited: _visitedPages.contains(1),
          ),
          const RoleBasedSettingsPage(),
        ];

      case UserRole.receptionist:
        // الريسبشنست: الحجوزات + المرضى + المبيعات + الإعدادات فقط
        return [
          AppointmentsPage(isVisible: _currentIndex == 0),
          PatientsPage(
            isVisible: _currentIndex == 1,
            hasBeenVisited: _visitedPages.contains(1),
          ),
          SalesMainPage(
            isVisible: _currentIndex == 2,
            hasBeenVisited: _visitedPages.contains(2),
          ),
          const RoleBasedSettingsPage(),
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final permissionsManager = PermissionsManager();
    final userRole = permissionsManager.currentUserRole;

    if (userRole == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.sp,
                color: AppColors.error,
              ),
              SizedBox(height: 16.h),
              Text(
                'خطأ في تحديد الصلاحيات',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gray900,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'يرجى تسجيل الدخول مرة أخرى',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.gray600,
                ),
              ),
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pushReplacementNamed('/login');
                },
                child: Text('تسجيل الدخول'),
              ),
            ],
          ),
        ),
      );
    }

    final pages = _getPages();

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: pages,
      ),
      bottomNavigationBar: RoleBasedBottomNavigation(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
            _visitedPages.add(index);
          });
        },
      ),
    );
  }
}