{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\projects\\diet_rx\\deit_rx_user\\android\\app\\.cxx\\Debug\\1l1r3y5n\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\projects\\diet_rx\\deit_rx_user\\android\\app\\.cxx\\Debug\\1l1r3y5n\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}