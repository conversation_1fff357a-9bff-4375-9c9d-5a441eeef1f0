import 'package:equatable/equatable.dart';
import 'admin_model.dart';

enum ExpenseCategory { supplies, maintenance, utilities, services, other }

extension ExpenseCategoryExtension on ExpenseCategory {
  String get value {
    switch (this) {
      case ExpenseCategory.supplies:
        return 'supplies';
      case ExpenseCategory.maintenance:
        return 'maintenance';
      case ExpenseCategory.utilities:
        return 'utilities';
      case ExpenseCategory.services:
        return 'services';
      case ExpenseCategory.other:
        return 'other';
    }
  }

  String get arabicName {
    switch (this) {
      case ExpenseCategory.supplies:
        return 'مستلزمات';
      case ExpenseCategory.maintenance:
        return 'صيانة';
      case ExpenseCategory.utilities:
        return 'خدمات عامة';
      case ExpenseCategory.services:
        return 'خدمات';
      case ExpenseCategory.other:
        return 'أخرى';
    }
  }

  static ExpenseCategory fromString(String value) {
    switch (value) {
      case 'supplies':
        return ExpenseCategory.supplies;
      case 'maintenance':
        return ExpenseCategory.maintenance;
      case 'utilities':
        return ExpenseCategory.utilities;
      case 'services':
        return ExpenseCategory.services;
      case 'other':
        return ExpenseCategory.other;
      default:
        return ExpenseCategory.other;
    }
  }
}

class ExpenseModel extends Equatable {
  final String id;
  final String title;
  final String? description;
  final double amount;
  final ExpenseCategory category;
  final DateTime expenseDate;
  final String? receipt; // Path to receipt image
  final String createdBy;
  final AdminModel? admin; // Admin who created this expense
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExpenseModel({
    required this.id,
    required this.title,
    this.description,
    required this.amount,
    required this.category,
    required this.expenseDate,
    this.receipt,
    required this.createdBy,
    this.admin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    try {
      return ExpenseModel(
        id: json['id']?.toString() ?? '',
        title: json['title']?.toString() ?? '',
        description: json['description']?.toString(),
        amount: _parseDouble(json['amount']),
        category: ExpenseCategoryExtension.fromString(json['category']?.toString() ?? 'other'),
        expenseDate: _parseDateTime(json['expense_date']) ?? DateTime.now(),
        receipt: json['receipt']?.toString(),
        createdBy: json['created_by']?.toString() ?? '',
        admin: _parseAdminModel(json['admins']),
        createdAt: _parseDateTime(json['created_at']) ?? DateTime.now(),
        updatedAt: _parseDateTime(json['updated_at']) ?? DateTime.now(),
      );
    } catch (e) {
      print('Error parsing ExpenseModel: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  // Helper method to safely parse double values
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  // Helper method to safely parse DateTime values
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String && value.isNotEmpty) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        print('Error parsing DateTime: $value');
        return null;
      }
    }
    return null;
  }

  // Helper method to safely parse AdminModel
  static AdminModel? _parseAdminModel(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) {
      try {
        return AdminModel.fromJson(value);
      } catch (e) {
        print('Error parsing AdminModel in ExpenseModel: $e');
        print('AdminModel JSON data: $value');
        return null;
      }
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'category': category.value,
      'expense_date': expenseDate.toIso8601String(),
      'receipt': receipt,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ExpenseModel copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    ExpenseCategory? category,
    DateTime? expenseDate,
    String? receipt,
    String? createdBy,
    AdminModel? admin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExpenseModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      expenseDate: expenseDate ?? this.expenseDate,
      receipt: receipt ?? this.receipt,
      createdBy: createdBy ?? this.createdBy,
      admin: admin ?? this.admin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        amount,
        category,
        expenseDate,
        receipt,
        createdBy,
        admin,
        createdAt,
        updatedAt,
      ];
}