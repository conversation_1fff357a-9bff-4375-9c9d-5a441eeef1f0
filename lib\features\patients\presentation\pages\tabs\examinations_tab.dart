import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/examination_model.dart';
import '../../../../../core/utils/image_compression.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:developer' as developer;

class ExaminationsTab extends StatefulWidget {
  final String patientId;

  const ExaminationsTab({
    super.key,
    required this.patientId,
  });

  @override
  State<ExaminationsTab> createState() => _ExaminationsTabState();
}

class _ExaminationsTabState extends State<ExaminationsTab> {
  List<ExaminationModel> _examinations = [];
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _loadExaminations();
  }

  Future<void> _loadExaminations() async {
    setState(() => _isLoading = true);
    try {
      final response = await Supabase.instance.client
          .from('examinations')
          .select()
          .eq('patient_id', widget.patientId)
          .order('examination_date', ascending: false);

      final examinations = (response as List)
          .map((json) => ExaminationModel.fromJson(json))
          .toList();

      setState(() {
        _examinations = examinations;
      });
    } catch (e) {
      developer.log('Error loading examinations: $e', name: 'ExaminationsTab');
      if (mounted) {
        _showErrorMessage('خطأ في تحميل الفحوصات');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(Icons.medical_services, color: AppColors.primary, size: 24),
                SizedBox(width: 12.w),
                Text(
                  'الفحوصات والتقارير',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _showAddExaminationDialog(),
                  icon: const Icon(Icons.add, color: AppColors.white),
                  label: const Text('إضافة فحص', style: TextStyle(color: AppColors.white)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _examinations.isEmpty
                    ? _buildEmptyState()
                    : _buildExaminationsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medical_services_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فحوصات مسجلة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'انقر على "إضافة فحص" لإضافة فحص جديد',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExaminationsList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _examinations.length,
      itemBuilder: (context, index) {
        final examination = _examinations[index];
        return _buildExaminationCard(examination);
      },
    );
  }

  Widget _buildExaminationCard(ExaminationModel examination) {
    final examinationType = ExaminationType.fromString(examination.type);
    
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.medical_services,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      examination.title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        examinationType.arabicName,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.info,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  // Patient ID Badge
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      'ID: ${examination.id.substring(0, 8)}',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  // Edit Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.info.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: IconButton(
                      onPressed: () => _showEditExaminationDialog(examination),
                      icon: Icon(Icons.edit, color: AppColors.info, size: 18),
                      tooltip: 'تعديل الفحص',
                      padding: EdgeInsets.all(8.w),
                      constraints: BoxConstraints(minWidth: 36.w, minHeight: 36.h),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  // Delete Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: IconButton(
                      onPressed: () => _showDeleteConfirmation(examination),
                      icon: Icon(Icons.delete, color: AppColors.error, size: 18),
                      tooltip: 'حذف الفحص',
                      padding: EdgeInsets.all(8.w),
                      constraints: BoxConstraints(minWidth: 36.w, minHeight: 36.h),
                    ),
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Date
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: AppColors.textSecondary),
              SizedBox(width: 8.w),
              Text(
                '${examination.examinationDate.day}/${examination.examinationDate.month}/${examination.examinationDate.year}',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),

          // Description
          if (examination.description != null && examination.description!.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              examination.description!,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimary,
              ),
            ),
          ],

          // Image
          if (examination.imageUrl != null && examination.imageUrl!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: Image.network(
                examination.imageUrl!,
                height: 200.h,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200.h,
                    color: AppColors.gray100,
                    child: const Center(
                      child: Icon(Icons.error, color: AppColors.error),
                    ),
                  );
                },
              ),
            ),
          ],

          // Notes
          if (examination.notes != null && examination.notes!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.gray50,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملاحظات:',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    examination.notes!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showAddExaminationDialog() {
    _showExaminationDialog();
  }

  void _showEditExaminationDialog(ExaminationModel examination) {
    _showExaminationDialog(examination: examination);
  }

  void _showExaminationDialog({ExaminationModel? examination}) {
    final isEditing = examination != null;
    final titleController = TextEditingController(text: examination?.title ?? '');
    final descriptionController = TextEditingController(text: examination?.description ?? '');
    final notesController = TextEditingController(text: examination?.notes ?? '');

    String selectedType = examination?.type ?? ExaminationType.hearing.name;
    DateTime selectedDate = examination?.examinationDate ?? DateTime.now();
    File? selectedImage;
    String? existingImageUrl = examination?.imageUrl;
    bool isUploading = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل الفحص' : 'إضافة فحص جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                TextFormField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'عنوان الفحص *',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),

                // Type
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الفحص *',
                    border: OutlineInputBorder(),
                  ),
                  items: ExaminationType.values.map((type) {
                    return DropdownMenuItem(
                      value: type.name,
                      child: Text(type.arabicName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setDialogState(() {
                        selectedType = value;
                      });
                    }
                  },
                ),
                SizedBox(height: 16.h),

                // Date
                InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setDialogState(() {
                        selectedDate = date;
                      });
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today),
                        SizedBox(width: 12.w),
                        Text(
                          'تاريخ الفحص: ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16.h),

                // Description
                TextFormField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'وصف الفحص',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),

                // Image
                if (existingImageUrl != null || selectedImage != null) ...[
                  Container(
                    height: 150.h,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: selectedImage != null
                        ? Image.file(selectedImage!, fit: BoxFit.cover)
                        : existingImageUrl != null
                            ? Image.network(existingImageUrl!, fit: BoxFit.cover)
                            : const Center(child: Text('لا توجد صورة')),
                  ),
                  SizedBox(height: 8.h),
                ],

                // Image buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          final image = await _picker.pickImage(source: ImageSource.gallery);
                          if (image != null) {
                            setDialogState(() {
                              selectedImage = File(image.path);
                              existingImageUrl = null;
                            });
                          }
                        },
                        icon: const Icon(Icons.photo),
                        label: const Text('اختيار صورة'),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          final image = await _picker.pickImage(source: ImageSource.camera);
                          if (image != null) {
                            setDialogState(() {
                              selectedImage = File(image.path);
                              existingImageUrl = null;
                            });
                          }
                        },
                        icon: const Icon(Icons.camera_alt),
                        label: const Text('التقاط صورة'),
                      ),
                    ),
                  ],
                ),

                if (selectedImage != null || existingImageUrl != null) ...[
                  SizedBox(height: 8.h),
                  TextButton.icon(
                    onPressed: () {
                      setDialogState(() {
                        selectedImage = null;
                        existingImageUrl = null;
                      });
                    },
                    icon: const Icon(Icons.delete, color: AppColors.error),
                    label: const Text('حذف الصورة', style: TextStyle(color: AppColors.error)),
                  ),
                ],

                SizedBox(height: 16.h),

                // Notes
                TextFormField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: isUploading ? null : () async {
                if (titleController.text.trim().isEmpty) {
                  _showErrorMessage('يرجى إدخال عنوان الفحص');
                  return;
                }

                setDialogState(() => isUploading = true);

                try {
                  String? imageUrl = existingImageUrl;

                  // Upload image if selected
                  if (selectedImage != null) {
                    try {
                      // Compress image
                      final compressedImage = await ImageCompression.compressImage(selectedImage!);

                      // Upload to Supabase Storage
                      final fileName = 'examination_${DateTime.now().millisecondsSinceEpoch}.jpg';
                      final uploadPath = 'examinations/$fileName';

                      await Supabase.instance.client.storage
                          .from('medical-files')
                          .uploadBinary(uploadPath, compressedImage);

                      imageUrl = Supabase.instance.client.storage
                          .from('medical-files')
                          .getPublicUrl(uploadPath);
                    } catch (e) {
                      developer.log('Error uploading image: $e', name: 'ExaminationsTab');
                      // Continue without image if upload fails
                      imageUrl = null;
                    }
                  }

                  if (isEditing) {
                    // Update examination
                    await Supabase.instance.client
                        .from('examinations')
                        .update({
                          'title': titleController.text.trim(),
                          'type': selectedType,
                          'description': descriptionController.text.trim().isEmpty
                              ? null : descriptionController.text.trim(),
                          'image_url': imageUrl,
                          'examination_date': selectedDate.toIso8601String().split('T')[0],
                          'notes': notesController.text.trim().isEmpty
                              ? null : notesController.text.trim(),
                          'updated_at': DateTime.now().toIso8601String(),
                        })
                        .eq('id', examination.id);
                  } else {
                    // Create new examination
                    await Supabase.instance.client
                        .from('examinations')
                        .insert({
                          'patient_id': widget.patientId,
                          'title': titleController.text.trim(),
                          'type': selectedType,
                          'description': descriptionController.text.trim().isEmpty
                              ? null : descriptionController.text.trim(),
                          'image_url': imageUrl,
                          'examination_date': selectedDate.toIso8601String().split('T')[0],
                          'notes': notesController.text.trim().isEmpty
                              ? null : notesController.text.trim(),
                        });
                  }

                  if (mounted) {
                    Navigator.of(context).pop();
                    _showSuccessMessage(isEditing ? 'تم تحديث الفحص بنجاح' : 'تم إضافة الفحص بنجاح');
                    _loadExaminations();
                  }
                } catch (e) {
                  developer.log('Error saving examination: $e', name: 'ExaminationsTab');
                  _showErrorMessage('خطأ في حفظ الفحص');
                } finally {
                  setDialogState(() => isUploading = false);
                }
              },
              child: isUploading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(ExaminationModel examination) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فحص "${examination.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // Delete image from storage if exists
                if (examination.imageUrl != null && examination.imageUrl!.isNotEmpty) {
                  await _deleteImageFromStorage(examination.imageUrl!);
                }

                // Delete examination from database
                await Supabase.instance.client
                    .from('examinations')
                    .delete()
                    .eq('id', examination.id);

                if (mounted) {
                  Navigator.of(context).pop();
                  _showSuccessMessage('تم حذف الفحص والصورة بنجاح');
                  _loadExaminations();
                }
              } catch (e) {
                developer.log('Error deleting examination: $e', name: 'ExaminationsTab');
                _showErrorMessage('خطأ في حذف الفحص');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteImageFromStorage(String imageUrl) async {
    try {
      // Extract file path from URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // Find the path after 'medical-files'
      int bucketIndex = pathSegments.indexOf('medical-files');
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        final filePath = pathSegments.sublist(bucketIndex + 1).join('/');

        await Supabase.instance.client.storage
            .from('medical-files')
            .remove([filePath]);

        developer.log('Image deleted from storage: $filePath', name: 'ExaminationsTab');
      }
    } catch (e) {
      developer.log('Error deleting image from storage: $e', name: 'ExaminationsTab');
      // Don't throw error, just log it
    }
  }
}
