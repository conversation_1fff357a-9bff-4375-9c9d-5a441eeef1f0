import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/clinic_info_model.dart';
import '../bloc/clinic_info_bloc.dart';
import '../bloc/clinic_info_event.dart';

class ClinicInfoFormDialog extends StatefulWidget {
  final ClinicInfoModel? clinicInfo;

  const ClinicInfoFormDialog({
    super.key,
    this.clinicInfo,
  });

  @override
  State<ClinicInfoFormDialog> createState() => _ClinicInfoFormDialogState();
}

class _ClinicInfoFormDialogState extends State<ClinicInfoFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _displayNameController;
  late TextEditingController _infoValueController;
  late TextEditingController _iconNameController;

  String _selectedInfoType = 'phone';
  String _infoKey = '';
  bool _isActive = true;
  int _displayOrder = 0;

  final List<Map<String, String>> _infoTypes = [
    {'value': 'phone', 'label': 'أرقام الهواتف'},
    {'value': 'email', 'label': 'البريد الإلكتروني'},
    {'value': 'social_media', 'label': 'وسائل التواصل الاجتماعي'},
    {'value': 'address', 'label': 'العنوان'},
    {'value': 'working_hours', 'label': 'مواعيد العمل'},
    {'value': 'website', 'label': 'الموقع الإلكتروني'},
  ];

  final List<Map<String, String>> _iconOptions = [
    {'value': 'phone', 'label': '📞 هاتف'},
    {'value': 'phone_emergency', 'label': '🚨 هاتف طوارئ'},
    {'value': 'phone_reception', 'label': '📋 هاتف استقبال'},
    {'value': 'email', 'label': '📧 بريد إلكتروني'},
    {'value': 'email_appointments', 'label': '📅 بريد حجوزات'},
    {'value': 'facebook', 'label': '📘 فيسبوك'},
    {'value': 'instagram', 'label': '📸 إنستغرام'},
    {'value': 'twitter', 'label': '🐦 تويتر'},
    {'value': 'whatsapp', 'label': '💬 واتساب'},
    {'value': 'location', 'label': '📍 موقع'},
    {'value': 'landmark', 'label': '🏥 علامة مميزة'},
    {'value': 'schedule', 'label': '🕘 مواعيد عمل'},
    {'value': 'schedule_friday', 'label': '🕐 مواعيد الجمعة'},
    {'value': 'web', 'label': '🌐 موقع إلكتروني'},
    {'value': 'info', 'label': 'ℹ️ معلومات عامة'},
  ];

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController(
      text: widget.clinicInfo?.displayName ?? '',
    );
    _infoValueController = TextEditingController(
      text: widget.clinicInfo?.infoValue ?? '',
    );
    _iconNameController = TextEditingController(
      text: widget.clinicInfo?.iconName ?? '',
    );

    if (widget.clinicInfo != null) {
      _selectedInfoType = widget.clinicInfo!.infoType;
      _infoKey = widget.clinicInfo!.infoKey;
      _isActive = widget.clinicInfo!.isActive;
      _displayOrder = widget.clinicInfo!.displayOrder;
    }
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _infoValueController.dispose();
    _iconNameController.dispose();
    super.dispose();
  }

  void _saveClinicInfo() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Generate info_key if not provided
    if (_infoKey.isEmpty) {
      _infoKey = '${_selectedInfoType}_${DateTime.now().millisecondsSinceEpoch}';
    }

    final clinicInfo = ClinicInfoModel(
      id: widget.clinicInfo?.id ?? '',
      infoType: _selectedInfoType,
      infoKey: _infoKey,
      infoValue: _infoValueController.text.trim(),
      displayName: _displayNameController.text.trim(),
      iconName: _iconNameController.text.trim().isNotEmpty
          ? _iconNameController.text.trim()
          : null,
      isActive: _isActive,
      displayOrder: _displayOrder,
      createdAt: widget.clinicInfo?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (widget.clinicInfo == null) {
      // Add new clinic info
      context.read<ClinicInfoBloc>().add(AddClinicInfo(clinicInfo: clinicInfo));
    } else {
      // Update existing clinic info
      context.read<ClinicInfoBloc>().add(UpdateClinicInfo(clinicInfo: clinicInfo));
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header
              Container(
                padding: EdgeInsets.all(24.w),
                child: Text(
                  widget.clinicInfo == null ? 'إضافة معلومات جديدة' : 'تعديل المعلومات',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Scrollable content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Info type
                      DropdownButtonFormField<String>(
                        value: _selectedInfoType,
                        decoration: InputDecoration(
                          labelText: 'نوع المعلومات *',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        items: _infoTypes.map((type) {
                          return DropdownMenuItem<String>(
                            value: type['value'],
                            child: Text(type['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedInfoType = value;
                            });
                          }
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'يرجى اختيار نوع المعلومات';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Display name
                      TextFormField(
                        controller: _displayNameController,
                        decoration: InputDecoration(
                          labelText: 'اسم العرض *',
                          hintText: 'مثال: الهاتف الرئيسي، البريد الإلكتروني',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'يرجى إدخال اسم العرض';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Info value (3x larger)
                      SizedBox(
                        height: _selectedInfoType == 'address' ? 120.h : 80.h, // 3x larger
                        child: TextFormField(
                          controller: _infoValueController,
                          decoration: InputDecoration(
                            labelText: 'القيمة *',
                            hintText: _getHintText(),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null, // Must be null when expands is true
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'يرجى إدخال القيمة';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Icon selection
                      DropdownButtonFormField<String>(
                        value: _iconNameController.text.isNotEmpty ? _iconNameController.text : null,
                        decoration: InputDecoration(
                          labelText: 'اختر الأيقونة (اختياري)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('بدون أيقونة'),
                          ),
                          ..._iconOptions.map((icon) {
                            return DropdownMenuItem<String>(
                              value: icon['value'],
                              child: Text(icon['label']!),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _iconNameController.text = value ?? '';
                          });
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Display order
                      TextFormField(
                        initialValue: _displayOrder.toString(),
                        decoration: InputDecoration(
                          labelText: 'ترتيب العرض',
                          hintText: 'رقم لترتيب العرض (0 = الأول)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _displayOrder = int.tryParse(value) ?? 0;
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Active status
                      Row(
                        children: [
                          Switch(
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value;
                              });
                            },
                            activeColor: AppColors.primary,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            'نشط',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: Text(
                              _isActive
                                  ? 'المعلومات نشطة ومرئية'
                                  : 'المعلومات غير نشطة',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),

              // Fixed footer with action buttons
              Container(
                padding: EdgeInsets.all(24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveClinicInfo,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          widget.clinicInfo == null ? 'إضافة' : 'تحديث',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getHintText() {
    switch (_selectedInfoType) {
      case 'phone':
        return 'مثال: 01234567890';
      case 'email':
        return 'مثال: <EMAIL>';
      case 'social_media':
        return 'مثال: https://facebook.com/clinic';
      case 'address':
        return 'مثال: شارع الطب، المعادي، القاهرة';
      case 'working_hours':
        return 'مثال: السبت - الخميس: 9:00 ص - 9:00 م';
      case 'website':
        return 'مثال: https://www.clinic.com';
      default:
        return 'أدخل القيمة';
    }
  }
}
