import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/specialization_model.dart';
import '../../data/repositories/specializations_repository.dart';

class AddSpecializationDialog extends StatefulWidget {
  final SpecializationModel? specialization; // null for add, non-null for edit

  const AddSpecializationDialog({super.key, this.specialization});

  @override
  State<AddSpecializationDialog> createState() => _AddSpecializationDialogState();
}

class _AddSpecializationDialogState extends State<AddSpecializationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _descriptionController = TextEditingController();

  Color _selectedColor = const Color(0xFF01928f);
  String _selectedIcon = 'medical_services';
  int _displayOrder = 0;
  bool _isActive = true;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'medical_services', 'icon': Icons.medical_services, 'label': 'خدمات طبية'},
    {'name': 'hearing', 'icon': Icons.hearing, 'label': 'سمع'},
    {'name': 'record_voice_over', 'icon': Icons.record_voice_over, 'label': 'نطق'},
    {'name': 'psychology', 'icon': Icons.psychology, 'label': 'نفسية'},
    {'name': 'chat', 'icon': Icons.chat, 'label': 'تخاطب'},
    {'name': 'school', 'icon': Icons.school, 'label': 'تعليم'},
    {'name': 'accessibility', 'icon': Icons.accessibility, 'label': 'إعاقة'},
    {'name': 'child_care', 'icon': Icons.child_care, 'label': 'رعاية أطفال'},
    {'name': 'sports', 'icon': Icons.sports, 'label': 'رياضة'},
    {'name': 'music_note', 'icon': Icons.music_note, 'label': 'موسيقى'},
  ];

  @override
  void initState() {
    super.initState();
    
    // If editing, populate fields
    if (widget.specialization != null) {
      final spec = widget.specialization!;
      _nameController.text = spec.name;
      _nameEnController.text = spec.nameEn ?? '';
      _descriptionController.text = spec.description ?? '';
      _selectedColor = Color(int.parse(spec.color.replaceFirst('#', '0xFF')));
      _selectedIcon = spec.icon ?? 'medical_services';
      _displayOrder = spec.displayOrder;
      _isActive = spec.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  widget.specialization == null ? Icons.add : Icons.edit,
                  color: AppColors.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.specialization == null ? 'إضافة تخصص جديد' : 'تعديل التخصص',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name (Arabic)
                      _buildTextField(
                        controller: _nameController,
                        label: 'اسم التخصص (عربي)',
                        icon: Icons.medical_services,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال اسم التخصص';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Name (English)
                      _buildTextField(
                        controller: _nameEnController,
                        label: 'اسم التخصص (إنجليزي)',
                        icon: Icons.language,
                      ),
                      const SizedBox(height: 16),

                      // Description
                      _buildTextField(
                        controller: _descriptionController,
                        label: 'الوصف',
                        icon: Icons.description,
                        maxLines: 3,
                      ),
                      const SizedBox(height: 16),

                      // Color Picker
                      _buildColorPicker(),
                      const SizedBox(height: 16),

                      // Icon Picker
                      _buildIconPicker(),
                      const SizedBox(height: 16),

                      // Display Order
                      _buildTextField(
                        controller: TextEditingController(text: _displayOrder.toString()),
                        label: 'ترتيب العرض',
                        icon: Icons.sort,
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _displayOrder = int.tryParse(value) ?? 0;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Active Switch
                      Row(
                        children: [
                          const Icon(Icons.toggle_on, color: AppColors.primary),
                          const SizedBox(width: 12),
                          const Text(
                            'نشط',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const Spacer(),
                          Switch(
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value;
                              });
                            },
                            activeColor: AppColors.primary,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveSpecialization,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Text(widget.specialization == null ? 'إضافة' : 'حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'لون التخصص',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _showColorPicker,
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: _selectedColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.border),
            ),
            child: Row(
              children: [
                const SizedBox(width: 16),
                Icon(Icons.palette, color: Colors.white),
                const SizedBox(width: 12),
                Text(
                  'انقر لاختيار اللون',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIconPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أيقونة التخصص',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(12),
          ),
          child: GridView.builder(
            padding: const EdgeInsets.all(8),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: _availableIcons.length,
            itemBuilder: (context, index) {
              final iconData = _availableIcons[index];
              final isSelected = _selectedIcon == iconData['name'];
              
              return InkWell(
                onTap: () {
                  setState(() {
                    _selectedIcon = iconData['name'];
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected ? _selectedColor.withOpacity(0.2) : Colors.transparent,
                    border: Border.all(
                      color: isSelected ? _selectedColor : AppColors.border,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    iconData['icon'],
                    color: isSelected ? _selectedColor : AppColors.textSecondary,
                    size: 24,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللون'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _selectedColor,
            onColorChanged: (color) {
              setState(() {
                _selectedColor = color;
              });
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveSpecialization() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final specialization = SpecializationModel(
        id: widget.specialization?.id ?? '', // Will be generated by database for new records
        name: _nameController.text.trim(),
        nameEn: _nameEnController.text.trim().isEmpty ? null : _nameEnController.text.trim(),
        description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
        color: '#${_selectedColor.value.toRadixString(16).substring(2)}',
        icon: _selectedIcon,
        isActive: _isActive,
        displayOrder: _displayOrder,
        createdAt: widget.specialization?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.specialization == null) {
        await SpecializationsRepository.addSpecialization(specialization);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إضافة التخصص بنجاح')),
        );
      } else {
        await SpecializationsRepository.updateSpecialization(specialization);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث التخصص بنجاح')),
        );
      }

      Navigator.of(context).pop(true); // Return true to indicate success
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
