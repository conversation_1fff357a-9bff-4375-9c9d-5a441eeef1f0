import 'package:equatable/equatable.dart';
import '../../../../core/models/employee_task_model.dart';

abstract class EmployeeTasksEvent extends Equatable {
  const EmployeeTasksEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllTasks extends EmployeeTasksEvent {
  const LoadAllTasks();
}

class LoadTasksByEmployee extends EmployeeTasksEvent {
  final String employeeId;

  const LoadTasksByEmployee(this.employeeId);

  @override
  List<Object?> get props => [employeeId];
}

class LoadTasksByStatus extends EmployeeTasksEvent {
  final String status;

  const LoadTasksByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

class LoadOverdueTasks extends EmployeeTasksEvent {
  const LoadOverdueTasks();
}

class LoadTasksDueSoon extends EmployeeTasksEvent {
  const LoadTasksDueSoon();
}

class AddTask extends EmployeeTasksEvent {
  final EmployeeTaskModel task;

  const AddTask(this.task);

  @override
  List<Object?> get props => [task];
}

class UpdateTask extends EmployeeTasksEvent {
  final EmployeeTaskModel task;

  const UpdateTask(this.task);

  @override
  List<Object?> get props => [task];
}

class UpdateTaskStatus extends EmployeeTasksEvent {
  final String taskId;
  final String status;

  const UpdateTaskStatus({
    required this.taskId,
    required this.status,
  });

  @override
  List<Object?> get props => [taskId, status];
}

class DeleteTask extends EmployeeTasksEvent {
  final String taskId;

  const DeleteTask(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

class RefreshTasks extends EmployeeTasksEvent {
  const RefreshTasks();
}
