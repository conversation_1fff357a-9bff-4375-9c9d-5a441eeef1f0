import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:googleapis_auth/auth_io.dart';
import '../utils/logger.dart';
import '../network/supabase_client.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final Logger _logger = Logger('NotificationService');

  // Firebase project configuration
  static const String _projectId = 'iihc-8841e';
  static const String _fcmUrl = 'https://fcm.googleapis.com/v1/projects/$_projectId/messages:send';

  // Service account credentials
  static const Map<String, dynamic> _serviceAccountKey = ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

  // Get access token for Firebase Cloud Messaging
  Future<String> _getAccessToken() async {
    try {
      final accountCredentials = ServiceAccountCredentials.fromJson(_serviceAccountKey);
      final scopes = ['https://www.googleapis.com/auth/firebase.messaging'];
      
      final client = await clientViaServiceAccount(accountCredentials, scopes);
      final accessToken = client.credentials.accessToken.data;
      client.close();
      
      return accessToken;
    } catch (e) {
      _logger.error('❌ Failed to get access token: $e');
      rethrow;
    }
  }

  // Send notification to specific user
  Future<bool> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, String>? data,
  }) async {
    try {
      // Get user's FCM tokens
      final tokens = await _getUserTokens(userId);
      if (tokens.isEmpty) {
        _logger.warning('⚠️ No FCM tokens found for user: $userId');
        return false;
      }

      bool success = true;
      for (final token in tokens) {
        final result = await _sendNotification(
          token: token,
          title: title,
          body: body,
          type: type,
          data: data,
        );
        if (!result) success = false;
      }

      return success;
    } catch (e) {
      _logger.error('❌ Failed to send notification to user: $e');
      return false;
    }
  }

  // Send notification to multiple users
  Future<bool> sendNotificationToUsers({
    required List<String> userIds,
    required String title,
    required String body,
    required String type,
    Map<String, String>? data,
  }) async {
    try {
      bool allSuccess = true;
      
      for (final userId in userIds) {
        final result = await sendNotificationToUser(
          userId: userId,
          title: title,
          body: body,
          type: type,
          data: data,
        );
        if (!result) allSuccess = false;
      }

      return allSuccess;
    } catch (e) {
      _logger.error('❌ Failed to send notifications to users: $e');
      return false;
    }
  }

  // Send notification to users by role
  Future<bool> sendNotificationToRole({
    required String role,
    required String title,
    required String body,
    required String type,
    Map<String, String>? data,
  }) async {
    try {
      // Get users by role
      final userIds = await _getUsersByRole(role);
      if (userIds.isEmpty) {
        _logger.warning('⚠️ No users found for role: $role');
        return false;
      }

      return await sendNotificationToUsers(
        userIds: userIds,
        title: title,
        body: body,
        type: type,
        data: data,
      );
    } catch (e) {
      _logger.error('❌ Failed to send notification to role: $e');
      return false;
    }
  }

  // Get user's FCM tokens
  Future<List<String>> _getUserTokens(String userId) async {
    try {
      final response = await SupabaseConfig.client
          .from('user_fcm_tokens')
          .select('fcm_token')
          .eq('user_id', userId)
          .eq('is_active', true);

      return (response as List)
          .map((token) => token['fcm_token'] as String)
          .toList();
    } catch (e) {
      _logger.error('❌ Failed to get user tokens: $e');
      return [];
    }
  }

  // Get users by role
  Future<List<String>> _getUsersByRole(String role) async {
    try {
      final response = await SupabaseConfig.client
          .from('admins')
          .select('id')
          .eq('role', role)
          .eq('is_active', true);

      return (response as List)
          .map((user) => user['id'] as String)
          .toList();
    } catch (e) {
      _logger.error('❌ Failed to get users by role: $e');
      return [];
    }
  }

  // Send notification using FCM API v1
  Future<bool> _sendNotification({
    required String token,
    required String title,
    required String body,
    required String type,
    Map<String, String>? data,
  }) async {
    try {
      final accessToken = await _getAccessToken();
      
      final notificationData = {
        'type': type,
        'timestamp': DateTime.now().toIso8601String(),
        ...?data,
      };

      final message = {
        'message': {
          'token': token,
          'notification': {
            'title': title,
            'body': body,
          },
          'data': notificationData,
          'android': {
            'priority': 'high',
            'notification': {
              'channel_id': _getChannelId(type),
              'default_sound': true,
              'default_vibrate_timings': true,
            },
          },
          'apns': {
            'payload': {
              'aps': {
                'alert': {
                  'title': title,
                  'body': body,
                },
                'sound': 'default',
                'badge': 1,
              },
            },
          },
        },
      };

      final response = await http.post(
        Uri.parse(_fcmUrl),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(message),
      );

      if (response.statusCode == 200) {
        _logger.info('✅ Notification sent successfully');
        return true;
      } else {
        _logger.error('❌ Failed to send notification: ${response.statusCode} - ${response.body}');
        
        // Handle invalid token
        if (response.statusCode == 404 || response.body.contains('UNREGISTERED')) {
          await _markTokenAsInactive(token);
        }
        
        return false;
      }
    } catch (e) {
      _logger.error('❌ Exception while sending notification: $e');
      return false;
    }
  }

  // Get channel ID based on notification type
  String _getChannelId(String type) {
    switch (type) {
      case 'appointment':
        return 'appointments';
      case 'task':
        return 'tasks';
      default:
        return 'general';
    }
  }

  // Mark token as inactive
  Future<void> _markTokenAsInactive(String token) async {
    try {
      await SupabaseConfig.client
          .from('user_fcm_tokens')
          .update({'is_active': false})
          .eq('fcm_token', token);
      
      _logger.info('🗑️ Marked token as inactive: ${token.substring(0, 20)}...');
    } catch (e) {
      _logger.error('❌ Failed to mark token as inactive: $e');
    }
  }

  // Appointment notification methods
  Future<bool> sendAppointmentCancelledNotification({
    required String patientUserId,
    required String appointmentId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
  }) async {
    return await sendNotificationToUser(
      userId: patientUserId,
      title: 'تم إلغاء الحجز',
      body: 'تم إلغاء حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}',
      type: 'appointment',
      data: {
        'appointment_id': appointmentId,
        'action': 'cancelled',
        'patient_name': patientName,
        'doctor_name': doctorName,
      },
    );
  }

  Future<bool> sendAppointmentConfirmedNotification({
    required String patientUserId,
    required String appointmentId,
    required String patientName,
    required String doctorName,
    required DateTime appointmentDate,
  }) async {
    return await sendNotificationToUser(
      userId: patientUserId,
      title: 'تم تأكيد الحجز',
      body: 'تم تأكيد حجزك مع د. $doctorName في ${_formatDate(appointmentDate)}',
      type: 'appointment',
      data: {
        'appointment_id': appointmentId,
        'action': 'confirmed',
        'patient_name': patientName,
        'doctor_name': doctorName,
      },
    );
  }

  Future<bool> sendAppointmentRescheduledNotification({
    required String patientUserId,
    required String appointmentId,
    required String patientName,
    required String doctorName,
    required DateTime oldDate,
    required DateTime newDate,
  }) async {
    return await sendNotificationToUser(
      userId: patientUserId,
      title: 'تم تغيير موعد الحجز',
      body: 'تم تغيير موعد حجزك مع د. $doctorName من ${_formatDate(oldDate)} إلى ${_formatDate(newDate)}',
      type: 'appointment',
      data: {
        'appointment_id': appointmentId,
        'action': 'rescheduled',
        'patient_name': patientName,
        'doctor_name': doctorName,
        'old_date': oldDate.toIso8601String(),
        'new_date': newDate.toIso8601String(),
      },
    );
  }

  // Task notification methods
  Future<bool> sendNewTaskNotification({
    required String assigneeUserId,
    required String taskId,
    required String taskTitle,
    required String assignedBy,
  }) async {
    return await sendNotificationToUser(
      userId: assigneeUserId,
      title: 'مهمة جديدة',
      body: 'تم تكليفك بمهمة جديدة: $taskTitle من قبل $assignedBy',
      type: 'task',
      data: {
        'task_id': taskId,
        'action': 'new_task',
        'task_title': taskTitle,
        'assigned_by': assignedBy,
      },
    );
  }

  Future<bool> sendTaskStatusChangedNotification({
    required String assignerUserId,
    required String taskId,
    required String taskTitle,
    required String newStatus,
    required String changedBy,
  }) async {
    return await sendNotificationToUser(
      userId: assignerUserId,
      title: 'تم تحديث حالة المهمة',
      body: 'تم تغيير حالة المهمة "$taskTitle" إلى $newStatus بواسطة $changedBy',
      type: 'task',
      data: {
        'task_id': taskId,
        'action': 'status_changed',
        'task_title': taskTitle,
        'new_status': newStatus,
        'changed_by': changedBy,
      },
    );
  }

  Future<bool> sendTaskCompletedNotification({
    required String assignerUserId,
    required String taskId,
    required String taskTitle,
    required String completedBy,
  }) async {
    return await sendNotificationToUser(
      userId: assignerUserId,
      title: 'تم إنجاز المهمة',
      body: 'تم إنجاز المهمة "$taskTitle" بواسطة $completedBy',
      type: 'task',
      data: {
        'task_id': taskId,
        'action': 'completed',
        'task_title': taskTitle,
        'completed_by': completedBy,
      },
    );
  }

  // Utility methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}