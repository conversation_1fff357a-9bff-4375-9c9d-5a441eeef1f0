import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../../../sales/data/repositories/sales_repository.dart';
import '../bloc/employees_bloc.dart';

class EmployeeCommissionsPage extends StatefulWidget {
  const EmployeeCommissionsPage({super.key});

  @override
  State<EmployeeCommissionsPage> createState() => _EmployeeCommissionsPageState();
}

class _EmployeeCommissionsPageState extends State<EmployeeCommissionsPage> {
  EmployeeModel? _selectedEmployee;
  List<SalesInvoiceModel> _employeeInvoices = [];
  bool _isLoading = false;
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedPeriod = 'last_30_days';

  final SalesRepository _salesRepository = SalesRepository();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _statsKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // Load employees when page initializes
    context.read<EmployeesBloc>().add(LoadAllEmployees());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployeeInvoices() async {
    if (_selectedEmployee == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Calculate date range based on selected period
      DateTime startDate;
      DateTime endDate = DateTime.now();

      switch (_selectedPeriod) {
        case 'last_30_days':
          startDate = DateTime.now().subtract(const Duration(days: 30));
          break;
        case 'custom':
          if (_startDate == null || _endDate == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('يرجى تحديد تاريخ البداية والنهاية')),
            );
            setState(() {
              _isLoading = false;
            });
            return;
          }
          startDate = _startDate!;
          endDate = _endDate!;
          break;
        default:
          startDate = DateTime.now().subtract(const Duration(days: 30));
      }

      // Get all invoices and filter by specialist_id and date range
      final allInvoices = await _salesRepository.getAllInvoicesForCommissions(
        page: 1,
        limit: 1000, // Get all invoices
      );

      _employeeInvoices = allInvoices.where((invoice) {
        // Filter by specialist_id
        if (invoice.specialistId != _selectedEmployee!.id) return false;
        
        // Filter by date range
        if (invoice.createdAt.isBefore(startDate) || 
            invoice.createdAt.isAfter(endDate.add(const Duration(days: 1)))) {
          return false;
        }
        
        return true;
      }).toList();

      setState(() {
        _isLoading = false;
      });

      // Scroll to statistics cards after loading data
      _scrollToStats();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
      );
    }
  }

  void _scrollToStats() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_statsKey.currentContext != null) {
        Scrollable.ensureVisible(
          _statsKey.currentContext!,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  double _calculateTotalCommissions() {
    return _employeeInvoices
        .where((invoice) => invoice.isCommissionSale && invoice.status != InvoiceStatus.returned)
        .fold(0.0, (sum, invoice) => sum + invoice.commissionAmount);
  }

  int _getTotalInvoicesCount() {
    return _employeeInvoices.length;
  }

  double _getTotalSalesAmount() {
    return _employeeInvoices.fold(0.0, (sum, invoice) {
      // استخدام نفس طريقة حساب المبلغ المعروض في الكروت
      return sum + _getInvoiceDisplayAmount(invoice);
    });
  }

  Color _getInvoiceCardBorderColor(SalesInvoiceModel invoice) {
    if (invoice.status == InvoiceStatus.returned) {
      return Colors.red.withValues(alpha: 0.6);
    } else if (invoice.paymentStatus == PaymentStatus.paid) {
      return Colors.green.withValues(alpha: 0.6);
    } else if (invoice.paymentStatus == PaymentStatus.partial) {
      return Colors.orange.withValues(alpha: 0.6);
    } else {
      return AppColors.border;
    }
  }

  // Calculate the display amount for invoice cards (مثل ملخص الفاتورة)
  double _getInvoiceDisplayAmount(SalesInvoiceModel invoice) {
    // دائماً نعرض المبلغ بعد الخصم (قبل الإرجاع) - مثل ملخص الفاتورة
    return invoice.totalAmount - invoice.discountAmount;
  }

  // حساب المبلغ المستحق للفواتير المرتجعة (مثل ملخص الفاتورة)
  double _calculateDueAmount(SalesInvoiceModel invoice) {
    if (invoice.status == InvoiceStatus.returned) {
      // في حالة الفاتورة المرتجعة (مثل ملخص الفاتورة):
      // المبلغ المستحق = المبلغ المدفوع - المبلغ المرتجع
      final dueAmount = invoice.paidAmount - invoice.returnAmount;
      return dueAmount > 0 ? dueAmount : 0;
    }
    return invoice.finalAmount;
  }

  void _showInvoiceDetailsDialog(SalesInvoiceModel invoice) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.r),
                    topRight: Radius.circular(16.r),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'تفاصيل الفاتورة #${invoice.invoiceNumber}',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              
              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Customer Info
                      _buildDetailSection(
                        'بيانات العميل',
                        [
                          _buildDetailRow('الاسم', invoice.patient?.name ?? 'غير محدد'),
                          _buildDetailRow('الهاتف', invoice.patient?.phone ?? 'غير محدد'),
                          _buildDetailRow('رقم المريض', invoice.patient?.patientId ?? 'غير محدد'),
                        ],
                      ),
                      
                      SizedBox(height: 20.h),
                      
                      // Invoice Info
                      _buildDetailSection(
                        'بيانات الفاتورة',
                        [
                          _buildDetailRow('التاريخ', '${invoice.createdAt.day}/${invoice.createdAt.month}/${invoice.createdAt.year}'),
                          _buildDetailRow('الوقت', '${invoice.createdAt.hour.toString().padLeft(2, '0')}:${invoice.createdAt.minute.toString().padLeft(2, '0')}'),
                          _buildDetailRow('نوع الدفع', invoice.paymentType.arabicName),
                          _buildDetailRow('حالة الدفع', invoice.paymentStatus.arabicName),
                          _buildDetailRow('حالة الفاتورة', invoice.status.arabicName),
                        ],
                      ),
                      
                      SizedBox(height: 20.h),
                      
                      // Items
                      _buildDetailSection(
                        'المنتجات',
                        invoice.items.map((item) => Container(
                          margin: EdgeInsets.only(bottom: 12.h),
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                            color: AppColors.background,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(color: AppColors.border),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.product?.name ?? item.productName,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              if (item.product?.productCode?.isNotEmpty == true || item.productCode?.isNotEmpty == true) ...[
                                SizedBox(height: 4.h),
                                Text(
                                  'الكود: ${item.product?.productCode ?? item.productCode}',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                              SizedBox(height: 8.h),
                              Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          'الكمية: ${item.quantity}',
                                          style: TextStyle(fontSize: 12.sp),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          'السعر: ${item.unitPrice.toStringAsFixed(2)} د.ا',
                                          style: TextStyle(fontSize: 12.sp),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          'المجموع: ${item.totalPrice.toStringAsFixed(2)} د.ا',
                                          style: TextStyle(fontSize: 12.sp),
                                          textAlign: TextAlign.end,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )).toList(),
                      ),
                      
                      SizedBox(height: 20.h),
                      
                      // Financial Summary (مثل ملخص الفاتورة)
                      _buildDetailSection(
                        'الملخص المالي',
                        [
                          _buildDetailRow('المجموع الفرعي', '${invoice.totalAmount.toStringAsFixed(2)} د.ا'),
                          if (invoice.discountAmount > 0)
                            _buildDetailRow('الخصم', '${invoice.discountAmount.toStringAsFixed(2)} د.ا', color: Colors.red),
                          _buildDetailRow('المجموع الكلي', '${_getInvoiceDisplayAmount(invoice).toStringAsFixed(2)} د.ا', isTotal: true),
                          if (invoice.status == InvoiceStatus.returned && invoice.returnAmount > 0) ...[
                            _buildDetailRow('المبلغ المرتجع', '${invoice.returnAmount.toStringAsFixed(2)} د.ا', color: Colors.red),
                            _buildDetailRow('المبلغ المستحق', '${_calculateDueAmount(invoice).toStringAsFixed(2)} د.ا', color: Colors.blue, isTotal: true),
                          ],
                          if (invoice.status != InvoiceStatus.returned) ...[
                            _buildDetailRow('المبلغ المدفوع', '${invoice.paidAmount.toStringAsFixed(2)} د.ا'),
                            if (invoice.remainingAmount > 0)
                              _buildDetailRow('المبلغ المتبقي', '${invoice.remainingAmount.toStringAsFixed(2)} د.ا', color: Colors.orange),
                          ],
                        ],
                      ),
                      
                      if (invoice.isCommissionSale && invoice.status != InvoiceStatus.returned) ...[
                        SizedBox(height: 20.h),
                        _buildDetailSection(
                          'تفاصيل العمولة',
                          [
                            _buildDetailRow('نسبة العمولة', '${invoice.commissionPercentage.toStringAsFixed(1)}%'),
                            _buildDetailRow('مبلغ العمولة', '${invoice.commissionAmount.toStringAsFixed(2)} د.ا', color: Colors.green),
                          ],
                        ),
                      ],
                      
                      if (invoice.notes?.isNotEmpty == true) ...[
                        SizedBox(height: 20.h),
                        _buildDetailSection(
                          'ملاحظات',
                          [
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(12.w),
                              decoration: BoxDecoration(
                                color: AppColors.background,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(color: AppColors.border),
                              ),
                              child: Text(
                                invoice.notes!,
                                style: TextStyle(fontSize: 14.sp),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        SizedBox(height: 12.h),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? color, bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: isTotal ? 15.sp : 14.sp,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 15.sp : 14.sp,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
              color: color ?? AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          children: [
            // Header with employee selector
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'العمولات والمبيعات',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  
                  // Employee selector
                  BlocBuilder<EmployeesBloc, EmployeesState>(
                    builder: (context, state) {
                      if (state is EmployeesLoaded) {
                        final specialists = state.employees
                            .where((e) => e.isSpecialist)
                            .toList();
                        
                        return DropdownButtonFormField<EmployeeModel>(
                          value: _selectedEmployee,
                          decoration: InputDecoration(
                            labelText: 'اختر الأخصائي',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          items: specialists.map((employee) {
                            return DropdownMenuItem(
                              value: employee,
                              child: Text(employee.name),
                            );
                          }).toList(),
                          onChanged: (employee) {
                            setState(() {
                              _selectedEmployee = employee;
                              _employeeInvoices.clear();
                            });
                            if (employee != null) {
                              _loadEmployeeInvoices();
                            }
                          },
                        );
                      }
                      return const CircularProgressIndicator();
                    },
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Period selector
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPeriod,
                          decoration: InputDecoration(
                            labelText: 'الفترة الزمنية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(
                              value: 'last_30_days',
                              child: Text('آخر 30 يوم'),
                            ),
                            DropdownMenuItem(
                              value: 'custom',
                              child: Text('فترة مخصصة'),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedPeriod = value!;
                            });
                            if (_selectedEmployee != null) {
                              _loadEmployeeInvoices();
                            }
                          },
                        ),
                      ),
                      
                      if (_selectedPeriod == 'custom') ...[
                        SizedBox(width: 16.w),
                        Expanded(
                          child: TextButton(
                            onPressed: () async {
                              final dateRange = await showDateRangePicker(
                                context: context,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                                initialDateRange: _startDate != null && _endDate != null
                                    ? DateTimeRange(start: _startDate!, end: _endDate!)
                                    : null,
                              );
                              if (dateRange != null) {
                                setState(() {
                                  _startDate = dateRange.start;
                                  _endDate = dateRange.end;
                                });
                                if (_selectedEmployee != null) {
                                  _loadEmployeeInvoices();
                                }
                              }
                            },
                            child: Text(
                              _startDate != null && _endDate != null
                                  ? '${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}'
                                  : 'اختر التاريخ',
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            
            // Statistics cards
            if (_selectedEmployee != null) ...[
              Container(
                key: _statsKey,
                padding: EdgeInsets.all(16.w),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي العمولات',
                        '${_calculateTotalCommissions().toStringAsFixed(2)} د.ا',
                        Icons.monetization_on,
                        Colors.green,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        'عدد الفواتير',
                        '${_getTotalInvoicesCount()}',
                        Icons.receipt_long,
                        Colors.blue,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي المبيعات',
                        '${_getTotalSalesAmount().toStringAsFixed(2)} د.ا',
                        Icons.trending_up,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Invoices list
            if (_isLoading)
              Container(
                height: 300.h,
                child: const Center(child: CircularProgressIndicator()),
              )
            else if (_selectedEmployee == null)
              Container(
                height: 300.h,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.person_search,
                        size: 64.sp,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'اختر أخصائي لعرض مبيعاته وعمولاته',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (_employeeInvoices.isEmpty)
              Container(
                height: 300.h,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 64.sp,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'لا توجد فواتير في هذه الفترة',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.all(16.w),
                itemCount: _employeeInvoices.length,
                itemBuilder: (context, index) {
                  final invoice = _employeeInvoices[index];
                  return _buildInvoiceCard(invoice);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32.sp),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(SalesInvoiceModel invoice) {
    return GestureDetector(
      onTap: () => _showInvoiceDetailsDialog(invoice),
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: _getInvoiceCardBorderColor(invoice),
            width: 2.0,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'فاتورة #${invoice.invoiceNumber}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        invoice.patient?.name ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(invoice).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        _getStatusText(invoice),
                        style: TextStyle(
                          fontSize: 11.sp,
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(invoice),
                        ),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${_getInvoiceDisplayAmount(invoice).toStringAsFixed(0)} د.ا',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            // Details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    Icons.calendar_today,
                    'التاريخ',
                    '${invoice.createdAt.day}/${invoice.createdAt.month}/${invoice.createdAt.year}',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.shopping_cart,
                    'المنتجات',
                    '${invoice.items.length}',
                  ),
                ),
                if (invoice.isCommissionSale && invoice.status != InvoiceStatus.returned)
                  Expanded(
                    child: _buildDetailItem(
                      Icons.monetization_on,
                      'العمولة',
                      '${invoice.commissionAmount.toStringAsFixed(2)} د.ا',
                      color: Colors.green,
                    ),
                  ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Tap hint
            Center(
              child: Text(
                'اضغط لعرض التفاصيل',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(SalesInvoiceModel invoice) {
    if (invoice.status == InvoiceStatus.returned) {
      return Colors.red;
    } else if (invoice.paymentStatus == PaymentStatus.paid) {
      return Colors.green;
    } else if (invoice.paymentStatus == PaymentStatus.partial) {
      return Colors.orange;
    } else {
      return Colors.grey;
    }
  }

  String _getStatusText(SalesInvoiceModel invoice) {
    if (invoice.status == InvoiceStatus.returned) {
      return 'مرتجعة';
    } else {
      return invoice.paymentStatus.arabicName;
    }
  }

  Widget _buildDetailItem(IconData icon, String label, String value, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: color ?? AppColors.textSecondary,
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
            color: color ?? AppColors.textPrimary,
          ),
        ),
      ],
    );
  }
}