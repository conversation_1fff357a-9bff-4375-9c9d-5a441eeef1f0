import 'package:equatable/equatable.dart';

class HomeworkModel extends Equatable {
  final String id;
  final String patientId;
  final String title;
  final String? description;
  final String? instructions;
  final String? pdfUrl;
  final DateTime assignedDate;
  final HomeworkStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const HomeworkModel({
    required this.id,
    required this.patientId,
    required this.title,
    this.description,
    this.instructions,
    this.pdfUrl,
    required this.assignedDate,
    this.status = HomeworkStatus.assigned,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HomeworkModel.fromJson(Map<String, dynamic> json) {
    return HomeworkModel(
      id: json['id'] as String,
      patientId: json['patient_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      instructions: json['instructions'] as String?,
      pdfUrl: json['pdf_url'] as String?,
      assignedDate: DateTime.parse(json['assigned_date'] as String),
      status: HomeworkStatus.fromString(json['status'] as String? ?? 'assigned'),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'title': title,
      'description': description,
      'instructions': instructions,
      'pdf_url': pdfUrl,
      'assigned_date': assignedDate.toIso8601String(),
      'status': status.name,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  HomeworkModel copyWith({
    String? id,
    String? patientId,
    String? title,
    String? description,
    String? instructions,
    String? pdfUrl,
    DateTime? assignedDate,
    HomeworkStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HomeworkModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      title: title ?? this.title,
      description: description ?? this.description,
      instructions: instructions ?? this.instructions,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      assignedDate: assignedDate ?? this.assignedDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        patientId,
        title,
        description,
        instructions,
        pdfUrl,
        assignedDate,
        status,
        notes,
        createdAt,
        updatedAt,
      ];
}

// Homework status enum
enum HomeworkStatus {
  assigned('مُكلف'),
  inProgress('قيد التنفيذ'),
  completed('مكتمل');

  const HomeworkStatus(this.arabicName);
  final String arabicName;

  static HomeworkStatus fromString(String value) {
    return HomeworkStatus.values.firstWhere(
      (status) => status.name == value,
      orElse: () => HomeworkStatus.assigned,
    );
  }

  static List<String> get allStatuses => HomeworkStatus.values.map((e) => e.name).toList();
  static List<String> get allArabicNames => HomeworkStatus.values.map((e) => e.arabicName).toList();
}
