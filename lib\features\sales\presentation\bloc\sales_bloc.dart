import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/sales_repository.dart';
import 'sales_event.dart';
import 'sales_state.dart';

class SalesBloc extends Bloc<SalesEvent, SalesState> {
  final SalesRepository _salesRepository;
  static const int _pageSize = 20;

  SalesBloc({required SalesRepository salesRepository})
      : _salesRepository = salesRepository,
        super(SalesInitial()) {
    on<LoadInvoices>(_onLoadInvoices);
    on<LoadMoreInvoices>(_onLoadMoreInvoices);
    on<RefreshInvoices>(_onRefreshInvoices);
    on<CreateInvoice>(_onCreateInvoice);
    on<UpdateInvoice>(_onUpdateInvoice);
    on<LoadInvoiceDetails>(_onLoadInvoiceDetails);
    on<DeleteInvoice>(_onDeleteInvoice);
    on<AddPayment>(_onAddPayment);
    on<SearchInvoices>(_onSearchInvoices);
    on<FilterInvoicesByPaymentStatus>(_onFilterInvoicesByPaymentStatus);
    on<FilterInvoicesByStatus>(_onFilterInvoicesByStatus);
    on<FilterInvoicesByPaymentType>(_onFilterInvoicesByPaymentType);
    on<ClearFilters>(_onClearFilters);
    on<GenerateInvoiceNumber>(_onGenerateInvoiceNumber);
    on<GeneratePaymentNumber>(_onGeneratePaymentNumber);
  }

  Future<void> _onLoadInvoices(LoadInvoices event, Emitter<SalesState> emit) async {
    try {
      developer.log('Loading invoices - Page: ${event.page}', name: 'SalesBloc');
      emit(SalesLoading());

      final invoices = await _salesRepository.getAllInvoices(
        page: event.page,
        limit: _pageSize,
        searchQuery: event.searchQuery,
        paymentStatus: event.paymentStatus,
        status: event.status,
        paymentType: event.paymentType,
        dateFilter: event.dateFilter,
      );

      emit(SalesLoaded(
        invoices: invoices,
        hasReachedMax: invoices.length < _pageSize,
        currentPage: event.page,
        searchQuery: event.searchQuery,
        paymentStatusFilter: event.paymentStatus,
        statusFilter: event.status,
        paymentTypeFilter: event.paymentType,
      ));

      developer.log('Loaded ${invoices.length} invoices', name: 'SalesBloc');
    } catch (e) {
      developer.log('Error loading invoices: $e', name: 'SalesBloc');
      emit(SalesError(_getErrorMessage(e, 'تحميل الفواتير')));
    }
  }

  Future<void> _onLoadMoreInvoices(LoadMoreInvoices event, Emitter<SalesState> emit) async {
    final currentState = state;
    if (currentState is! SalesLoaded || currentState.hasReachedMax) return;

    try {
      developer.log('Loading more invoices - Page: ${currentState.currentPage + 1}', name: 'SalesBloc');

      final newInvoices = await _salesRepository.getAllInvoices(
        page: currentState.currentPage + 1,
        limit: _pageSize,
        searchQuery: currentState.searchQuery,
        paymentStatus: currentState.paymentStatusFilter,
        status: currentState.statusFilter,
      );

      emit(currentState.copyWith(
        invoices: [...currentState.invoices, ...newInvoices],
        hasReachedMax: newInvoices.length < _pageSize,
        currentPage: currentState.currentPage + 1,
      ));

      developer.log('Loaded ${newInvoices.length} more invoices', name: 'SalesBloc');
    } catch (e) {
      developer.log('Error loading more invoices: $e', name: 'SalesBloc');
      emit(SalesError('فشل في تحميل المزيد من الفواتير: $e'));
    }
  }

  Future<void> _onRefreshInvoices(RefreshInvoices event, Emitter<SalesState> emit) async {
    final currentState = state;
    if (currentState is SalesLoaded) {
      add(LoadInvoices(
        searchQuery: currentState.searchQuery,
        paymentStatus: currentState.paymentStatusFilter,
        status: currentState.statusFilter,
      ));
    } else {
      add(const LoadInvoices());
    }
  }

  Future<void> _onCreateInvoice(CreateInvoice event, Emitter<SalesState> emit) async {
    try {
      developer.log('Creating invoice: ${event.invoice.invoiceNumber}', name: 'SalesBloc');
      emit(SalesLoading());

      final createdInvoice = await _salesRepository.createInvoice(
        event.invoice,
        event.items,
      );

      emit(InvoiceCreated(createdInvoice));
      developer.log('Invoice created successfully: ${createdInvoice.id}', name: 'SalesBloc');

      // Refresh invoices list
      add(RefreshInvoices());
    } catch (e) {
      developer.log('Error creating invoice: $e', name: 'SalesBloc');
      emit(SalesError(_getErrorMessage(e, 'إنشاء الفاتورة')));
    }
  }

  Future<void> _onUpdateInvoice(UpdateInvoice event, Emitter<SalesState> emit) async {
    try {
      developer.log('Updating invoice: ${event.invoice.invoiceNumber}', name: 'SalesBloc');
      emit(SalesLoading());

      final updatedInvoice = await _salesRepository.updateInvoice(
        event.invoice,
        event.items,
      );

      emit(InvoiceUpdated(updatedInvoice));
      developer.log('Invoice updated successfully: ${updatedInvoice.id}', name: 'SalesBloc');

      // Refresh invoices list
      add(RefreshInvoices());
    } catch (e) {
      developer.log('Error updating invoice: $e', name: 'SalesBloc');
      emit(SalesError(_getErrorMessage(e, 'تحديث الفاتورة')));
    }
  }

  Future<void> _onLoadInvoiceDetails(LoadInvoiceDetails event, Emitter<SalesState> emit) async {
    try {
      developer.log('Loading invoice details: ${event.invoiceId}', name: 'SalesBloc');
      emit(SalesLoading());

      final invoice = await _salesRepository.getInvoiceById(event.invoiceId);
      emit(InvoiceDetailsLoaded(invoice));

      developer.log('Invoice details loaded successfully', name: 'SalesBloc');
    } catch (e) {
      developer.log('Error loading invoice details: $e', name: 'SalesBloc');
      emit(SalesError('فشل في تحميل تفاصيل الفاتورة: $e'));
    }
  }

  Future<void> _onDeleteInvoice(DeleteInvoice event, Emitter<SalesState> emit) async {
    try {
      developer.log('Deleting invoice: ${event.invoiceId}', name: 'SalesBloc');

      await _salesRepository.deleteInvoice(event.invoiceId);
      emit(InvoiceDeleted(event.invoiceId));

      developer.log('Invoice deleted successfully', name: 'SalesBloc');

      // Refresh invoices list
      add(RefreshInvoices());
    } catch (e) {
      developer.log('Error deleting invoice: $e', name: 'SalesBloc');
      emit(SalesError('فشل في حذف الفاتورة: $e'));
    }
  }

  Future<void> _onAddPayment(AddPayment event, Emitter<SalesState> emit) async {
    try {
      developer.log('Adding payment: ${event.payment.paymentNumber}', name: 'SalesBloc');

      await _salesRepository.addPayment(event.payment);
      
      // Get updated invoice
      final updatedInvoice = await _salesRepository.getInvoiceById(event.payment.invoiceId);
      emit(PaymentAdded(updatedInvoice));

      developer.log('Payment added successfully', name: 'SalesBloc');

      // Refresh invoices list
      add(RefreshInvoices());
    } catch (e) {
      developer.log('Error adding payment: $e', name: 'SalesBloc');
      emit(SalesError('فشل في إضافة الدفعة: $e'));
    }
  }

  Future<void> _onSearchInvoices(SearchInvoices event, Emitter<SalesState> emit) async {
    final currentState = state;
    if (currentState is SalesLoaded) {
      add(LoadInvoices(
        searchQuery: event.query.isEmpty ? null : event.query,
        paymentStatus: currentState.paymentStatusFilter,
        status: currentState.statusFilter,
      ));
    } else {
      add(LoadInvoices(searchQuery: event.query.isEmpty ? null : event.query));
    }
  }

  Future<void> _onFilterInvoicesByPaymentStatus(FilterInvoicesByPaymentStatus event, Emitter<SalesState> emit) async {
    final currentState = state;
    if (currentState is SalesLoaded) {
      add(LoadInvoices(
        searchQuery: currentState.searchQuery,
        paymentStatus: event.paymentStatus,
        status: currentState.statusFilter,
      ));
    } else {
      add(LoadInvoices(paymentStatus: event.paymentStatus));
    }
  }

  Future<void> _onFilterInvoicesByStatus(FilterInvoicesByStatus event, Emitter<SalesState> emit) async {
    final currentState = state;
    if (currentState is SalesLoaded) {
      add(LoadInvoices(
        searchQuery: currentState.searchQuery,
        paymentStatus: currentState.paymentStatusFilter,
        status: event.status,
      ));
    } else {
      add(LoadInvoices(status: event.status));
    }
  }

  Future<void> _onFilterInvoicesByPaymentType(FilterInvoicesByPaymentType event, Emitter<SalesState> emit) async {
    final currentState = state;
    if (currentState is SalesLoaded) {
      add(LoadInvoices(
        searchQuery: currentState.searchQuery,
        paymentStatus: currentState.paymentStatusFilter,
        status: currentState.statusFilter,
        paymentType: event.paymentType,
      ));
    } else {
      add(LoadInvoices(paymentType: event.paymentType));
    }
  }

  Future<void> _onClearFilters(ClearFilters event, Emitter<SalesState> emit) async {
    add(const LoadInvoices());
  }

  Future<void> _onGenerateInvoiceNumber(GenerateInvoiceNumber event, Emitter<SalesState> emit) async {
    try {
      final number = await _salesRepository.generateInvoiceNumber();
      emit(NumberGenerated(generatedNumber: number, type: 'invoice'));
    } catch (e) {
      developer.log('Error generating invoice number: $e', name: 'SalesBloc');
      emit(SalesError(_getErrorMessage(e, 'توليد رقم الفاتورة')));
    }
  }

  Future<void> _onGeneratePaymentNumber(GeneratePaymentNumber event, Emitter<SalesState> emit) async {
    try {
      final number = await _salesRepository.generatePaymentNumber();
      emit(NumberGenerated(generatedNumber: number, type: 'payment'));
    } catch (e) {
      developer.log('Error generating payment number: $e', name: 'SalesBloc');
      emit(SalesError(_getErrorMessage(e, 'توليد رقم الدفعة')));
    }
  }

  /// Convert technical errors to user-friendly Arabic messages
  String _getErrorMessage(dynamic error, String operation) {
    final errorString = error.toString().toLowerCase();

    // Database constraint errors
    if (errorString.contains('constraint') || errorString.contains('check')) {
      return 'خطأ في البيانات المدخلة. يرجى التحقق من صحة المعلومات.';
    }

    // Network/connection errors
    if (errorString.contains('network') || errorString.contains('connection') ||
        errorString.contains('timeout') || errorString.contains('socket')) {
      return 'مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.';
    }

    // Authentication errors
    if (errorString.contains('auth') || errorString.contains('unauthorized') ||
        errorString.contains('permission')) {
      return 'ليس لديك صلاحية للقيام بهذا الإجراء.';
    }

    // Validation errors
    if (errorString.contains('validation') || errorString.contains('invalid')) {
      return 'البيانات المدخلة غير صحيحة. يرجى المراجعة والمحاولة مرة أخرى.';
    }

    // Duplicate errors
    if (errorString.contains('duplicate') || errorString.contains('unique')) {
      return 'هذه البيانات موجودة مسبقاً. يرجى استخدام بيانات مختلفة.';
    }

    // Not found errors
    if (errorString.contains('not found') || errorString.contains('404')) {
      return 'البيانات المطلوبة غير موجودة.';
    }

    // Server errors
    if (errorString.contains('500') || errorString.contains('server error')) {
      return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
    }

    // Default error message
    return 'حدث خطأ أثناء $operation. يرجى المحاولة مرة أخرى.';
  }
}
