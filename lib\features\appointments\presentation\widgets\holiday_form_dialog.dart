import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/holiday_model.dart';
import '../bloc/holidays_bloc.dart';
import '../bloc/holidays_event.dart';

class HolidayFormDialog extends StatefulWidget {
  final HolidayModel? holiday;

  const HolidayFormDialog({
    super.key,
    this.holiday,
  });

  @override
  State<HolidayFormDialog> createState() => _HolidayFormDialogState();
}

class _HolidayFormDialogState extends State<HolidayFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _occasionController;
  late TextEditingController _notesController;

  DateTime? _selectedDate;
  String _selectedType = 'official';
  bool _isActive = true;

  final List<Map<String, String>> _holidayTypes = [
    {'value': 'official', 'label': 'عطلة رسمية'},
    {'value': 'emergency', 'label': 'إجازة طارئة'},
    {'value': 'personal', 'label': 'إجازة شخصية'},
    {'value': 'maintenance', 'label': 'صيانة العيادة'},
  ];

  @override
  void initState() {
    super.initState();
    _occasionController = TextEditingController(
      text: widget.holiday?.occasionName ?? '',
    );
    _notesController = TextEditingController(
      text: widget.holiday?.notes ?? '',
    );

    if (widget.holiday != null) {
      _selectedDate = widget.holiday!.holidayDate;
      _selectedType = widget.holiday!.holidayType;
      _isActive = widget.holiday!.isActive;
    }
  }

  @override
  void dispose() {
    _occasionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _saveHoliday() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار تاريخ الإجازة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final holiday = HolidayModel(
      id: widget.holiday?.id ?? const Uuid().v4(),
      holidayDate: _selectedDate!,
      occasionName: _occasionController.text.trim(),
      holidayType: _selectedType,
      isActive: _isActive,
      notes: _notesController.text.trim().isNotEmpty
          ? _notesController.text.trim()
          : null,
      createdAt: widget.holiday?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (widget.holiday == null) {
      // Add new holiday
      context.read<HolidaysBloc>().add(AddHoliday(holiday: holiday));
    } else {
      // Update existing holiday
      context.read<HolidaysBloc>().add(UpdateHoliday(holiday: holiday));
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header
              Container(
                padding: EdgeInsets.all(24.w),
                child: Text(
                  widget.holiday == null ? 'إضافة يوم إجازة' : 'تعديل يوم الإجازة',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Scrollable content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Occasion name
              TextFormField(
                controller: _occasionController,
                decoration: InputDecoration(
                  labelText: 'اسم المناسبة *',
                  hintText: 'مثال: عيد الفطر، إجازة طارئة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال اسم المناسبة';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16.h),

              // Date selection
              InkWell(
                onTap: _selectDate,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.gray300),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: AppColors.primary,
                        size: 20.w,
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          _selectedDate == null
                              ? 'اختر تاريخ الإجازة *'
                              : '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: _selectedDate == null
                                ? AppColors.textSecondary
                                : AppColors.textPrimary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16.h),

              // Holiday type
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: InputDecoration(
                  labelText: 'نوع الإجازة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                items: _holidayTypes.map((type) {
                  return DropdownMenuItem<String>(
                    value: type['value'],
                    child: Text(type['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              SizedBox(height: 16.h),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  hintText: 'أي ملاحظات إضافية...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                maxLines: 3,
                maxLength: 500,
              ),
              SizedBox(height: 16.h),

              // Active status
              Row(
                children: [
                  Switch(
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                    activeColor: AppColors.primary,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'نشط',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _isActive
                          ? 'الإجازة نشطة ومفعلة'
                          : 'الإجازة غير نشطة',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24.h),

                    ],
                  ),
                ),
              ),

              // Fixed footer with action buttons
              Container(
                padding: EdgeInsets.all(24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveHoliday,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          widget.holiday == null ? 'إضافة' : 'تحديث',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
