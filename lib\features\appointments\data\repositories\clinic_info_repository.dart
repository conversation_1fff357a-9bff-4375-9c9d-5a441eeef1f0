import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/clinic_info_model.dart';

class ClinicInfoRepository {
  // Get all clinic information
  Future<List<ClinicInfoModel>> getAllClinicInfo() async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Loading all clinic info...');
      final response = await SupabaseConfig.clinicInfo
          .select()
          .eq('is_active', true)
          .order('display_order', ascending: true);

      debugPrint('📊 ClinicInfoRepository: Raw response: $response');
      debugPrint('📊 ClinicInfoRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ ClinicInfoRepository: No clinic info found');
        return [];
      }

      final clinicInfoList = response.map((json) => ClinicInfoModel.fromJson(json)).toList();
      debugPrint('✅ ClinicInfoRepository: Successfully parsed ${clinicInfoList.length} clinic info items');
      
      return clinicInfoList;
    } catch (e, stackTrace) {
      debugPrint('❌ ClinicInfoRepository: Error loading clinic info: $e');
      debugPrint('📍 ClinicInfoRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب معلومات العيادة: ${e.toString()}');
    }
  }

  // Get clinic info by type
  Future<List<ClinicInfoModel>> getClinicInfoByType(String infoType) async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Loading clinic info by type: $infoType');
      final response = await SupabaseConfig.clinicInfo
          .select()
          .eq('info_type', infoType)
          .eq('is_active', true)
          .order('display_order', ascending: true);

      final clinicInfoList = response.map((json) => ClinicInfoModel.fromJson(json)).toList();
      debugPrint('✅ ClinicInfoRepository: Successfully loaded ${clinicInfoList.length} items for type $infoType');
      
      return clinicInfoList;
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error loading clinic info by type: $e');
      throw Exception('فشل في جلب معلومات العيادة: ${e.toString()}');
    }
  }

  // Get phone numbers
  Future<List<ClinicInfoModel>> getPhoneNumbers() async {
    return getClinicInfoByType('phone');
  }

  // Get email addresses
  Future<List<ClinicInfoModel>> getEmailAddresses() async {
    return getClinicInfoByType('email');
  }

  // Get social media links
  Future<List<ClinicInfoModel>> getSocialMediaLinks() async {
    return getClinicInfoByType('social_media');
  }

  // Get addresses
  Future<List<ClinicInfoModel>> getAddresses() async {
    return getClinicInfoByType('address');
  }

  // Get working hours
  Future<List<ClinicInfoModel>> getWorkingHours() async {
    return getClinicInfoByType('working_hours');
  }

  // Get website info
  Future<List<ClinicInfoModel>> getWebsiteInfo() async {
    return getClinicInfoByType('website');
  }

  // Add new clinic info
  Future<ClinicInfoModel> addClinicInfo(ClinicInfoModel clinicInfo) async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Adding new clinic info: ${clinicInfo.displayName}');
      
      final response = await SupabaseConfig.clinicInfo
          .insert(clinicInfo.toJson())
          .select()
          .single();

      final newClinicInfo = ClinicInfoModel.fromJson(response);
      debugPrint('✅ ClinicInfoRepository: Successfully added clinic info: ${newClinicInfo.id}');
      
      return newClinicInfo;
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error adding clinic info: $e');
      throw Exception('فشل في إضافة معلومات العيادة: ${e.toString()}');
    }
  }

  // Update clinic info
  Future<ClinicInfoModel> updateClinicInfo(ClinicInfoModel clinicInfo) async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Updating clinic info: ${clinicInfo.id}');
      
      final response = await SupabaseConfig.clinicInfo
          .update(clinicInfo.toJson())
          .eq('id', clinicInfo.id)
          .select()
          .single();

      final updatedClinicInfo = ClinicInfoModel.fromJson(response);
      debugPrint('✅ ClinicInfoRepository: Successfully updated clinic info: ${updatedClinicInfo.id}');
      
      return updatedClinicInfo;
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error updating clinic info: $e');
      throw Exception('فشل في تحديث معلومات العيادة: ${e.toString()}');
    }
  }

  // Delete clinic info
  Future<void> deleteClinicInfo(String clinicInfoId) async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Deleting clinic info: $clinicInfoId');
      
      await SupabaseConfig.clinicInfo
          .delete()
          .eq('id', clinicInfoId);

      debugPrint('✅ ClinicInfoRepository: Successfully deleted clinic info: $clinicInfoId');
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error deleting clinic info: $e');
      throw Exception('فشل في حذف معلومات العيادة: ${e.toString()}');
    }
  }

  // Toggle clinic info active status
  Future<ClinicInfoModel> toggleClinicInfoStatus(String clinicInfoId, bool isActive) async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Toggling clinic info status: $clinicInfoId to $isActive');
      
      final response = await SupabaseConfig.clinicInfo
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', clinicInfoId)
          .select()
          .single();

      final updatedClinicInfo = ClinicInfoModel.fromJson(response);
      debugPrint('✅ ClinicInfoRepository: Successfully toggled clinic info status');
      
      return updatedClinicInfo;
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error toggling clinic info status: $e');
      throw Exception('فشل في تغيير حالة معلومات العيادة: ${e.toString()}');
    }
  }

  // Search clinic info
  Future<List<ClinicInfoModel>> searchClinicInfo(String query) async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Searching clinic info with query: $query');
      
      final response = await SupabaseConfig.clinicInfo
          .select()
          .or('display_name.ilike.%$query%,info_value.ilike.%$query%')
          .eq('is_active', true)
          .order('display_order', ascending: true);

      final clinicInfoList = response.map((json) => ClinicInfoModel.fromJson(json)).toList();
      debugPrint('✅ ClinicInfoRepository: Found ${clinicInfoList.length} clinic info items matching query');
      
      return clinicInfoList;
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error searching clinic info: $e');
      throw Exception('فشل في البحث عن معلومات العيادة: ${e.toString()}');
    }
  }

  // Get clinic info grouped by type
  Future<Map<String, List<ClinicInfoModel>>> getClinicInfoGroupedByType() async {
    try {
      debugPrint('🔍 ClinicInfoRepository: Loading clinic info grouped by type...');
      
      final allInfo = await getAllClinicInfo();
      final Map<String, List<ClinicInfoModel>> grouped = {};
      
      for (final info in allInfo) {
        if (!grouped.containsKey(info.infoType)) {
          grouped[info.infoType] = [];
        }
        grouped[info.infoType]!.add(info);
      }
      
      debugPrint('✅ ClinicInfoRepository: Successfully grouped clinic info by ${grouped.keys.length} types');
      return grouped;
    } catch (e) {
      debugPrint('❌ ClinicInfoRepository: Error grouping clinic info: $e');
      throw Exception('فشل في تجميع معلومات العيادة: ${e.toString()}');
    }
  }
}
