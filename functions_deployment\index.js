const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { createClient } = require('@supabase/supabase-js');

admin.initializeApp();

const supabaseUrl = 'https://xwxeauofbzedfzaogzzy.supabase.co';
const supabaseKey = functions.config().supabase.key;
const supabase = createClient(supabaseUrl, supabaseKey);

exports.sendScheduledNotifications = functions
  .pubsub
  .schedule('every 1 minutes')
  .timeZone('Asia/Riyadh')
  .onRun(async (context) => {
    console.log('🔍 Checking for due notifications...');
    
    try {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const currentDayOfWeek = now.getDay() === 0 ? 7 : now.getDay();
      
      console.log(`⏰ Current time: ${currentTime}, Day: ${currentDayOfWeek}`);
      
      const { data: notifications, error } = await supabase
        .from('scheduled_notifications')
        .select('*')
        .eq('is_active', true)
        .eq('scheduled_time', currentTime);
      
      if (error) {
        console.error('❌ Error fetching notifications:', error);
        return null;
      }
      
      const dueNotifications = notifications.filter(notification => {
        const daysOfWeek = notification.days_of_week || [];
        return daysOfWeek.includes(currentDayOfWeek);
      });
      
      console.log(`📊 Found ${dueNotifications.length} due notifications`);
      
      let successCount = 0;
      for (const notification of dueNotifications) {
        const success = await sendNotificationToPatient(notification);
        if (success) successCount++;
      }
      
      if (dueNotifications.length > 0) {
        console.log(`✅ Successfully sent ${successCount}/${dueNotifications.length} notifications`);
      }
      
      return { processed: dueNotifications.length, successful: successCount };
      
    } catch (error) {
      console.error('❌ Error in scheduled notifications:', error);
      return null;
    }
  });

async function sendNotificationToPatient(notification) {
  try {
    const patientId = notification.patient_id;
    
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single();
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`);
      return false;
    }
    
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true);
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId}`);
      return false;
    }
    
    const title = getNotificationTitle(notification.notification_type);
    const body = `مرحباً ${patient.name}، ${notification.body}`;
    
    let sentCount = 0;
    for (const tokenData of tokens) {
      const message = {
        token: tokenData.fcm_token,
        notification: { title, body },
        data: {
          type: notification.notification_type,
          patient_id: patientId,
          reminder_id: notification.reminder_id || '',
        },
        android: {
          priority: 'high',
          notification: {
            channel_id: 'diet_rx_notifications',
            sound: 'default',
          },
        },
        apns: {
          headers: { 'apns-priority': '10' },
          payload: {
            aps: {
              alert: { title, body },
              sound: 'default',
              badge: 1,
            },
          },
        },
      };
      
      try {
        const response = await admin.messaging().send(message);
        console.log(`✅ Notification sent: ${response}`);
        sentCount++;
        
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title, body,
          status: 'sent',
          firebase_response: { messageId: response },
          created_at: new Date().toISOString(),
        });
        
      } catch (sendError) {
        console.error(`❌ Failed to send:`, sendError);
        
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title, body,
          status: 'failed',
          error_message: sendError.message,
          created_at: new Date().toISOString(),
        });
      }
    }
    
    return sentCount > 0;
    
  } catch (error) {
    console.error('❌ Error sending notification:', error);
    return false;
  }
}

function getNotificationTitle(notificationType) {
  switch (notificationType) {
    case 'meal': return '🍽️ تذكير الوجبة';
    case 'exercise': return '🏃‍♂️ تذكير النشاط البدني';
    case 'medication': return '💊 تذكير الدواء';
    case 'water': return '💧 تذكير شرب الماء';
    default: return '🔔 تذكير من Diet Rx';
  }
}

exports.testNotification = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  
  try {
    const patientId = req.query.patientId || '**********';
    
    const testNotification = {
      id: 'test-' + Date.now(),
      patient_id: patientId,
      notification_type: 'test',
      body: 'هذا إشعار تجريبي من Firebase Cloud Functions',
    };
    
    const success = await sendNotificationToPatient(testNotification);
    
    res.json({ 
      success, 
      message: success ? 'تم إرسال الإشعار بنجاح' : 'فشل في إرسال الإشعار',
      patientId 
    });
    
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});