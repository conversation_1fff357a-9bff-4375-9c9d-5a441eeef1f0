class AppointmentStatistics {
  final int confirmedCount;
  final int completedCount;
  final int cancelledCount;
  final int noShowCount;
  final int totalCount;

  const AppointmentStatistics({
    required this.confirmedCount,
    required this.completedCount,
    required this.cancelledCount,
    required this.noShowCount,
    required this.totalCount,
  });

  factory AppointmentStatistics.empty() {
    return const AppointmentStatistics(
      confirmedCount: 0,
      completedCount: 0,
      cancelledCount: 0,
      noShowCount: 0,
      totalCount: 0,
    );
  }

  factory AppointmentStatistics.fromAppointments(List<dynamic> appointments) {
    int confirmed = 0;
    int completed = 0;
    int cancelled = 0;
    int noShow = 0;

    for (final appointment in appointments) {
      final status = appointment['status'] as String?;
      switch (status) {
        case 'confirmed':
          confirmed++;
          break;
        case 'completed':
          completed++;
          break;
        case 'cancelled':
          cancelled++;
          break;
        case 'no_show':
          noShow++;
          break;
      }
    }

    return AppointmentStatistics(
      confirmedCount: confirmed,
      completedCount: completed,
      cancelledCount: cancelled,
      noShowCount: noShow,
      totalCount: appointments.length,
    );
  }

  String getStatusDisplayName(String status) {
    switch (status) {
      case 'confirmed':
        return 'مؤكدة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'no_show':
        return 'لم يحضر';
      default:
        return 'غير محدد';
    }
  }

  int getCountForStatus(String status) {
    switch (status) {
      case 'confirmed':
        return confirmedCount;
      case 'completed':
        return completedCount;
      case 'cancelled':
        return cancelledCount;
      case 'no_show':
        return noShowCount;
      default:
        return 0;
    }
  }
}
