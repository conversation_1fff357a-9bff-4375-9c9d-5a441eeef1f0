import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/specialization_model.dart';

class EmployeesRepository {
  // Get all employees with their specializations
  static Future<List<EmployeeModel>> getAllEmployees() async {
    try {
      debugPrint('🔍 EmployeesRepository: Loading all employees...');
      
      final response = await SupabaseConfig.admins
          .select('''
            *,
            specializations:specialization_id (
              id,
              name,
              name_en,
              description,
              color,
              icon,
              is_active,
              display_order,
              created_at,
              updated_at
            )
          ''')
          .order('created_at', ascending: false);

      debugPrint('📊 EmployeesRepository: Raw response length: ${response.length}');

      final employees = response.map((json) {
        // Handle specialization data
        SpecializationModel? specialization;
        if (json['specializations'] != null) {
          specialization = SpecializationModel.fromJson(json['specializations'] as Map<String, dynamic>);
        }

        // Create employee with specialization
        final employee = EmployeeModel.fromJson(json);
        return employee.copyWith(specialization: specialization);
      }).toList();

      debugPrint('✅ EmployeesRepository: Successfully loaded ${employees.length} employees');
      return employees;
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error loading employees: $e');
      throw Exception('فشل في جلب الموظفين: $e');
    }
  }

  // Get employees by type
  static Future<List<EmployeeModel>> getEmployeesByType(String employeeType) async {
    try {
      debugPrint('🔍 EmployeesRepository: Loading employees by type: $employeeType');
      
      final response = await SupabaseConfig.admins
          .select('''
            *,
            specializations:specialization_id (
              id,
              name,
              name_en,
              description,
              color,
              icon,
              is_active,
              display_order,
              created_at,
              updated_at
            )
          ''')
          .eq('employee_type', employeeType)
          .eq('is_active', true)
          .order('name', ascending: true);

      final employees = response.map((json) {
        SpecializationModel? specialization;
        if (json['specializations'] != null) {
          specialization = SpecializationModel.fromJson(json['specializations'] as Map<String, dynamic>);
        }

        final employee = EmployeeModel.fromJson(json);
        return employee.copyWith(specialization: specialization);
      }).toList();

      debugPrint('✅ EmployeesRepository: Successfully loaded ${employees.length} employees of type $employeeType');
      return employees;
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error loading employees by type: $e');
      throw Exception('فشل في جلب الموظفين: $e');
    }
  }

  // Get specialists by specialization
  static Future<List<EmployeeModel>> getSpecialistsBySpecialization(String specializationId) async {
    try {
      debugPrint('🔍 EmployeesRepository: Loading specialists by specialization: $specializationId');
      
      final response = await SupabaseConfig.admins
          .select('''
            *,
            specializations:specialization_id (
              id,
              name,
              name_en,
              description,
              color,
              icon,
              is_active,
              display_order,
              created_at,
              updated_at
            )
          ''')
          .eq('employee_type', 'specialist')
          .eq('specialization_id', specializationId)
          .eq('is_active', true)
          .order('name', ascending: true);

      final employees = response.map((json) {
        SpecializationModel? specialization;
        if (json['specializations'] != null) {
          specialization = SpecializationModel.fromJson(json['specializations'] as Map<String, dynamic>);
        }

        final employee = EmployeeModel.fromJson(json);
        return employee.copyWith(specialization: specialization);
      }).toList();

      debugPrint('✅ EmployeesRepository: Successfully loaded ${employees.length} specialists');
      return employees;
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error loading specialists: $e');
      throw Exception('فشل في جلب الأخصائيين: $e');
    }
  }

  // Add new employee
  static Future<EmployeeModel> addEmployee(EmployeeModel employee) async {
    try {
      debugPrint('🔍 EmployeesRepository: Adding new employee: ${employee.name}');
      
      final response = await SupabaseConfig.admins
          .insert(employee.toJson())
          .select()
          .single();

      final newEmployee = EmployeeModel.fromJson(response);
      debugPrint('✅ EmployeesRepository: Successfully added employee');
      
      return newEmployee;
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error adding employee: $e');
      throw Exception('فشل في إضافة الموظف: $e');
    }
  }

  // Update employee
  static Future<EmployeeModel> updateEmployee(EmployeeModel employee) async {
    try {
      debugPrint('🔍 EmployeesRepository: Updating employee: ${employee.name}');
      
      final response = await SupabaseConfig.admins
          .update(employee.toJson())
          .eq('id', employee.id)
          .select()
          .single();

      final updatedEmployee = EmployeeModel.fromJson(response);
      debugPrint('✅ EmployeesRepository: Successfully updated employee');
      
      return updatedEmployee;
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error updating employee: $e');
      throw Exception('فشل في تحديث الموظف: $e');
    }
  }

  // Delete employee
  static Future<void> deleteEmployee(String employeeId) async {
    try {
      debugPrint('🔍 EmployeesRepository: Deleting employee: $employeeId');
      
      await SupabaseConfig.admins
          .delete()
          .eq('id', employeeId);

      debugPrint('✅ EmployeesRepository: Successfully deleted employee');
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error deleting employee: $e');
      throw Exception('فشل في حذف الموظف: $e');
    }
  }

  // Toggle employee active status
  static Future<EmployeeModel> toggleEmployeeStatus(String employeeId, bool isActive) async {
    try {
      debugPrint('🔍 EmployeesRepository: Toggling employee status: $employeeId to $isActive');
      
      final response = await SupabaseConfig.admins
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', employeeId)
          .select()
          .single();

      final updatedEmployee = EmployeeModel.fromJson(response);
      debugPrint('✅ EmployeesRepository: Successfully toggled employee status');
      
      return updatedEmployee;
    } catch (e) {
      debugPrint('❌ EmployeesRepository: Error toggling employee status: $e');
      throw Exception('فشل في تغيير حالة الموظف: $e');
    }
  }
}
