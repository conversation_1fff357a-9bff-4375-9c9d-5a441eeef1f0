import 'package:equatable/equatable.dart';

enum PaymentMethod { cash, card, bankTransfer, check }

extension PaymentMethodExtension on PaymentMethod {
  String get value {
    switch (this) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
      case PaymentMethod.check:
        return 'check';
    }
  }

  String get arabicName {
    switch (this) {
      case PaymentMethod.cash:
        return 'نقداً';
      case PaymentMethod.card:
        return 'بطاقة ائتمان';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.check:
        return 'شيك';
    }
  }

  static PaymentMethod fromString(String value) {
    switch (value) {
      case 'cash':
        return PaymentMethod.cash;
      case 'card':
        return PaymentMethod.card;
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      case 'check':
        return PaymentMethod.check;
      default:
        return PaymentMethod.cash;
    }
  }
}

class PaymentModel extends Equatable {
  final String id;
  final String invoiceId;
  final String paymentNumber;
  final double amount;
  final PaymentMethod paymentMethod;
  final DateTime paymentDate;
  final String? notes;
  final String? createdBy;
  final DateTime createdAt;

  const PaymentModel({
    required this.id,
    required this.invoiceId,
    required this.paymentNumber,
    required this.amount,
    required this.paymentMethod,
    required this.paymentDate,
    this.notes,
    this.createdBy,
    required this.createdAt,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'] as String? ?? '',
      invoiceId: json['invoice_id'] as String? ?? '',
      paymentNumber: json['payment_number'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: PaymentMethodExtension.fromString(json['payment_method'] as String? ?? 'cash'),
      paymentDate: json['payment_date'] != null ? DateTime.parse(json['payment_date'] as String) : DateTime.now(),
      notes: json['notes'] as String?,
      createdBy: json['created_by'] as String?,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // Don't include 'id' when creating new payments, let database generate UUID
      'invoice_id': invoiceId,
      'payment_number': paymentNumber,
      'amount': amount,
      'payment_method': paymentMethod.value,
      'payment_date': paymentDate.toIso8601String().split('T')[0], // Date only
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
    };
  }

  PaymentModel copyWith({
    String? id,
    String? invoiceId,
    String? paymentNumber,
    double? amount,
    PaymentMethod? paymentMethod,
    DateTime? paymentDate,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      paymentNumber: paymentNumber ?? this.paymentNumber,
      amount: amount ?? this.amount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentDate: paymentDate ?? this.paymentDate,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        invoiceId,
        paymentNumber,
        amount,
        paymentMethod,
        paymentDate,
        notes,
        createdBy,
        createdAt,
      ];
}
