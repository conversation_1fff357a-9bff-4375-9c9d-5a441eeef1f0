import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/services/appointment_notification_service.dart';
import '../../../../core/utils/logger.dart';

class AppointmentStatusUpdateWidget extends StatefulWidget {
  final String appointmentId;
  final String currentStatus;
  final String patientUserId;
  final String patientName;
  final String doctorName;
  final DateTime appointmentDate;
  final Function(String newStatus) onStatusChanged;

  const AppointmentStatusUpdateWidget({
    super.key,
    required this.appointmentId,
    required this.currentStatus,
    required this.patientUserId,
    required this.patientName,
    required this.doctorName,
    required this.appointmentDate,
    required this.onStatusChanged,
  });

  @override
  State<AppointmentStatusUpdateWidget> createState() => _AppointmentStatusUpdateWidgetState();
}

class _AppointmentStatusUpdateWidgetState extends State<AppointmentStatusUpdateWidget> {
  final Logger _logger = Logger('AppointmentStatusUpdateWidget');
  final AppointmentNotificationService _notificationService = AppointmentNotificationService();
  
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.update,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تحديث حالة الحجز',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            Text(
              'الحالة الحالية: ${_getStatusDisplayName(widget.currentStatus)}',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
            
            SizedBox(height: 16.h),
            
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: [
                _buildStatusButton('confirmed', 'مؤكد', Colors.green),
                _buildStatusButton('in_progress', 'قيد التنفيذ', Colors.blue),
                _buildStatusButton('completed', 'مكتمل', Colors.purple),
                _buildStatusButton('cancelled', 'ملغي', Colors.red),
                _buildStatusButton('no_show', 'عدم حضور', Colors.orange),
              ],
            ),
            
            if (_isUpdating) ...[
              SizedBox(height: 16.h),
              Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(String status, String label, Color color) {
    final isCurrentStatus = widget.currentStatus == status;
    final isDisabled = _isUpdating || isCurrentStatus;

    return ElevatedButton(
      onPressed: isDisabled ? null : () => _updateStatus(status),
      style: ElevatedButton.styleFrom(
        backgroundColor: isCurrentStatus ? color : Colors.grey[200],
        foregroundColor: isCurrentStatus ? Colors.white : color,
        elevation: isCurrentStatus ? 2 : 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
          side: BorderSide(color: color, width: 1),
        ),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: isCurrentStatus ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Future<void> _updateStatus(String newStatus) async {
    if (_isUpdating || newStatus == widget.currentStatus) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      _logger.info('📅 Updating appointment status: ${widget.appointmentId} -> $newStatus');

      // Update status in database (you'll need to implement this)
      // await _updateAppointmentStatusInDatabase(newStatus);

      // Send notification based on status
      await _sendStatusNotification(newStatus);

      // Call the callback
      widget.onStatusChanged(newStatus);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث حالة الحجز إلى ${_getStatusDisplayName(newStatus)}'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      _logger.error('❌ Failed to update appointment status: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث حالة الحجز: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _sendStatusNotification(String newStatus) async {
    try {
      switch (newStatus) {
        case 'confirmed':
          await _notificationService.notifyAppointmentConfirmed(
            appointmentId: widget.appointmentId,
            patientUserId: widget.patientUserId,
            patientName: widget.patientName,
            doctorName: widget.doctorName,
            appointmentDate: widget.appointmentDate,
          );
          break;
          
        case 'cancelled':
          await _notificationService.notifyAppointmentCancelled(
            appointmentId: widget.appointmentId,
            patientUserId: widget.patientUserId,
            patientName: widget.patientName,
            doctorName: widget.doctorName,
            appointmentDate: widget.appointmentDate,
          );
          break;
          
        default:
          await _notificationService.notifyAppointmentStatusChanged(
            appointmentId: widget.appointmentId,
            patientUserId: widget.patientUserId,
            patientName: widget.patientName,
            doctorName: widget.doctorName,
            appointmentDate: widget.appointmentDate,
            newStatus: newStatus,
            statusDisplayName: _getStatusDisplayName(newStatus),
          );
      }
    } catch (e) {
      _logger.error('❌ Failed to send status notification: $e');
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'عدم حضور';
      default:
        return status;
    }
  }
}