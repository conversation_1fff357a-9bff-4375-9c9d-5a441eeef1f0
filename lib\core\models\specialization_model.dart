import 'package:equatable/equatable.dart';

class SpecializationModel extends Equatable {
  final String id;
  final String name;
  final String? nameEn;
  final String? description;
  final String color;
  final String? icon;
  final bool isActive;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SpecializationModel({
    required this.id,
    required this.name,
    this.nameEn,
    this.description,
    this.color = '#01928f',
    this.icon,
    this.isActive = true,
    this.displayOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SpecializationModel.fromJson(Map<String, dynamic> json) {
    return SpecializationModel(
      id: json['id'] as String,
      name: json['name'] as String,
      nameEn: json['name_en'] as String?,
      description: json['description'] as String?,
      color: json['color'] as String? ?? '#01928f',
      icon: json['icon'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      displayOrder: json['display_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
      'name_en': nameEn,
      'description': description,
      'color': color,
      'icon': icon,
      'is_active': isActive,
      'display_order': displayOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  SpecializationModel copyWith({
    String? id,
    String? name,
    String? nameEn,
    String? description,
    String? color,
    String? icon,
    bool? isActive,
    int? displayOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SpecializationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isActive: isActive ?? this.isActive,
      displayOrder: displayOrder ?? this.displayOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nameEn,
        description,
        color,
        icon,
        isActive,
        displayOrder,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'SpecializationModel(id: $id, name: $name, nameEn: $nameEn, isActive: $isActive)';
  }
}
