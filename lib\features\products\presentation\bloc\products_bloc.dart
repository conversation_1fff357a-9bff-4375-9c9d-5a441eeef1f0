import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/models/product_image_model.dart';
import '../../data/repositories/products_repository.dart';
import 'products_event.dart';
import 'products_state.dart';

class ProductsBloc extends Bloc<ProductsEvent, ProductsState> {
  final ProductsRepository _productsRepository;

  ProductsBloc({required ProductsRepository productsRepository})
      : _productsRepository = productsRepository,
        super(ProductsInitial()) {
    on<LoadAllProducts>(_onLoadAllProducts);

    on<SearchProducts>(_onSearchProducts);
    on<CreateProduct>(_onCreateProduct);
    on<UpdateProduct>(_onUpdateProduct);
    on<DeleteProduct>(_onDeleteProduct);
    on<UpdateProductStock>(_onUpdateProductStock);
    on<CreateProductWithImages>(_onCreateProductWithImages);
    on<UploadProductImages>(_onUploadProductImages);
    on<DeleteProductImage>(_onDeleteProductImage);
    on<RefreshProducts>(_onRefreshProducts);
    on<FilterProductsByPrice>(_onFilterProductsByPrice);
  }

  Future<void> _onLoadAllProducts(
    LoadAllProducts event,
    Emitter<ProductsState> emit,
  ) async {
    emit(ProductsLoading());
    try {
      debugPrint('🔄 ProductsBloc: Loading all products...');
      final products = await _productsRepository.getAllProducts();
      debugPrint('✅ ProductsBloc: Loaded ${products.length} products successfully');

      debugPrint('🔄 ProductsBloc: Loading categories...');
      final categories = await _productsRepository.getCategories();
      debugPrint('✅ ProductsBloc: Loaded ${categories.length} categories successfully');

      emit(ProductsLoaded(
        products: products,
        filteredProducts: products,
        categories: ['الكل', ...categories],
      ));
    } catch (e) {
      debugPrint('❌ ProductsBloc: Error loading products: $e');
      emit(ProductsError(message: e.toString()));
    }
  }



  Future<void> _onSearchProducts(
    SearchProducts event,
    Emitter<ProductsState> emit,
  ) async {
    if (state is ProductsLoaded) {
      final currentState = state as ProductsLoaded;

      if (event.query.isEmpty) {
        emit(currentState.copyWith(
          filteredProducts: currentState.products,
          searchQuery: event.query,
          isSearching: false,
        ));
      } else {
        try {
          debugPrint('🔍 ProductsBloc: Searching products with query: ${event.query}');

          final searchResults = await _productsRepository.searchProducts(event.query);

          emit(currentState.copyWith(
            filteredProducts: searchResults,
            searchQuery: event.query,
            isSearching: true,
          ));

          debugPrint('✅ ProductsBloc: Found ${searchResults.length} products');
        } catch (e) {
          debugPrint('❌ ProductsBloc: Error searching products: $e');
          emit(ProductsError(message: e.toString()));
        }
      }
    }
  }

  Future<void> _onCreateProduct(
    CreateProduct event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      final createdProduct = await _productsRepository.createProduct(event.product);
      emit(ProductCreated(product: createdProduct));
      
      // Refresh the list
      add(LoadAllProducts());
    } catch (e) {
      emit(ProductsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateProduct(
    UpdateProduct event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      final updatedProduct = await _productsRepository.updateProduct(event.product);
      emit(ProductUpdated(product: updatedProduct));
      
      // Refresh the list
      add(LoadAllProducts());
    } catch (e) {
      emit(ProductsError(message: e.toString()));
    }
  }

  Future<void> _onDeleteProduct(
    DeleteProduct event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      await _productsRepository.deleteProduct(event.productId);
      emit(ProductDeleted(productId: event.productId));
      
      // Refresh the list
      add(LoadAllProducts());
    } catch (e) {
      emit(ProductsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateProductStock(
    UpdateProductStock event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      final updatedProduct = await _productsRepository.updateProductStock(
        event.productId,
        event.newStock,
      );
      emit(ProductStockUpdated(product: updatedProduct));
      
      // Refresh the list
      add(LoadAllProducts());
    } catch (e) {
      emit(ProductsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshProducts(
    RefreshProducts event,
    Emitter<ProductsState> emit,
  ) async {
    add(LoadAllProducts());
  }

  Future<void> _onFilterProductsByPrice(
    FilterProductsByPrice event,
    Emitter<ProductsState> emit,
  ) async {
    if (state is ProductsLoaded) {
      final currentState = state as ProductsLoaded;
      
      final filteredProducts = currentState.products.where((product) {
        final price = product.effectivePrice;
        return price >= event.minPrice && price <= event.maxPrice;
      }).toList();
      
      emit(currentState.copyWith(filteredProducts: filteredProducts));
    }
  }

  Future<void> _onCreateProductWithImages(
    CreateProductWithImages event,
    Emitter<ProductsState> emit,
  ) async {
    emit(ProductsLoading());
    try {
      debugPrint('🔄 Starting product creation with ${event.images.length} images');
      debugPrint('📦 Product data: ${event.product.name}');

      // Create product with images using the new method
      final createdProduct = await _productsRepository.createProductWithImages(event.product, event.images);
      debugPrint('✅ Product created successfully: ${createdProduct.id}');

      // Reload all products
      add(LoadAllProducts());
    } catch (e) {
      debugPrint('❌ Error creating product: $e');
      debugPrint('📍 Stack trace: ${StackTrace.current}');
      emit(ProductsError(message: e.toString()));
    }
  }

  Future<void> _onUploadProductImages(
    UploadProductImages event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      // Get current product
      final product = await _productsRepository.getProductById(event.productId);
      List<ProductImageModel> allImages = List.from(product.images);

      // Upload new images
      for (int i = 0; i < event.images.length; i++) {
        final imageUrl = await _productsRepository.uploadProductImage(
          event.productId,
          event.images[i],
          allImages.length + i, // sort order
        );

        final imagePath = 'products/${event.productId}/${event.productId}_${DateTime.now().millisecondsSinceEpoch}_${allImages.length + i}.${event.images[i].path.split('.').last}';

        allImages.add(ProductImageModel(
          id: '${event.productId}_${allImages.length + i}',
          productId: event.productId,
          imageUrl: imageUrl,
          imagePath: imagePath,
          sortOrder: allImages.length + i,
          isPrimary: allImages.isEmpty, // First image is primary
          createdAt: DateTime.now(),
        ));
      }

      // Update product with all images
      await _productsRepository.updateProduct(product.copyWith(images: allImages));

      // Reload all products
      add(LoadAllProducts());
    } catch (e) {
      emit(ProductsError(message: e.toString()));
    }
  }

  Future<void> _onDeleteProductImage(
    DeleteProductImage event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      debugPrint('🔄 Deleting product image: ${event.imageId} from product: ${event.productId}');

      await _productsRepository.deleteProductImage(event.productId, event.imageId);

      debugPrint('✅ Product image deleted successfully');

      // Reload all products to refresh the UI
      add(LoadAllProducts());
    } catch (e) {
      debugPrint('❌ Error deleting product image: $e');
      emit(ProductsError(message: e.toString()));
    }
  }
}
