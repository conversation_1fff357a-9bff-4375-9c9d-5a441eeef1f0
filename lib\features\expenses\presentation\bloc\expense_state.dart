import 'package:equatable/equatable.dart';
import '../../../../core/models/expense_model.dart';

abstract class ExpenseState extends Equatable {
  const ExpenseState();

  @override
  List<Object?> get props => [];
}

class ExpenseInitial extends ExpenseState {}

class ExpenseLoading extends ExpenseState {}

class ExpenseLoaded extends ExpenseState {
  final List<ExpenseModel> expenses;
  final bool hasReachedMax;
  final int currentPage;
  final String? searchQuery;
  final ExpenseCategory? categoryFilter;
  final DateTime? dateFilter;

  const ExpenseLoaded({
    required this.expenses,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.searchQuery,
    this.categoryFilter,
    this.dateFilter,
  });

  ExpenseLoaded copyWith({
    List<ExpenseModel>? expenses,
    bool? hasReachedMax,
    int? currentPage,
    String? searchQuery,
    ExpenseCategory? categoryFilter,
    DateTime? dateFilter,
  }) {
    return ExpenseLoaded(
      expenses: expenses ?? this.expenses,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      searchQuery: searchQuery ?? this.searchQuery,
      categoryFilter: categoryFilter ?? this.categoryFilter,
      dateFilter: dateFilter ?? this.dateFilter,
    );
  }

  @override
  List<Object?> get props => [
        expenses,
        hasReachedMax,
        currentPage,
        searchQuery,
        categoryFilter,
        dateFilter,
      ];
}

class ExpenseError extends ExpenseState {
  final String message;

  const ExpenseError(this.message);

  @override
  List<Object?> get props => [message];
}

class ExpenseCreated extends ExpenseState {
  final ExpenseModel expense;

  const ExpenseCreated(this.expense);

  @override
  List<Object?> get props => [expense];
}

class ExpenseUpdated extends ExpenseState {
  final ExpenseModel expense;

  const ExpenseUpdated(this.expense);

  @override
  List<Object?> get props => [expense];
}

class ExpenseDeleted extends ExpenseState {
  final String expenseId;

  const ExpenseDeleted(this.expenseId);

  @override
  List<Object?> get props => [expenseId];
}
