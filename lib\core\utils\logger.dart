import 'package:flutter/foundation.dart';

class Logger {
  final String _className;

  Logger(this._className);

  void info(String message) {
    if (kDebugMode) {
      print('ℹ️ [$_className] $message');
    }
  }

  void warning(String message) {
    if (kDebugMode) {
      print('⚠️ [$_className] $message');
    }
  }

  void error(String message) {
    if (kDebugMode) {
      print('❌ [$_className] $message');
    }
  }

  void debug(String message) {
    if (kDebugMode) {
      print('🐛 [$_className] $message');
    }
  }

  void success(String message) {
    if (kDebugMode) {
      print('✅ [$_className] $message');
    }
  }
}