import 'dart:developer' as developer;
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/return_model.dart';
import '../../../../core/models/return_item_model.dart';
import '../../../../core/models/sales_invoice_model.dart';

class ReturnsRepository {
  // Generate unique return number
  Future<String> generateReturnNumber() async {
    try {
      final response = await SupabaseConfig.client
          .rpc('generate_return_number');
      return response as String;
    } catch (e) {
      developer.log('Error generating return number: $e', name: 'ReturnsRepository');
      throw Exception('فشل في توليد رقم الاسترجاع: $e');
    }
  }

  // Create new return
  Future<ReturnModel> createReturn(ReturnModel returnModel, List<ReturnItemModel> items) async {
    try {
      developer.log('Creating return: ${returnModel.returnNumber}', name: 'ReturnsRepository');

      // Insert return
      final returnResponse = await SupabaseConfig.returns
          .insert(returnModel.toJson())
          .select()
          .single();

      final createdReturn = ReturnModel.fromJson(returnResponse);
      developer.log('Return created with ID: ${createdReturn.id}', name: 'ReturnsRepository');

      // Insert return items
      if (items.isNotEmpty) {
        final itemsData = items.map((item) => {
          ...item.toJson(),
          'return_id': createdReturn.id,
        }).toList();

        await SupabaseConfig.returnItems
            .insert(itemsData);

        developer.log('Inserted ${items.length} return items', name: 'ReturnsRepository');
      }

      return await getReturnById(createdReturn.id);
    } catch (e) {
      developer.log('Error creating return: $e', name: 'ReturnsRepository');
      throw Exception('فشل في إنشاء الاسترجاع: $e');
    }
  }

  // Get all returns with pagination
  Future<List<ReturnModel>> getAllReturns({
    int page = 1,
    int limit = 20,
    String? searchQuery,
    ReturnStatus? status,
  }) async {
    try {
      developer.log('Fetching returns - Page: $page, Limit: $limit', name: 'ReturnsRepository');

      var query = SupabaseConfig.returns
          .select('''
            *,
            patients(id, name, phone, patient_id),
            sales_invoices(id, invoice_number),
            return_items(
              *,
              products(id, name, product_code),
              invoice_items(*)
            )
          ''');

      // Apply filters
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('return_number.ilike.%$searchQuery%,patients.name.ilike.%$searchQuery%,sales_invoices.invoice_number.ilike.%$searchQuery%');
      }

      if (status != null) {
        query = query.eq('status', status.value);
      }

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      final returns = (response as List)
          .map((json) => ReturnModel.fromJson(json as Map<String, dynamic>))
          .toList();

      developer.log('Fetched ${returns.length} returns', name: 'ReturnsRepository');
      return returns;
    } catch (e) {
      developer.log('Error fetching returns: $e', name: 'ReturnsRepository');
      throw Exception('فشل في جلب المرتجعات: $e');
    }
  }

  // Get return by ID
  Future<ReturnModel> getReturnById(String returnId) async {
    try {
      final response = await SupabaseConfig.returns
          .select('''
            *,
            patients(id, name, phone, patient_id),
            sales_invoices(id, invoice_number),
            return_items(
              *,
              products(id, name, product_code),
              invoice_items(*)
            )
          ''')
          .eq('id', returnId)
          .single();

      return ReturnModel.fromJson(response);
    } catch (e) {
      developer.log('Error fetching return: $e', name: 'ReturnsRepository');
      throw Exception('فشل في جلب الاسترجاع: $e');
    }
  }

  // Approve return
  Future<ReturnModel> approveReturn(String returnId, String approvedBy) async {
    try {
      developer.log('Approving return: $returnId', name: 'ReturnsRepository');

      // Get return details
      final returnModel = await getReturnById(returnId);

      // Update return status
      await SupabaseConfig.returns
          .update({
            'status': ReturnStatus.approved.value,
            'approved_by': approvedBy,
            'approved_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', returnId);

      // Restore product stock
      for (final item in returnModel.items) {
        await _updateProductStock(item.productId, item.quantityReturned);
      }

      // Update invoice status if fully returned
      await _checkAndUpdateInvoiceStatus(returnModel.invoiceId);

      return await getReturnById(returnId);
    } catch (e) {
      developer.log('Error approving return: $e', name: 'ReturnsRepository');
      throw Exception('فشل في الموافقة على الاسترجاع: $e');
    }
  }

  // Complete return (process refund)
  Future<ReturnModel> completeReturn(String returnId) async {
    try {
      developer.log('Completing return: $returnId', name: 'ReturnsRepository');

      await SupabaseConfig.returns
          .update({
            'status': ReturnStatus.completed.value,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', returnId);

      return await getReturnById(returnId);
    } catch (e) {
      developer.log('Error completing return: $e', name: 'ReturnsRepository');
      throw Exception('فشل في إكمال الاسترجاع: $e');
    }
  }

  // Cancel return
  Future<ReturnModel> cancelReturn(String returnId) async {
    try {
      developer.log('Cancelling return: $returnId', name: 'ReturnsRepository');

      await SupabaseConfig.returns
          .update({
            'status': ReturnStatus.cancelled.value,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', returnId);

      return await getReturnById(returnId);
    } catch (e) {
      developer.log('Error cancelling return: $e', name: 'ReturnsRepository');
      throw Exception('فشل في إلغاء الاسترجاع: $e');
    }
  }

  // Update product stock
  Future<void> _updateProductStock(String productId, int quantityChange) async {
    try {
      final newStock = await SupabaseConfig.client.rpc('increment_stock', params: {
        'product_id': productId,
        'quantity': quantityChange,
      });

      await SupabaseConfig.products
          .update({
            'stock': newStock,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', productId);
    } catch (e) {
      developer.log('Error updating product stock: $e', name: 'ReturnsRepository');
    }
  }

  // Check and update invoice status if fully returned
  Future<void> _checkAndUpdateInvoiceStatus(String invoiceId) async {
    try {
      // Get all returns for this invoice
      final returns = await SupabaseConfig.returns
          .select('*, return_items(*)')
          .eq('invoice_id', invoiceId)
          .eq('status', ReturnStatus.approved.value);

      // Get original invoice items
      final invoiceItems = await SupabaseConfig.invoiceItems
          .select('*')
          .eq('invoice_id', invoiceId);

      // Check if all items are returned
      bool fullyReturned = true;
      for (final invoiceItem in invoiceItems) {
        final totalReturned = returns.fold<int>(0, (sum, returnData) {
          final returnItems = returnData['return_items'] as List;
          return sum + returnItems
              .where((item) => item['invoice_item_id'] == invoiceItem['id'])
              .fold<int>(0, (itemSum, item) => itemSum + (item['quantity_returned'] as int));
        });

        if (totalReturned < invoiceItem['quantity']) {
          fullyReturned = false;
          break;
        }
      }

      if (fullyReturned) {
        await SupabaseConfig.salesInvoices
            .update({
              'status': InvoiceStatus.returned.value,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', invoiceId);
      }
    } catch (e) {
      developer.log('Error checking invoice status: $e', name: 'ReturnsRepository');
    }
  }

  // Get returnable items for an invoice
  Future<List<Map<String, dynamic>>> getReturnableItems(String invoiceId) async {
    try {
      final response = await SupabaseConfig.invoiceItems
          .select('''
            *,
            products(id, name, product_code)
          ''')
          .eq('invoice_id', invoiceId);

      // Get already returned quantities
      // First get return IDs for this invoice
      final returnIds = await SupabaseConfig.returns
          .select('id')
          .eq('invoice_id', invoiceId)
          .neq('status', ReturnStatus.cancelled.value);

      final returnIdsList = returnIds.map((r) => r['id']).toList();

      // Then get return items for these returns
      final returns = returnIdsList.isEmpty
          ? <Map<String, dynamic>>[]
          : await SupabaseConfig.returnItems
              .select('invoice_item_id, quantity_returned')
              .inFilter('return_id', returnIdsList);

      final returnedQuantities = <String, int>{};
      for (final returnItem in returns) {
        final itemId = returnItem['invoice_item_id'] as String;
        returnedQuantities[itemId] = (returnedQuantities[itemId] ?? 0) + 
            (returnItem['quantity_returned'] as int);
      }

      // Filter items that can still be returned
      final returnableItems = <Map<String, dynamic>>[];
      for (final item in response) {
        final itemId = item['id'] as String;
        final originalQuantity = item['quantity'] as int;
        final returnedQuantity = returnedQuantities[itemId] ?? 0;
        final availableQuantity = originalQuantity - returnedQuantity;

        if (availableQuantity > 0) {
          returnableItems.add({
            ...item,
            'available_quantity': availableQuantity,
            'returned_quantity': returnedQuantity,
          });
        }
      }

      return returnableItems;
    } catch (e) {
      developer.log('Error fetching returnable items: $e', name: 'ReturnsRepository');
      throw Exception('فشل في جلب العناصر القابلة للاسترجاع: $e');
    }
  }
}
