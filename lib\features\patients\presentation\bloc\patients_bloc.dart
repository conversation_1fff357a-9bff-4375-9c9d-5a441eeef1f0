import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/patients_repository.dart';
import 'patients_event.dart';
import 'patients_state.dart';

class PatientsBloc extends Bloc<PatientsEvent, PatientsState> {
  final PatientsRepository _patientsRepository;

  PatientsBloc({required PatientsRepository patientsRepository})
      : _patientsRepository = patientsRepository,
        super(PatientsInitial()) {
    on<LoadAllPatients>(_onLoadAllPatients);
    on<LoadPremiumPatients>(_onLoadPremiumPatients);
    on<SearchPatients>(_onSearchPatients);
    on<CreatePatient>(_onCreatePatient);
    on<UpdatePatient>(_onUpdatePatient);
    on<DeletePatient>(_onDeletePatient);
    on<UpgradeToPremium>(_onUpgradeToPremium);
    on<DowngradeFromPremium>(_onDowngradeFromPremium);
    on<RefreshPatients>(_onRefreshPatients);
  }

  Future<void> _onLoadAllPatients(
    LoadAllPatients event,
    Emitter<PatientsState> emit,
  ) async {
    emit(PatientsLoading());
    try {
      final allPatients = await _patientsRepository.getAllPatients();
      final premiumPatients = await _patientsRepository.getPremiumPatients();

      emit(PatientsLoaded(
        patients: allPatients,
        premiumPatients: premiumPatients,
      ));
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onLoadPremiumPatients(
    LoadPremiumPatients event,
    Emitter<PatientsState> emit,
  ) async {
    emit(PatientsLoading());
    try {
      final premiumPatients = await _patientsRepository.getPremiumPatients();
      final allPatients = await _patientsRepository.getAllPatients();

      emit(PatientsLoaded(
        patients: allPatients,
        premiumPatients: premiumPatients,
      ));
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onSearchPatients(
    SearchPatients event,
    Emitter<PatientsState> emit,
  ) async {
    final currentState = state;
    if (currentState is! PatientsLoaded) return;

    if (event.query.isEmpty) {
      // إعادة تعيين البحث وعرض جميع المرضى
      emit(currentState.copyWith(
        patients: currentState.patients,
        premiumPatients: currentState.premiumPatients,
        searchQuery: event.query,
        isSearching: false,
      ));
      add(LoadAllPatients());
      return;
    }

    try {
      debugPrint('🔍 PatientsBloc: Searching patients with query: ${event.query}');

      final searchResults = await _patientsRepository.searchPatients(event.query);
      final premiumPatients = searchResults.where((p) => p.isPremium).toList();

      emit(currentState.copyWith(
        patients: searchResults,
        premiumPatients: premiumPatients,
        searchQuery: event.query,
        isSearching: true,
      ));

      debugPrint('✅ PatientsBloc: Found ${searchResults.length} patients');
    } catch (e) {
      debugPrint('❌ PatientsBloc: Error searching patients: $e');
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onCreatePatient(
    CreatePatient event,
    Emitter<PatientsState> emit,
  ) async {
    try {
      final createdPatient = await _patientsRepository.createPatient(event.patient);
      emit(PatientCreated(patient: createdPatient));

      // Refresh the list
      add(LoadAllPatients());
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onUpdatePatient(
    UpdatePatient event,
    Emitter<PatientsState> emit,
  ) async {
    try {
      final updatedPatient = await _patientsRepository.updatePatient(event.patient);
      emit(PatientUpdated(patient: updatedPatient));

      // Refresh the list
      add(LoadAllPatients());
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onDeletePatient(
    DeletePatient event,
    Emitter<PatientsState> emit,
  ) async {
    try {
      await _patientsRepository.deletePatient(event.patientId);
      emit(PatientDeleted(patientId: event.patientId));

      // Refresh the list
      add(LoadAllPatients());
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onUpgradeToPremium(
    UpgradeToPremium event,
    Emitter<PatientsState> emit,
  ) async {
    try {
      final upgradedPatient = await _patientsRepository.upgradeToPremium(event.patientId);
      emit(PatientUpgraded(patient: upgradedPatient));

      // Refresh the list
      add(LoadAllPatients());
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onDowngradeFromPremium(
    DowngradeFromPremium event,
    Emitter<PatientsState> emit,
  ) async {
    try {
      final downgradedPatient = await _patientsRepository.downgradeFromPremium(event.patientId);
      emit(PatientDowngraded(patient: downgradedPatient));

      // Refresh the list
      add(LoadAllPatients());
    } catch (e) {
      emit(PatientsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshPatients(
    RefreshPatients event,
    Emitter<PatientsState> emit,
  ) async {
    add(LoadAllPatients());
  }
}
