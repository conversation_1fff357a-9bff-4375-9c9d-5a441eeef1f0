import 'package:equatable/equatable.dart';

class LabTestModel extends Equatable {
  final String id;
  final String patientId;
  final String testName;
  final DateTime testDate;
  final String? testType;
  final String? imageUrl;
  final String? results;
  final String? doctorNotes;
  final bool? isNormal;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LabTestModel({
    required this.id,
    required this.patientId,
    required this.testName,
    required this.testDate,
    this.testType,
    this.imageUrl,
    this.results,
    this.doctorNotes,
    this.isNormal,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LabTestModel.fromJson(Map<String, dynamic> json) {
    return LabTestModel(
      id: json['id'] as String,
      patientId: json['patient_id'] as String,
      testName: json['test_name'] as String,
      testDate: DateTime.parse(json['test_date'] as String),
      testType: json['test_type'] as String?,
      imageUrl: json['image_url'] as String?,
      results: json['results'] as String?,
      doctorNotes: json['doctor_notes'] as String?,
      isNormal: json['is_normal'] as bool?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'patient_id': patientId,
      'test_name': testName,
      'test_date': testDate.toIso8601String().split('T')[0], // Date only
      'test_type': testType,
      'image_url': imageUrl,
      'results': results,
      'doctor_notes': doctorNotes,
      'is_normal': isNormal,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include ID if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  LabTestModel copyWith({
    String? id,
    String? patientId,
    String? testName,
    DateTime? testDate,
    String? testType,
    String? imageUrl,
    String? results,
    String? doctorNotes,
    bool? isNormal,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LabTestModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      testName: testName ?? this.testName,
      testDate: testDate ?? this.testDate,
      testType: testType ?? this.testType,
      imageUrl: imageUrl ?? this.imageUrl,
      results: results ?? this.results,
      doctorNotes: doctorNotes ?? this.doctorNotes,
      isNormal: isNormal ?? this.isNormal,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get testTypeDisplayName {
    switch (testType?.toLowerCase()) {
      case 'blood':
        return 'فحص دم';
      case 'urine':
        return 'فحص بول';
      case 'x_ray':
        return 'أشعة سينية';
      case 'mri':
        return 'رنين مغناطيسي';
      case 'ct_scan':
        return 'أشعة مقطعية';
      case 'ultrasound':
        return 'موجات فوق صوتية';
      default:
        return testType ?? 'فحص عام';
    }
  }

  String get normalityStatus {
    if (isNormal == null) return 'غير محدد';
    return isNormal! ? 'طبيعي' : 'غير طبيعي';
  }

  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasResults => results != null && results!.isNotEmpty;
  bool get hasDoctorNotes => doctorNotes != null && doctorNotes!.isNotEmpty;

  @override
  List<Object?> get props => [
        id,
        patientId,
        testName,
        testDate,
        testType,
        imageUrl,
        results,
        doctorNotes,
        isNormal,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'LabTestModel(id: $id, patientId: $patientId, testName: $testName, '
        'testDate: $testDate, testType: $testType, hasImage: $hasImage, '
        'isNormal: $isNormal)';
  }
}
