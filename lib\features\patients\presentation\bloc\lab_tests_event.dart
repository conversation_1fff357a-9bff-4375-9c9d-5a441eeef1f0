import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/models/lab_test_model.dart';

abstract class LabTestsEvent extends Equatable {
  const LabTestsEvent();

  @override
  List<Object?> get props => [];
}

class LoadLabTestsByPatientId extends LabTestsEvent {
  final String patientId;

  const LoadLabTestsByPatientId({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadLabTestsByType extends LabTestsEvent {
  final String patientId;
  final String testType;

  const LoadLabTestsByType({
    required this.patientId,
    required this.testType,
  });

  @override
  List<Object?> get props => [patientId, testType];
}

class LoadLabTestsByDateRange extends LabTestsEvent {
  final String patientId;
  final DateTime startDate;
  final DateTime endDate;

  const LoadLabTestsByDateRange({
    required this.patientId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [patientId, startDate, endDate];
}

class AddLabTest extends LabTestsEvent {
  final LabTestModel labTest;

  const AddLabTest({required this.labTest});

  @override
  List<Object?> get props => [labTest];
}

class UpdateLabTest extends LabTestsEvent {
  final LabTestModel labTest;

  const UpdateLabTest({required this.labTest});

  @override
  List<Object?> get props => [labTest];
}

class DeleteLabTest extends LabTestsEvent {
  final String labTestId;
  final String patientId;

  const DeleteLabTest({
    required this.labTestId,
    required this.patientId,
  });

  @override
  List<Object?> get props => [labTestId, patientId];
}

class UploadLabTestImage extends LabTestsEvent {
  final XFile imageFile;
  final String patientId;

  const UploadLabTestImage({
    required this.imageFile,
    required this.patientId,
  });

  @override
  List<Object?> get props => [imageFile, patientId];
}

class DeleteLabTestImage extends LabTestsEvent {
  final String imageUrl;

  const DeleteLabTestImage({required this.imageUrl});

  @override
  List<Object?> get props => [imageUrl];
}

class SearchLabTests extends LabTestsEvent {
  final String patientId;
  final String query;

  const SearchLabTests({
    required this.patientId,
    required this.query,
  });

  @override
  List<Object?> get props => [patientId, query];
}

class RefreshLabTests extends LabTestsEvent {
  final String patientId;

  const RefreshLabTests({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}
