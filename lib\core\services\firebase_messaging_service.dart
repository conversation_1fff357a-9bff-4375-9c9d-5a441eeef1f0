import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../network/supabase_client.dart';

class FirebaseMessagingService {
  static final FirebaseMessagingService _instance =
      FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final Logger _logger = Logger('FirebaseMessagingService');

  String? _currentToken;
  String? _currentUserId;

  // Initialize Firebase Messaging
  Future<void> initialize() async {
    try {
      _logger.info('🔔 Initializing Firebase Messaging...');

      // Request permission for notifications
      await _requestPermission();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Setup message handlers first
      _setupMessageHandlers();

      // Get FCM token after handlers are set up
      await _getAndSaveToken();

      _logger.info('✅ Firebase Messaging initialized successfully');
    } catch (e) {
      _logger.error('❌ Failed to initialize Firebase Messaging: $e');
      rethrow;
    }
  }

  // Request notification permissions with support for all Android/iOS versions
  Future<void> _requestPermission() async {
    try {
      // For iOS 10+ and Android 13+
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: true,
        criticalAlert: true,
        provisional: false,
        sound: true,
      );

      _logger.info(
        '🔐 Notification permission status: ${settings.authorizationStatus}',
      );

      // Additional permission request for Android 13+ (API level 33+)
      if (Platform.isAndroid) {
        await _requestAndroidNotificationPermission();
      }

      // Additional permission request for iOS 15+
      if (Platform.isIOS) {
        await _requestIOSNotificationPermission();
      }
    } catch (e) {
      _logger.error('❌ Error requesting notification permissions: $e');
    }
  }

  // Request Android-specific notification permissions
  Future<void> _requestAndroidNotificationPermission() async {
    try {
      // For Android 13+ (API level 33+), we need to request POST_NOTIFICATIONS permission
      // This is handled automatically by the firebase_messaging plugin
      _logger.info('📱 Android notification permissions requested');
    } catch (e) {
      _logger.error('❌ Error requesting Android notification permission: $e');
    }
  }

  // Request iOS-specific notification permissions
  Future<void> _requestIOSNotificationPermission() async {
    try {
      // Request additional iOS permissions
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: true,
        criticalAlert: true,
        provisional: true,
        sound: true,
      );

      _logger.info('🍎 iOS notification permissions: ${settings.authorizationStatus}');
      
      // Check if we have provisional authorization (iOS 12+)
      if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        _logger.info('📱 iOS provisional authorization granted');
      }
    } catch (e) {
      _logger.error('❌ Error requesting iOS notification permission: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  // Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    const appointmentChannel = AndroidNotificationChannel(
      'appointments',
      'إشعارات الحجوزات',
      description: 'إشعارات متعلقة بالحجوزات الطبية',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    const tasksChannel = AndroidNotificationChannel(
      'tasks',
      'إشعارات المهام',
      description: 'إشعارات متعلقة بالمهام والتكليفات',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    const generalChannel = AndroidNotificationChannel(
      'general',
      'إشعارات عامة',
      description: 'إشعارات عامة من التطبيق',
      importance: Importance.defaultImportance,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(appointmentChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(tasksChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(generalChannel);
  }

  // Get and save FCM token
  Future<void> _getAndSaveToken() async {
    try {
      _logger.info('🔄 Getting FCM token...');
      
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        _currentToken = token;
        _logger.info('📱 FCM Token obtained: ${token.substring(0, 20)}...');

        // Save token to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', token);
        _logger.info('💾 FCM Token saved to local storage');

        // Save token to Supabase if user is logged in
        await _saveTokenToSupabase(token);
      } else {
        _logger.warning('⚠️ FCM Token is null');
        
        // Try to get token again after a delay
        await Future.delayed(const Duration(seconds: 2));
        final retryToken = await _firebaseMessaging.getToken();
        if (retryToken != null) {
          _currentToken = retryToken;
          _logger.info('📱 FCM Token obtained on retry: ${retryToken.substring(0, 20)}...');
          
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('fcm_token', retryToken);
          await _saveTokenToSupabase(retryToken);
        } else {
          _logger.error('❌ Failed to get FCM token even on retry');
        }
      }
    } catch (e) {
      _logger.error('❌ Failed to get FCM token: $e');
    }
  }

  // Save token to Supabase
  Future<void> _saveTokenToSupabase(String token) async {
    try {
      final userId = await _getCurrentUserId();
      if (userId == null) {
        _logger.warning('⚠️ No user ID found, skipping token save');
        return;
      }

      final deviceInfo = {
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
        'app_version': '1.0.0', // You can get this from package_info
      };

      await SupabaseConfig.client.from('user_fcm_tokens').upsert({
        'user_id': userId,
        'fcm_token': token,
        'device_info': deviceInfo,
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      }, onConflict: 'fcm_token');

      _logger.info('✅ Token saved to Supabase for user: $userId');
    } catch (e) {
      _logger.error('❌ Failed to save token to Supabase: $e');
    }
  }

  // Get current user ID
  Future<String?> _getCurrentUserId() async {
    try {
      final user = SupabaseConfig.client.auth.currentUser;
      return user?.id;
    } catch (e) {
      _logger.error('❌ Failed to get current user ID: $e');
      return null;
    }
  }

  // Setup message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    _handleInitialMessage();

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen((token) {
      _currentToken = token;
      _saveTokenToSupabase(token);
    });
  }

  // Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    _logger.info('📨 Foreground message received: ${message.messageId}');

    // Show local notification when app is in foreground
    await _showLocalNotification(message);
  }

  // Handle notification tap
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    _logger.info('👆 Notification tapped: ${message.messageId}');
    await _processNotificationAction(message);
  }

  // Handle initial message (when app is opened from terminated state)
  Future<void> _handleInitialMessage() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _logger.info('🚀 Initial message: ${initialMessage.messageId}');
      await _processNotificationAction(initialMessage);
    }
  }

  // Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      final notificationType = message.data['type'] ?? 'general';
      final channelId = _getChannelId(notificationType);

      final androidDetails = AndroidNotificationDetails(
        channelId,
        'إشعارات عامة',
        channelDescription: 'إشعارات عامة من التطبيق',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        notification.title,
        notification.body,
        details,
        payload: jsonEncode(message.data),
      );
    } catch (e) {
      _logger.error('❌ Failed to show local notification: $e');
    }
  }

  // Get channel ID based on notification type
  String _getChannelId(String type) {
    switch (type) {
      case 'appointment':
        return 'appointments';
      case 'task':
        return 'tasks';
      default:
        return 'general';
    }
  }

  // Handle notification tap from local notifications
  void _onNotificationTapped(NotificationResponse response) {
    try {
      if (response.payload != null) {
        final data = jsonDecode(response.payload!);
        final message = RemoteMessage(
          messageId: DateTime.now().millisecondsSinceEpoch.toString(),
          data: Map<String, String>.from(data),
        );
        _processNotificationAction(message);
      }
    } catch (e) {
      _logger.error('❌ Failed to handle notification tap: $e');
    }
  }

  // Process notification action
  Future<void> _processNotificationAction(RemoteMessage message) async {
    final type = message.data['type'];
    final id = message.data['id'];

    _logger.info('🎯 Processing notification action - Type: $type, ID: $id');

    switch (type) {
      case 'appointment':
        await _handleAppointmentNotification(id);
        break;
      case 'task':
        await _handleTaskNotification(id);
        break;
      default:
        _logger.info('ℹ️ Unknown notification type: $type');
    }
  }

  // Handle appointment notification
  Future<void> _handleAppointmentNotification(String? appointmentId) async {
    if (appointmentId == null) return;

    // Navigate to appointment details
    // You can implement navigation logic here
    _logger.info('📅 Navigating to appointment: $appointmentId');
  }

  // Handle task notification
  Future<void> _handleTaskNotification(String? taskId) async {
    if (taskId == null) return;

    // Navigate to task details
    // You can implement navigation logic here
    _logger.info('📋 Navigating to task: $taskId');
  }

  // Update user ID when user logs in
  Future<void> updateUserId(String userId) async {
    _currentUserId = userId;
    if (_currentToken != null) {
      await _saveTokenToSupabase(_currentToken!);
    }
  }

  // Clear token when user logs out
  Future<void> clearToken() async {
    try {
      if (_currentToken != null && _currentUserId != null) {
        // Mark token as inactive in Supabase
        await SupabaseConfig.client
            .from('user_fcm_tokens')
            .update({'is_active': false})
            .eq('fcm_token', _currentToken!);
      }

      _currentToken = null;
      _currentUserId = null;

      // Clear from local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('fcm_token');

      _logger.info('🗑️ Token cleared successfully');
    } catch (e) {
      _logger.error('❌ Failed to clear token: $e');
    }
  }

  // Get current token
  String? get currentToken => _currentToken;

  // Send personalized notification
  static Future<bool> sendPersonalizedNotification({
    required String fcmToken,
    required String patientName,
    required String notificationType,
    required String content,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Create personalized title based on notification type
      String title;
      switch (notificationType) {
        case 'meal':
          title = '🍽️ تذكير الوجبة';
          break;
        case 'water':
          title = '💧 تذكير شرب الماء';
          break;
        case 'exercise':
          title = '🏃‍♂️ تذكير التمرين';
          break;
        case 'medication':
          title = '💊 تذكير الدواء';
          break;
        case 'appointment':
          title = '📅 تذكير الموعد';
          break;
        case 'welcome':
          title = '🎉 مرحباً بك';
          break;
        case 'test':
          title = '🧪 إشعار تجريبي';
          break;
        default:
          title = '🔔 إشعار';
      }

      // Personalize the content with patient name
      final personalizedContent =
          content.contains(patientName) ? content : '$content، $patientName';

      // Prepare data payload
      final data = <String, String>{
        'type': notificationType,
        'patient_name': patientName,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData?.map(
          (key, value) => MapEntry(key, value.toString()),
        ),
      };

      // Use the existing sendNotification method if available, or implement FCM sending
      return await _sendFCMNotification(
        fcmToken: fcmToken,
        title: title,
        body: personalizedContent,
        data: data,
      );
    } catch (e) {
      final logger = Logger('FirebaseMessagingService');
      logger.error('❌ Failed to send personalized notification: $e');
      return false;
    }
  }

  // Internal method to send FCM notification
  static Future<bool> _sendFCMNotification({
    required String fcmToken,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      // This is a simplified implementation
      // In a real app, you would use Firebase Admin SDK or HTTP API
      final logger = Logger('FirebaseMessagingService');
      logger.info('📤 Sending notification to: $fcmToken');
      logger.info('📝 Title: $title');
      logger.info('📝 Body: $body');

      // For now, return true as if sent successfully
      // You can implement actual FCM sending here using HTTP API
      return true;
    } catch (e) {
      final logger = Logger('FirebaseMessagingService');
      logger.error('❌ Failed to send FCM notification: $e');
      return false;
    }
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  final logger = Logger('BackgroundMessageHandler');
  logger.info('📨 Background message received: ${message.messageId}');

  // You can process the message here if needed
  // Note: UI operations are not allowed in background handlers
}
