import 'package:flutter/material.dart';

enum TreatmentType {
  speech('نطق', 'speech', Color(0xFF4CAF50), Icons.record_voice_over),
  behavior('سلوك', 'behavior', Color(0xFF2196F3), Icons.psychology),
  hearing('سمع', 'hearing', Color(0xFFFF9800), Icons.hearing),
  autism('توحد', 'autism', Color(0xFF9C27B0), Icons.accessibility_new),
  adhd('فرط الحركة', 'adhd', Color(0xFFE91E63), Icons.flash_on),
  learning('صعوبات التعلم', 'learning', Color(0xFF607D8B), Icons.school),
  cognitive('إدراكي', 'cognitive', Color(0xFF795548), Icons.lightbulb),
  social('تفاعل اجتماعي', 'social', Color(0xFF009688), Icons.people),
  motor('حركي', 'motor', Color(0xFFFF5722), Icons.directions_run),
  sensory('حسي', 'sensory', Color(0xFF3F51B5), Icons.touch_app),
  developmental('تأخر نمائي', 'developmental', Color(0xFF8BC34A), Icons.child_care),
  communication('تواصل', 'communication', Color(0xFFFF6F00), Icons.chat);

  const TreatmentType(this.arabicName, this.englishName, this.color, this.icon);

  final String arabicName;
  final String englishName;
  final Color color;
  final IconData icon;

  // Getter for display name (returns Arabic name)
  String get displayName => arabicName;

  static TreatmentType? fromString(String value) {
    for (TreatmentType type in TreatmentType.values) {
      if (type.englishName == value) {
        return type;
      }
    }
    return null;
  }

  static List<TreatmentType> fromStringList(List<String> values) {
    return values
        .map((value) => TreatmentType.fromString(value))
        .where((type) => type != null)
        .cast<TreatmentType>()
        .toList();
  }

  static List<String> toStringList(List<TreatmentType> types) {
    return types.map((type) => type.englishName).toList();
  }
}

// Helper class for treatment type selection
class TreatmentTypeHelper {
  static Widget buildChip(TreatmentType type, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? type.color : type.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: type.color,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              type.icon,
              size: 16,
              color: isSelected ? Colors.white : type.color,
            ),
            const SizedBox(width: 6),
            Text(
              type.arabicName,
              style: TextStyle(
                color: isSelected ? Colors.white : type.color,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget buildSelectionGrid(
    List<TreatmentType> selectedTypes,
    Function(TreatmentType) onToggle,
  ) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: TreatmentType.values.map((type) {
        final isSelected = selectedTypes.contains(type);
        return buildChip(type, isSelected, () => onToggle(type));
      }).toList(),
    );
  }

  static Widget buildDisplayChips(List<TreatmentType> types) {
    if (types.isEmpty) {
      return const Text(
        'لم يتم تحديد نوع العلاج',
        style: TextStyle(
          color: Colors.grey,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: types.map((type) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: type.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: type.color.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(type.icon, size: 12, color: type.color),
              const SizedBox(width: 4),
              Text(
                type.arabicName,
                style: TextStyle(
                  fontSize: 10,
                  color: type.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
