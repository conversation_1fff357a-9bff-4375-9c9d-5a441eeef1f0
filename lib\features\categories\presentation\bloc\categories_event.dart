import 'package:equatable/equatable.dart';
import '../../../../core/models/category_model.dart';

abstract class CategoriesEvent extends Equatable {
  const CategoriesEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllCategories extends CategoriesEvent {}

class LoadActiveCategories extends CategoriesEvent {}

class SearchCategories extends CategoriesEvent {
  final String query;

  const SearchCategories({required this.query});

  @override
  List<Object?> get props => [query];
}

class CreateCategory extends CategoriesEvent {
  final CategoryModel category;

  const CreateCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class UpdateCategory extends CategoriesEvent {
  final CategoryModel category;

  const UpdateCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class DeleteCategory extends CategoriesEvent {
  final String categoryId;

  const DeleteCategory({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class ToggleCategoryStatus extends CategoriesEvent {
  final String categoryId;

  const ToggleCategoryStatus({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class GetCategoryProductsCount extends CategoriesEvent {
  final String categoryId;

  const GetCategoryProductsCount({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}
