import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:typed_data';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/utils/time_utils.dart';
import '../../../employees/data/repositories/employees_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';

class PrintMultipleAppointmentsInvoicePage extends StatefulWidget {
  final List<AppointmentModel> appointments;
  final PatientModel patient;
  final VoidCallback? onPrintComplete;

  const PrintMultipleAppointmentsInvoicePage({
    super.key,
    required this.appointments,
    required this.patient,
    this.onPrintComplete,
  });

  @override
  State<PrintMultipleAppointmentsInvoicePage> createState() => _PrintMultipleAppointmentsInvoicePageState();
}

class _PrintMultipleAppointmentsInvoicePageState extends State<PrintMultipleAppointmentsInvoicePage> {
  ReceiptController? controller;
  PaperSize _paperSize = PaperSize.mm80;
  Map<String, EmployeeModel> _employees = {};
  Map<String, TimeSlotModel> _timeSlots = {};
  bool _isLoadingData = false;

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
    _loadAllData();
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
    setState(() {
      _paperSize = PaperSize.values[paperSizeIndex];
    });
  }

  Future<void> _loadAllData() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      // Load employees
      final employees = await EmployeesRepository.getAllEmployees();
      final employeesMap = <String, EmployeeModel>{};

      for (final employee in employees) {
        employeesMap[employee.id] = employee;
      }

      // Load time slots for each appointment
      final timeSlotsMap = <String, TimeSlotModel>{};
      final timeSlotsRepo = TimeSlotsRepository();

      for (final appointment in widget.appointments) {
        if (appointment.timeSlotId != null) {
          try {
            final timeSlot = await timeSlotsRepo.getTimeSlotById(appointment.timeSlotId!);
            if (timeSlot != null) {
              timeSlotsMap[appointment.timeSlotId!] = timeSlot;
            }
          } catch (e) {
            debugPrint('❌ Error loading time slot ${appointment.timeSlotId}: $e');
          }
        }
      }

      if (mounted) {
        setState(() {
          _employees = employeesMap;
          _timeSlots = timeSlotsMap;
          _isLoadingData = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingData = false;
        });
      }
      debugPrint('❌ Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'طباعة فاتورة الحجز المتعدد',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _printInvoice,
            icon: Icon(Icons.print),
            tooltip: 'طباعة',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Preview Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    'معاينة فاتورة الحجز المتعدد',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  _isLoadingData
                      ? SizedBox(
                          height: 200.h,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                ),
                                SizedBox(height: 16.h),
                                Text(
                                  'جاري تحميل بيانات الأخصائيين...',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : Receipt(
                          builder: (context) => _buildReceiptContent(),
                          onInitialized: (controller) {
                            controller.paperSize = _paperSize;
                            this.controller = controller;
                          },
                        ),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Print Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _printInvoice,
                icon: Icon(Icons.print, color: Colors.white),
                label: Text(
                  'طباعة فاتورة الحجز المتعدد',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptContent() {
    final totalFee = widget.appointments.fold(0.0, (sum, apt) => sum + apt.consultationFee);
    final totalPaid = widget.appointments.fold(0.0, (sum, apt) => sum + apt.paidAmount);
    final totalRemaining = widget.appointments.fold(0.0, (sum, apt) => sum + apt.remainingAmount);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header
        Text(
          'مركز مستشفى إربد الإسلامي',
          style: TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'للسمع والنطق والسلوك',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),
        Text(
          'العنوان: إربد - الأردن',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        
        Divider(thickness: 2),
        
        // Invoice Type
        Text(
          'فاتورة حجز متعدد',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: 16),
        Divider(),
        
        // Booking Info
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('عدد الجلسات:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.appointments.length} جلسة', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('تاريخ الحجز:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.appointments.first.createdAt.day}/${widget.appointments.first.createdAt.month}/${widget.appointments.first.createdAt.year}', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
          ],
        ),
        
        Divider(),
        
        // Patient Info
        Text(
          'بيانات المريض',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الاسم:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
            Expanded(
              child: Text(
                widget.patient.name,
                style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الهاتف:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
            Text(widget.patient.phone ?? 'غير محدد', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('رقم المريض:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
            Text(widget.patient.patientId ?? 'غير محدد', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('نوع العلاج:', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
            Expanded(
              child: Text(
                widget.patient.treatmentTypes.isNotEmpty
                    ? widget.patient.treatmentTypes.map((t) => t.displayName).join(', ')
                    : 'غير محدد',
                style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        
        Divider(),
        
        // Appointments Details
        Text(
          'تفاصيل الجلسات',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),
        
        // Appointments List
        ...widget.appointments.asMap().entries.map((entry) {
          final index = entry.key;
          final appointment = entry.value;
          final employee = appointment.employeeId != null ? _employees[appointment.employeeId] : null;
          final timeSlot = appointment.timeSlotId != null ? _timeSlots[appointment.timeSlotId] : null;

          return Column(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                margin: EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الجلسة ${index + 1}',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24, color: Colors.black),
                    ),
                    SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('التاريخ:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                        Text('${appointment.appointmentDate.day}/${appointment.appointmentDate.month}/${appointment.appointmentDate.year}', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('اليوم:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                        Text(TimeUtils.getArabicDayName(appointment.appointmentDate.weekday), style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('وقت الجلسة:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                        Text(
                          timeSlot != null
                              ? TimeUtils.formatTimeRange(timeSlot.startTime, timeSlot.endTime)
                              : 'غير محدد',
                          style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('الأخصائي:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                        Expanded(
                          child: Text(
                            employee?.name != null ? 'د. ${employee!.name}' : 'غير محدد',
                            style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                            textAlign: TextAlign.end,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('التخصص:', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                        Expanded(
                          child: Text(
                            employee?.specialization?.name ?? 'غير محدد',
                            style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                            textAlign: TextAlign.end,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (index < widget.appointments.length - 1) SizedBox(height: 8),
            ],
          );
        }),

        SizedBox(height: 16),
        Divider(thickness: 3),

        // Financial Summary
        Text(
          'الملخص المالي',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('إجمالي الرسوم:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28)),
            Text('${totalFee.toStringAsFixed(2)} د.ا', style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المبلغ المدفوع:', style: TextStyle(fontSize: 26, color: Colors.black, fontWeight: FontWeight.bold)),
            Text('${totalPaid.toStringAsFixed(2)} د.ا', style: TextStyle(fontSize: 26, color: Colors.black, fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المبلغ المتبقي:', style: TextStyle(color: Colors.black, fontSize: 26, fontWeight: FontWeight.bold)),
            Text('${totalRemaining.toStringAsFixed(2)} د.ا', style: TextStyle(color: Colors.black, fontSize: 26, fontWeight: FontWeight.bold)),
          ],
        ),

        SizedBox(height: 32),

        // Footer
        Text(
          "شكرا لزيارتكم",
          style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),

        // مساحة إضافية في نهاية الفاتورة
        SizedBox(height: 40),
      ],
    );
  }

  Future<void> _printInvoice() async {
    // Get printer address from settings
    final prefs = await SharedPreferences.getInstance();
    final printerAddress = prefs.getString('printer_address');

    if (printerAddress == null) {
      _showError('لم يتم تكوين الطابعة. يرجى الذهاب إلى إعدادات الطابعة أولاً');
      return;
    }

    if (controller == null) {
      _showError('خطأ في إعداد الطابعة');
      return;
    }

    // عرض ديالوج جاري الطباعة
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              SizedBox(height: 16.h),
              Text(
                'جاري الطباعة...',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    try {
      // Print the receipt
      await controller!.print(
        address: printerAddress,
        keepConnected: true,
        addFeeds: 4, // إضافة مساحة إضافية في نهاية الطباعة
      );

      // إرسال أمر قطع الورق (GS V 65 0) لقطع الورق كامل
      await FlutterBluetoothPrinter.printBytes(
        address: printerAddress,
        data: Uint8List.fromList([29, 86, 65, 0]), // أمر قطع الورق (GS V 65 0)
        keepConnected: true,
      );

      // إغلاق ديالوج الطباعة
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showSuccess('تم طباعة فاتورة الحجز المتعدد بنجاح');

    } catch (e) {
      // إغلاق ديالوج الطباعة في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
      }
      _showError('فشل في طباعة فاتورة الحجز المتعدد: $e');
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}