import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import 'create_invoice_page.dart';
import 'invoices_list_page.dart';
import '../../../expenses/presentation/pages/expenses_page.dart';

class SalesMainPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const SalesMainPage({
    super.key,
    required this.isVisible,
    required this.hasBeenVisited,
  });

  @override
  State<SalesMainPage> createState() => _SalesMainPageState();
}

class _SalesMainPageState extends State<SalesMainPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible && !widget.hasBeenVisited) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام المبيعات'),
        backgroundColor: AppColors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(
              icon: Icon(Icons.add_shopping_cart),
              text: 'إنشاء فاتورة',
            ),
            Tab(
              icon: Icon(Icons.receipt_long),
              text: 'الفواتير',
            ),
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'النفقات والخدمات',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Create Invoice Tab
          CreateInvoicePage(
            isVisible: widget.isVisible,
            hasBeenVisited: widget.hasBeenVisited,
          ),
          
          // Invoices List Tab
          InvoicesListPage(
            isVisible: widget.isVisible,
            hasBeenVisited: widget.hasBeenVisited,
          ),

          // Expenses Tab
          ExpensesPage(
            isVisible: widget.isVisible,
            hasBeenVisited: widget.hasBeenVisited,
          ),
        ],
      ),
    );
  }
}