class AppStrings {
  // App
  static const String appName = 'مركز مستشفى إربد الإسلامي';
  static const String appDescription = 'للسمع والنطق والسلوك';
  
  // Auth
  static const String login = 'تسجيل الدخول';
  static const String register = 'إنشاء حساب جديد';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String name = 'الاسم';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  static const String loginSuccess = 'تم تسجيل الدخول بنجاح';
  static const String loginError = 'خطأ في تسجيل الدخول';
  static const String logout = 'تسجيل الخروج';
  
  // Navigation
  static const String appointments = 'الحجوزات';
  static const String patients = 'المرضى';
  static const String products = 'المنتجات';

  static const String profile = 'الملف الشخصي';
  
  // Appointments
  static const String todayAppointments = 'مواعيد اليوم';
  static const String createAppointment = 'إنشاء موعد';
  static const String appointmentDetails = 'تفاصيل الموعد';
  static const String appointmentTime = 'وقت الموعد';
  static const String appointmentDate = 'تاريخ الموعد';
  static const String appointmentStatus = 'حالة الموعد';
  static const String scheduled = 'مجدول';
  static const String completed = 'مكتمل';
  static const String cancelled = 'ملغي';
  
  // Patients
  static const String premiumPatients = 'المرضى المميزون';
  static const String allPatients = 'جميع المرضى';
  static const String patientProfile = 'ملف المريض';
  static const String basicInfo = 'البيانات الأساسية';
  static const String labTests = 'الفحوصات المخبرية';
  static const String medicalInfo = 'المعلومات الطبية';
  static const String appointmentManagement = 'إدارة المواعيد';
  static const String weeklyResults = 'النتائج الأسبوعية';
  static const String weight = 'الوزن';
  static const String height = 'الطول';
  static const String age = 'العمر';
  static const String gender = 'الجنس';
  static const String male = 'ذكر';
  static const String female = 'أنثى';
  static const String bodyFat = 'نسبة الدهون';
  static const String visceralFat = 'الدهون الحشوية';
  static const String waterPercentage = 'نسبة السوائل';
  static const String muscleMass = 'الكتلة العضلية';
  static const String upgradeToPremium = 'ترقية إلى مميز';
  
  // Medical Info
  static const String medications = 'الأدوية';
  static const String supplements = 'المكملات الغذائية';
  static const String medicalConditions = 'الحالة الصحية';
  static const String allergies = 'الحساسية من الأطعمة';
  static const String physicalActivity = 'النشاط البدني';
  static const String notes = 'الملاحظات';
  
  // Products
  static const String addProduct = 'إضافة منتج';
  static const String editProduct = 'تعديل منتج';
  static const String deleteProduct = 'حذف منتج';
  static const String productName = 'اسم المنتج';
  static const String productDescription = 'وصف المنتج';
  static const String productPrice = 'سعر المنتج';
  static const String productCategory = 'فئة المنتج';
  static const String productImage = 'صورة المنتج';
  static const String active = 'نشط';
  static const String inactive = 'غير نشط';
  

  
  // Common
  static const String save = 'حفظ';
  static const String cancel = 'إلغاء';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String add = 'إضافة';
  static const String search = 'البحث';
  static const String filter = 'تصفية';
  static const String loading = 'جاري التحميل...';
  static const String error = 'خطأ';
  static const String success = 'نجح';
  static const String warning = 'تحذير';
  static const String info = 'معلومات';
  static const String confirm = 'تأكيد';
  static const String yes = 'نعم';
  static const String no = 'لا';
  static const String ok = 'موافق';
  static const String retry = 'إعادة المحاولة';
  static const String noData = 'لا توجد بيانات';
  static const String noInternet = 'لا يوجد اتصال بالإنترنت';
  
  // Validation
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsDoNotMatch = 'كلمات المرور غير متطابقة';
  static const String invalidPhoneNumber = 'رقم الهاتف غير صحيح';
}
