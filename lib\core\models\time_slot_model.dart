import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class TimeSlotModel extends Equatable {
  final String id;
  final int dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday
  final String startTime; // HH:mm format
  final String endTime; // HH:mm format
  final int durationMinutes;
  final bool isActive;
  final int maxPatients;
  final String employeeId; // Required - each time slot must belong to an employee
  final String? employeeName; // Employee name for display
  final String? specialization; // Employee specialization
  final DateTime createdAt;
  final DateTime updatedAt;

  const TimeSlotModel({
    required this.id,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.durationMinutes,
    required this.isActive,
    required this.maxPatients,
    required this.employeeId,
    this.employeeName,
    this.specialization,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get day name in Arabic
  String get dayNameArabic {
    const dayNames = [
      'الأحد',    // 0 - Sunday
      'الإثنين',  // 1 - Monday
      'الثلاثاء', // 2 - Tuesday
      'الأربعاء', // 3 - Wednesday
      'الخميس',   // 4 - Thursday
      'الجمعة',   // 5 - Friday
      'السبت',    // 6 - Saturday
    ];
    return dayNames[dayOfWeek];
  }

  // Get day name in English
  String get dayNameEnglish {
    const dayNames = [
      'Sunday',    // 0
      'Monday',    // 1
      'Tuesday',   // 2
      'Wednesday', // 3
      'Thursday',  // 4
      'Friday',    // 5
      'Saturday',  // 6
    ];
    return dayNames[dayOfWeek];
  }

  // Format time range with AM/PM
  String get timeRange {
    final startFormatted = _formatTimeWithAmPm(startTime);
    final endFormatted = _formatTimeWithAmPm(endTime);
    return '$startFormatted - $endFormatted';
  }

  // Helper method to format time with AM/PM
  String _formatTimeWithAmPm(String time24) {
    try {
      final parts = time24.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);

      if (hour == 0) {
        return '12:${minute.toString().padLeft(2, '0')} ص';
      } else if (hour < 12) {
        return '$hour:${minute.toString().padLeft(2, '0')} ص';
      } else if (hour == 12) {
        return '12:${minute.toString().padLeft(2, '0')} م';
      } else {
        return '${hour - 12}:${minute.toString().padLeft(2, '0')} م';
      }
    } catch (e) {
      return time24; // fallback to original format
    }
  }

  // Check if time slot is valid
  bool get isValidTimeRange {
    try {
      final start = DateTime.parse('2024-01-01 $startTime');
      final end = DateTime.parse('2024-01-01 $endTime');
      return end.isAfter(start);
    } catch (e) {
      return false;
    }
  }

  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    try {
      // Debug print the incoming JSON
      debugPrint('🔄 TimeSlotModel.fromJson: Parsing JSON: $json');

      final id = json['id'] as String;
      final dayOfWeek = json['day_of_week'] as int;

      // Handle time format from database (might include seconds)
      String rawStartTime = json['start_time'] as String;
      String rawEndTime = json['end_time'] as String;

      // Remove seconds if present (e.g., "09:00:00" -> "09:00")
      final startTime = rawStartTime.length > 5 ? rawStartTime.substring(0, 5) : rawStartTime;
      final endTime = rawEndTime.length > 5 ? rawEndTime.substring(0, 5) : rawEndTime;

      debugPrint('🕐 TimeSlotModel: Raw times: $rawStartTime -> $startTime, $rawEndTime -> $endTime');
      final durationMinutes = json['duration_minutes'] as int? ?? 30;
      final isActive = json['is_active'] as bool? ?? true;
      final maxPatients = json['max_patients'] as int? ?? 1;
      final employeeId = json['employee_id'] as String;

      // Handle employee data from join
      String? employeeName;
      String? specialization;

      if (json['employees'] != null) {
        final employeeData = json['employees'] as Map<String, dynamic>;
        employeeName = employeeData['name'] as String?;
        specialization = employeeData['specialization_name'] as String?;
      }

      final createdAt = DateTime.parse(json['created_at'] as String);
      final updatedAt = DateTime.parse(json['updated_at'] as String);

      debugPrint('✅ TimeSlotModel.fromJson: Successfully parsed time slot $id');

      return TimeSlotModel(
        id: id,
        dayOfWeek: dayOfWeek,
        startTime: startTime,
        endTime: endTime,
        durationMinutes: durationMinutes,
        isActive: isActive,
        maxPatients: maxPatients,
        employeeId: employeeId,
        employeeName: employeeName,
        specialization: specialization,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e, stackTrace) {
      debugPrint('❌ TimeSlotModel.fromJson: Error parsing JSON: $e');
      debugPrint('📍 TimeSlotModel.fromJson: JSON was: $json');
      debugPrint('📍 TimeSlotModel.fromJson: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'day_of_week': dayOfWeek,
      'start_time': startTime,
      'end_time': endTime,
      'duration_minutes': durationMinutes,
      'is_active': isActive,
      'max_patients': maxPatients,
      'employee_id': employeeId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  TimeSlotModel copyWith({
    String? id,
    int? dayOfWeek,
    String? startTime,
    String? endTime,
    int? durationMinutes,
    bool? isActive,
    int? maxPatients,
    String? employeeId,
    String? employeeName,
    String? specialization,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TimeSlotModel(
      id: id ?? this.id,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      isActive: isActive ?? this.isActive,
      maxPatients: maxPatients ?? this.maxPatients,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      specialization: specialization ?? this.specialization,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        dayOfWeek,
        startTime,
        endTime,
        durationMinutes,
        isActive,
        maxPatients,
        employeeId,
        employeeName,
        specialization,
        createdAt,
        updatedAt,
      ];
}
