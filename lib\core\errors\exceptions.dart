class ServerException implements Exception {
  final String message;
  
  const ServerException(this.message);
}

class CacheException implements Exception {
  final String message;
  
  const CacheException(this.message);
}

class NetworkException implements Exception {
  final String message;
  
  const NetworkException(this.message);
}

class ValidationException implements Exception {
  final String message;
  
  const ValidationException(this.message);
}

class AuthenticationException implements Exception {
  final String message;
  
  const AuthenticationException(this.message);
}

class AuthorizationException implements Exception {
  final String message;
  
  const AuthorizationException(this.message);
}

class NotFoundException implements Exception {
  final String message;
  
  const NotFoundException(this.message);
}

class ConflictException implements Exception {
  final String message;
  
  const ConflictException(this.message);
}

class UnknownException implements Exception {
  final String message;
  
  const UnknownException(this.message);
}
