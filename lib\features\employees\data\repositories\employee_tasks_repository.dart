import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/employee_task_model.dart';

class EmployeeTasksRepository {
  // Get all tasks
  static Future<List<EmployeeTaskModel>> getAllTasks() async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Loading all tasks...');
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .order('created_at', ascending: false);

      final tasks = (response as List)
          .map((json) => EmployeeTaskModel.fromJson(json))
          .toList();
      
      debugPrint('✅ EmployeeTasksRepository: Successfully loaded ${tasks.length} tasks');
      return tasks;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error loading tasks: $e');
      throw Exception('فشل في تحميل المهام: $e');
    }
  }

  // Get tasks by employee ID
  static Future<List<EmployeeTaskModel>> getTasksByEmployeeId(String employeeId) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Loading tasks for employee: $employeeId');
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .eq('employee_id', employeeId)
          .order('created_at', ascending: false);

      final tasks = (response as List)
          .map((json) => EmployeeTaskModel.fromJson(json))
          .toList();
      
      debugPrint('✅ EmployeeTasksRepository: Successfully loaded ${tasks.length} tasks for employee');
      return tasks;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error loading employee tasks: $e');
      throw Exception('فشل في تحميل مهام الموظف: $e');
    }
  }

  // Get tasks by status
  static Future<List<EmployeeTaskModel>> getTasksByStatus(String status) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Loading tasks with status: $status');
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .eq('status', status)
          .order('created_at', ascending: false);

      final tasks = (response as List)
          .map((json) => EmployeeTaskModel.fromJson(json))
          .toList();
      
      debugPrint('✅ EmployeeTasksRepository: Successfully loaded ${tasks.length} tasks with status $status');
      return tasks;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error loading tasks by status: $e');
      throw Exception('فشل في تحميل المهام حسب الحالة: $e');
    }
  }

  // Add new task
  static Future<EmployeeTaskModel> addTask(EmployeeTaskModel task) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Adding new task: ${task.title}');
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .insert(task.toJson())
          .select()
          .single();

      final newTask = EmployeeTaskModel.fromJson(response);
      debugPrint('✅ EmployeeTasksRepository: Successfully added task');
      
      return newTask;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error adding task: $e');
      throw Exception('فشل في إضافة المهمة: $e');
    }
  }

  // Update task
  static Future<EmployeeTaskModel> updateTask(EmployeeTaskModel task) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Updating task: ${task.title}');
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .update(task.toJson())
          .eq('id', task.id)
          .select()
          .single();

      final updatedTask = EmployeeTaskModel.fromJson(response);
      debugPrint('✅ EmployeeTasksRepository: Successfully updated task');
      
      return updatedTask;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error updating task: $e');
      throw Exception('فشل في تحديث المهمة: $e');
    }
  }

  // Update task status
  static Future<EmployeeTaskModel> updateTaskStatus(String taskId, String status) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Updating task status: $taskId to $status');
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', taskId)
          .select()
          .single();

      final updatedTask = EmployeeTaskModel.fromJson(response);
      debugPrint('✅ EmployeeTasksRepository: Successfully updated task status');
      
      return updatedTask;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error updating task status: $e');
      throw Exception('فشل في تحديث حالة المهمة: $e');
    }
  }

  // Delete task
  static Future<void> deleteTask(String taskId) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Deleting task: $taskId');
      
      await SupabaseConfig.client
          .from('employee_tasks')
          .delete()
          .eq('id', taskId);

      debugPrint('✅ EmployeeTasksRepository: Successfully deleted task');
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error deleting task: $e');
      throw Exception('فشل في حذف المهمة: $e');
    }
  }

  // Get overdue tasks
  static Future<List<EmployeeTaskModel>> getOverdueTasks() async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Loading overdue tasks...');
      
      final now = DateTime.now().toIso8601String();
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .lt('due_date', now)
          .neq('status', 'completed')
          .neq('status', 'cancelled')
          .order('due_date', ascending: true);

      final tasks = (response as List)
          .map((json) => EmployeeTaskModel.fromJson(json))
          .toList();
      
      debugPrint('✅ EmployeeTasksRepository: Successfully loaded ${tasks.length} overdue tasks');
      return tasks;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error loading overdue tasks: $e');
      throw Exception('فشل في تحميل المهام المتأخرة: $e');
    }
  }

  // Get tasks due soon (within 3 days)
  static Future<List<EmployeeTaskModel>> getTasksDueSoon() async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Loading tasks due soon...');
      
      final now = DateTime.now();
      final threeDaysFromNow = now.add(const Duration(days: 3));
      
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .gte('due_date', now.toIso8601String())
          .lte('due_date', threeDaysFromNow.toIso8601String())
          .neq('status', 'completed')
          .neq('status', 'cancelled')
          .order('due_date', ascending: true);

      final tasks = (response as List)
          .map((json) => EmployeeTaskModel.fromJson(json))
          .toList();
      
      debugPrint('✅ EmployeeTasksRepository: Successfully loaded ${tasks.length} tasks due soon');
      return tasks;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error loading tasks due soon: $e');
      throw Exception('فشل في تحميل المهام المستحقة قريباً: $e');
    }
  }

  // Get admin name by ID
  static Future<String?> getAdminName(String adminId) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Getting admin name for: $adminId');
      
      final response = await SupabaseConfig.client
          .from('admins')
          .select('name')
          .eq('id', adminId)
          .maybeSingle();

      if (response != null) {
        final name = response['name'] as String?;
        debugPrint('✅ EmployeeTasksRepository: Found admin name: $name');
        return name;
      }
      
      debugPrint('⚠️ EmployeeTasksRepository: Admin not found: $adminId');
      return null;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error getting admin name: $e');
      return null;
    }
  }

  // Get employee name by ID
  static Future<String?> getEmployeeName(String employeeId) async {
    try {
      debugPrint('🔍 EmployeeTasksRepository: Getting employee name for: $employeeId');
      
      final response = await SupabaseConfig.client
          .from('admins')
          .select('name')
          .eq('id', employeeId)
          .maybeSingle();

      if (response != null) {
        final name = response['name'] as String?;
        debugPrint('✅ EmployeeTasksRepository: Found employee name: $name');
        return name;
      }
      
      debugPrint('⚠️ EmployeeTasksRepository: Employee not found: $employeeId');
      return null;
    } catch (e) {
      debugPrint('❌ EmployeeTasksRepository: Error getting employee name: $e');
      return null;
    }
  }
}