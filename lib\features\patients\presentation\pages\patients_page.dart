import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/treatment_type.dart';
import '../../../../core/utils/debouncer.dart';

import '../../../../core/widgets/responsive_button.dart';
import '../bloc/patients_bloc.dart';
import '../bloc/patients_event.dart';
import '../bloc/patients_state.dart';
import '../widgets/patient_form_dialog.dart';
import '../widgets/patient_details_dialog.dart';
import 'medical_record_page.dart';
import 'add_patient_page.dart';
import '../../../appointments/presentation/pages/create_appointment_page.dart';

class PatientsPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const PatientsPage({
    super.key,
    this.isVisible = false,
    this.hasBeenVisited = false,
  });

  @override
  State<PatientsPage> createState() => _PatientsPageState();
}

class _PatientsPageState extends State<PatientsPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);

  // Track if data has been loaded
  bool _dataLoaded = false;

  @override
  bool get wantKeepAlive => true; // Keep state alive

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Don't load patients automatically - wait for user to visit this page
  }

  @override
  void didUpdateWidget(PatientsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if page became visible
    if (widget.isVisible && !oldWidget.isVisible) {
      _loadPatientsIfNeeded();
    }
  }

  void _loadPatientsIfNeeded() {
    // Only load if page is visible and data hasn't been loaded yet
    if (widget.isVisible && !_dataLoaded) {
      debugPrint('🔄 Loading patients data for first time...');
      context.read<PatientsBloc>().add(LoadAllPatients());
      _dataLoaded = true;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debouncer.run(() {
      setState(() {
        // Trigger rebuild with new search query
      });
    });
  }

  List<PatientModel> _filterPatients(List<PatientModel> patients, String query) {
    if (query.isEmpty) {
      return patients;
    }

    final lowercaseQuery = query.toLowerCase();
    return patients.where((patient) {
      final name = patient.name.toLowerCase();
      final phone = patient.phone?.toLowerCase() ?? '';
      final patientId = patient.patientId?.toLowerCase() ?? '';

      return name.contains(lowercaseQuery) ||
             phone.contains(lowercaseQuery) ||
             patientId.contains(lowercaseQuery);
    }).toList();
  }

  // Helper method to get treatment type colors
  Color _getTreatmentTypeColor(List<TreatmentType> treatmentTypes) {
    if (treatmentTypes.isEmpty) return AppColors.gray300;
    
    // Use the first treatment type's color from the enum itself
    final firstType = treatmentTypes.first;
    return firstType.color;
  }

  // Helper method to get treatment type display text
  String _getTreatmentTypesText(List<TreatmentType> treatmentTypes) {
    if (treatmentTypes.isEmpty) return 'غير محدد';
    
    final arabicNames = treatmentTypes.map((type) => type.arabicName).toList();
    if (arabicNames.length > 2) {
      return '${arabicNames.take(2).join('، ')} +${arabicNames.length - 2}';
    }
    return arabicNames.join('، ');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Load data only if page is currently visible
    if (widget.isVisible) {
      _loadPatientsIfNeeded();
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.patients),
        backgroundColor: AppColors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(100.h),
          child: Column(
            children: [
              // Search Bar
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                child: TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'البحث بالاسم، الهاتف، أو رقم المريض...',
                    prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, color: AppColors.textSecondary),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.gray200),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.gray200),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    filled: true,
                    fillColor: AppColors.gray50,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  ),
                ),
              ),
              // Tab Bar
              TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: const [
                  Tab(text: AppStrings.premiumPatients),
                  Tab(text: AppStrings.allPatients),
                  Tab(text: 'إضافة مريض'),
                ],
              ),
            ],
          ),
        ),
      ),
      body: BlocConsumer<PatientsBloc, PatientsState>(
        listener: (context, state) {
          if (state is PatientsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is PatientCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة المريض بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is PatientUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث بيانات المريض بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is PatientDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف المريض بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PatientsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is PatientsLoaded) {
            return TabBarView(
              controller: _tabController,
              children: [
                _buildPremiumPatientsView(_filterPatients(state.premiumPatients, _searchController.text)),
                _buildAllPatientsView(_filterPatients(state.patients, _searchController.text)),
                const AddPatientPage(),
              ],
            );
          }

          if (state is PatientsError) {
            return _buildErrorView(state.message);
          }

          return _buildEmptyView();
        },
      ),
    );
  }

  void _showAddPatientDialog() {
    showDialog(
      context: context,
      builder:
          (context) => BlocProvider.value(
            value: context.read<PatientsBloc>(),
            child: const PatientFormDialog(),
          ),
    );
  }

  Future<void> _onRefreshPatients() async {
    debugPrint('🔄 Refreshing patients data...');
    context.read<PatientsBloc>().add(LoadAllPatients());
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline,
                size: 64.w,
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12.h),
            Text(
              message,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<PatientsBloc>().add(LoadAllPatients());
                  },
                  icon: const Icon(Icons.refresh, color: AppColors.white),
                  label: const Text('إعادة المحاولة', style: TextStyle(color: AppColors.white)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                color: AppColors.gray100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.people_outline,
                size: 64.w,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              'لا توجد بيانات',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'لم يتم العثور على أي مرضى',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 32.h),
            ElevatedButton.icon(
              onPressed: () {
                context.read<PatientsBloc>().add(LoadAllPatients());
              },
              icon: const Icon(Icons.refresh, color: AppColors.white),
              label: const Text('تحديث البيانات', style: TextStyle(color: AppColors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyPatientsView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: AppColors.gray100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.people_outline,
              size: 48.w,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 16.h),
          TextButton.icon(
            onPressed: () {
              context.read<PatientsBloc>().add(LoadAllPatients());
            },
            icon: const Icon(Icons.refresh, size: 20),
            label: const Text('إعادة التحميل'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumPatientsView(List<PatientModel> patients) {
    return RefreshIndicator(
      onRefresh: _onRefreshPatients,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Premium Patients List
            Expanded(
              child:
                  patients.isEmpty
                      ? _buildEmptyPatientsView('لا توجد مرضى مميزين')
                      : ListView.builder(
                        itemCount: patients.length,
                        itemBuilder: (context, index) {
                          final patient = patients[index];
                          final treatmentColor = _getTreatmentTypeColor(patient.treatmentTypes);
                          
                          return Container(
                            margin: EdgeInsets.only(bottom: 12.h),
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(
                                color: treatmentColor.withValues(alpha: 0.3),
                                width: 2,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: treatmentColor.withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: IntrinsicHeight(
                              child: Stack(
                                children: [
                                  // المحتوى الأساسي
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 24.r,
                                            backgroundColor: treatmentColor.withValues(alpha: 0.1),
                                            child: Icon(
                                              Icons.person,
                                              color: treatmentColor,
                                              size: 24.w,
                                            ),
                                          ),
                                          SizedBox(width: 12.w),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        patient.name,
                                                        style: TextStyle(
                                                          fontSize: 16.sp,
                                                          fontWeight: FontWeight.w600,
                                                          color: AppColors.textPrimary,
                                                        ),
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                    ),
                                                    SizedBox(width: 8.w),
                                                    Container(
                                                      padding: EdgeInsets.symmetric(
                                                        horizontal: 6.w,
                                                        vertical: 2.h,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: AppColors.primary,
                                                        borderRadius: BorderRadius.circular(4.r),
                                                      ),
                                                      child: Text(
                                                        'مميز',
                                                        style: TextStyle(
                                                          fontSize: 10.sp,
                                                          fontWeight: FontWeight.w600,
                                                          color: AppColors.white,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 4.h),
                                                // Treatment Types
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: 8.w,
                                                    vertical: 4.h,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: treatmentColor.withValues(alpha: 0.1),
                                                    borderRadius: BorderRadius.circular(6.r),
                                                    border: Border.all(
                                                      color: treatmentColor.withValues(alpha: 0.3),
                                                    ),
                                                  ),
                                                  child: Text(
                                                    _getTreatmentTypesText(patient.treatmentTypes),
                                                    style: TextStyle(
                                                      fontSize: 11.sp,
                                                      fontWeight: FontWeight.w600,
                                                      color: treatmentColor,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 4.h),
                                                // Phone
                                                if (patient.phone != null && patient.phone!.isNotEmpty)
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.phone,
                                                        size: 14.w,
                                                        color: AppColors.textSecondary,
                                                      ),
                                                      SizedBox(width: 4.w),
                                                      Expanded(
                                                        child: Text(
                                                          patient.phone!,
                                                          style: TextStyle(
                                                            fontSize: 13.sp,
                                                            color: AppColors.textSecondary,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 8.w),
                                                      // Call button
                                                      InkWell(
                                                        onTap: () => _makePhoneCall(patient.phone!),
                                                        child: Container(
                                                          padding: EdgeInsets.all(4.w),
                                                          decoration: BoxDecoration(
                                                            color: AppColors.primary.withValues(alpha: 0.1),
                                                            borderRadius: BorderRadius.circular(4.r),
                                                          ),
                                                          child: Icon(
                                                            Icons.phone,
                                                            size: 14.w,
                                                            color: AppColors.primary,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 4.w),
                                                      // Copy button
                                                      InkWell(
                                                        onTap: () => _copyToClipboard(patient.phone!),
                                                        child: Container(
                                                          padding: EdgeInsets.all(4.w),
                                                          decoration: BoxDecoration(
                                                            color: AppColors.primary.withValues(alpha: 0.1),
                                                            borderRadius: BorderRadius.circular(4.r),
                                                          ),
                                                          child: Icon(
                                                            Icons.copy,
                                                            size: 14.w,
                                                            color: AppColors.primary,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                SizedBox(height: 2.h),
                                                // Patient ID
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.badge,
                                                      size: 14.w,
                                                      color: AppColors.textSecondary,
                                                    ),
                                                    SizedBox(width: 4.w),
                                                    Text(
                                                      'ID: ${patient.patientId ?? 'غير محدد'}',
                                                      style: TextStyle(
                                                        fontSize: 13.sp,
                                                        color: AppColors.primary,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 2.h),
                                                Text(
                                                  'العمر: ${patient.age} سنة${patient.gender != null ? ' | ${patient.gender}' : ''}',
                                                  style: TextStyle(
                                                    fontSize: 13.sp,
                                                    color: AppColors.textSecondary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(width: 30.w), // مساحة لأيقونة 3 نقاط
                                        ],
                                      ),
                                      SizedBox(height: 12.h),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: ResponsiveButton(
                                              onPressed: () {
                                                _navigateToMedicalRecord(patient);
                                              },
                                              text: 'السجل الطبي',
                                              icon: Icons.medical_information,
                                              isOutlined: true,
                                              showLoadingState: false,
                                            ),
                                          ),
                                          SizedBox(width: 8.w),
                                          Expanded(
                                            child: ElevatedButton.icon(
                                              onPressed: () {
                                                _navigateToAppointmentBooking(patient);
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: AppColors.primary,
                                                foregroundColor: AppColors.white,
                                                padding: EdgeInsets.symmetric(vertical: 12.h),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius: BorderRadius.circular(8.r),
                                                ),
                                              ),
                                              icon: Icon(
                                                Icons.calendar_today,
                                                size: 16.sp,
                                              ),
                                              label: Text(
                                                'حجز',
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  // أيقونة 3 نقاط في الزاوية العلوية اليسرى
                                  Positioned(
                                    top: 0,
                                    left: 0,
                                    child: PopupMenuButton<String>(
                                      onSelected: (value) {
                                        if (value == 'downgrade') {
                                          _showDowngradeConfirmation(patient);
                                        }
                                      },
                                      itemBuilder: (context) => [
                                        const PopupMenuItem<String>(
                                          value: 'downgrade',
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.star_border,
                                                color: AppColors.error,
                                              ),
                                              SizedBox(width: 8),
                                              Text('إلغاء العضوية المميزة'),
                                            ],
                                          ),
                                        ),
                                      ],
                                      child: Container(
                                        padding: EdgeInsets.all(4.w),
                                        child: Icon(
                                          Icons.more_vert,
                                          color: AppColors.textSecondary,
                                          size: 20.w,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllPatientsView(List<PatientModel> patients) {
    return RefreshIndicator(
      onRefresh: _onRefreshPatients,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // All Patients List
            Expanded(
              child: patients.isEmpty
                  ? _buildEmptyPatientsView('لا توجد مرضى')
                  : ListView.builder(
                      itemCount: patients.length,
                      itemBuilder: (context, index) {
                        final patient = patients[index];
                        final isPremium = patient.isPremium;
                        final treatmentColor = _getTreatmentTypeColor(patient.treatmentTypes);
                        
                        return GestureDetector(
                          onTap: () => _showPatientDetailsDialog(patient),
                          child: Container(
                            margin: EdgeInsets.only(bottom: 12.h),
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(
                                color: treatmentColor.withValues(alpha: 0.3),
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: treatmentColor.withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 20.r,
                                      backgroundColor: isPremium
                                          ? AppColors.primary.withValues(alpha: 0.1)
                                          : treatmentColor.withValues(alpha: 0.1),
                                      child: Icon(
                                        Icons.person,
                                        color: isPremium ? AppColors.primary : treatmentColor,
                                        size: 20.w,
                                      ),
                                    ),
                                    SizedBox(width: 12.w),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Flexible(
                                                child: Text(
                                                  patient.name,
                                                  style: TextStyle(
                                                    fontSize: 16.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color: AppColors.textPrimary,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                              if (isPremium) ...[
                                                SizedBox(width: 8.w),
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: 6.w,
                                                    vertical: 2.h,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.primary,
                                                    borderRadius: BorderRadius.circular(4.r),
                                                  ),
                                                  child: Text(
                                                    'مميز',
                                                    style: TextStyle(
                                                      fontSize: 10.sp,
                                                      fontWeight: FontWeight.w600,
                                                      color: AppColors.white,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ],
                                          ),
                                          SizedBox(height: 4.h),
                                          // Treatment Types
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 8.w,
                                              vertical: 4.h,
                                            ),
                                            decoration: BoxDecoration(
                                              color: treatmentColor.withValues(alpha: 0.1),
                                              borderRadius: BorderRadius.circular(6.r),
                                              border: Border.all(
                                                color: treatmentColor.withValues(alpha: 0.3),
                                              ),
                                            ),
                                            child: Text(
                                              _getTreatmentTypesText(patient.treatmentTypes),
                                              style: TextStyle(
                                                fontSize: 11.sp,
                                                fontWeight: FontWeight.w600,
                                                color: treatmentColor,
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 4.h),
                                          // Phone
                                          if (patient.phone != null && patient.phone!.isNotEmpty)
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.phone,
                                                  size: 14.w,
                                                  color: AppColors.textSecondary,
                                                ),
                                                SizedBox(width: 4.w),
                                                Expanded(
                                                  child: Text(
                                                    patient.phone!,
                                                    style: TextStyle(
                                                      fontSize: 13.sp,
                                                      color: AppColors.textSecondary,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(width: 8.w),
                                                // Call button
                                                InkWell(
                                                  onTap: () => _makePhoneCall(patient.phone!),
                                                  child: Container(
                                                    padding: EdgeInsets.all(4.w),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.primary.withValues(alpha: 0.1),
                                                      borderRadius: BorderRadius.circular(4.r),
                                                    ),
                                                    child: Icon(
                                                      Icons.phone,
                                                      size: 14.w,
                                                      color: AppColors.primary,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(width: 4.w),
                                                // Copy button
                                                InkWell(
                                                  onTap: () => _copyToClipboard(patient.phone!),
                                                  child: Container(
                                                    padding: EdgeInsets.all(4.w),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.primary.withValues(alpha: 0.1),
                                                      borderRadius: BorderRadius.circular(4.r),
                                                    ),
                                                    child: Icon(
                                                      Icons.copy,
                                                      size: 14.w,
                                                      color: AppColors.primary,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          SizedBox(height: 2.h),
                                          // Patient ID
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.badge,
                                                size: 14.w,
                                                color: AppColors.textSecondary,
                                              ),
                                              SizedBox(width: 4.w),
                                              Text(
                                                'ID: ${patient.patientId ?? 'غير محدد'}',
                                                style: TextStyle(
                                                  fontSize: 13.sp,
                                                  color: AppColors.primary,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 2.h),
                                          Text(
                                            'العمر: ${patient.age} سنة | ${patient.gender}',
                                            style: TextStyle(
                                              fontSize: 13.sp,
                                              color: AppColors.textSecondary,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 12.h),
                                // Action buttons row
                                Row(
                                  children: [
                                    if (!isPremium)
                                      Expanded(
                                        child: Container(
                                          height: 36.h,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: [
                                                AppColors.warning.withValues(alpha: 0.1),
                                                AppColors.primary.withValues(alpha: 0.05),
                                              ],
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                            ),
                                            borderRadius: BorderRadius.circular(8.r),
                                            border: Border.all(
                                              color: AppColors.warning.withValues(alpha: 0.3),
                                            ),
                                          ),
                                          child: ElevatedButton.icon(
                                            onPressed: () {
                                              context.read<PatientsBloc>().add(
                                                UpgradeToPremium(patientId: patient.id),
                                              );
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.transparent,
                                              foregroundColor: AppColors.warning,
                                              elevation: 0,
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 12.w,
                                                vertical: 8.h,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(8.r),
                                              ),
                                            ),
                                            icon: Icon(
                                              Icons.star,
                                              size: 16.sp,
                                            ),
                                            label: Text(
                                              'عضوية مميزة',
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (!isPremium) SizedBox(width: 8.w),
                                    Expanded(
                                      child: ElevatedButton.icon(
                                        onPressed: () {
                                          _navigateToAppointmentBooking(patient);
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.primary,
                                          foregroundColor: AppColors.white,
                                          padding: EdgeInsets.symmetric(vertical: 8.h),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(8.r),
                                          ),
                                        ),
                                        icon: Icon(
                                          Icons.calendar_today,
                                          size: 16.sp,
                                        ),
                                        label: Text(
                                          'حجز',
                                          style: TextStyle(
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToMedicalRecord(PatientModel patient) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MedicalRecordPage(patient: patient),
      ),
    );
  }

  void _navigateToAppointmentBooking(PatientModel patient) {
    // Navigate to appointment booking page with pre-selected patient
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateAppointmentPage(
          preSelectedPatient: patient,
        ),
      ),
    );
  }

  // دالة الاتصال
  void _makePhoneCall(String phoneNumber) async {
    try {
      // تنظيف رقم الهاتف من المسافات والرموز الخاصة
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // استخدام tel: scheme مع DIAL mode (أكثر أماناً)
      final Uri phoneUri = Uri.parse('tel:$cleanNumber');

      debugPrint('🔄 Attempting to call: $cleanNumber');
      debugPrint('📞 URI: $phoneUri');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(
          phoneUri,
          mode: LaunchMode.externalApplication, // فتح في تطبيق خارجي
        );
        debugPrint('✅ Phone call initiated successfully');
      } else {
        debugPrint('❌ Cannot launch phone app');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح تطبيق الهاتف'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error making phone call: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // دالة نسخ النص
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ رقم الهاتف'),
          backgroundColor: AppColors.success,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // دالة إظهار تفاصيل المريض
  void _showPatientDetailsDialog(PatientModel patient) {
    showDialog(
      context: context,
      builder: (context) => PatientDetailsDialog(patient: patient),
    );
  }

  // د��لة تأكيد إلغاء العضوية المميزة
  void _showDowngradeConfirmation(PatientModel patient) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إلغاء العضوية المميزة'),
        content: Text(
          'هل أنت متأكد من إلغاء العضوية المميزة للمريض "${patient.name}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<PatientsBloc>().add(
                DowngradeFromPremium(patientId: patient.id),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}