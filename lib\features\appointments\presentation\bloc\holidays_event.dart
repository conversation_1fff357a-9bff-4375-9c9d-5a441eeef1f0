import 'package:equatable/equatable.dart';
import '../../../../core/models/holiday_model.dart';

abstract class HolidaysEvent extends Equatable {
  const HolidaysEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllHolidays extends HolidaysEvent {
  const LoadAllHolidays();
}

class LoadActiveHolidays extends HolidaysEvent {
  const LoadActiveHolidays();
}

class CheckHolidayByDate extends HolidaysEvent {
  final DateTime date;

  const CheckHolidayByDate({required this.date});

  @override
  List<Object?> get props => [date];
}

class LoadHolidaysInRange extends HolidaysEvent {
  final DateTime startDate;
  final DateTime endDate;

  const LoadHolidaysInRange({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];
}

class AddHoliday extends HolidaysEvent {
  final HolidayModel holiday;

  const AddHoliday({required this.holiday});

  @override
  List<Object?> get props => [holiday];
}

class UpdateHoliday extends HolidaysEvent {
  final HolidayModel holiday;

  const UpdateHoliday({required this.holiday});

  @override
  List<Object?> get props => [holiday];
}

class DeleteHoliday extends HolidaysEvent {
  final String holidayId;

  const DeleteHoliday({required this.holidayId});

  @override
  List<Object?> get props => [holidayId];
}

class ToggleHolidayStatus extends HolidaysEvent {
  final String holidayId;
  final bool isActive;

  const ToggleHolidayStatus({
    required this.holidayId,
    required this.isActive,
  });

  @override
  List<Object?> get props => [holidayId, isActive];
}

class SearchHolidays extends HolidaysEvent {
  final String query;

  const SearchHolidays({required this.query});

  @override
  List<Object?> get props => [query];
}

class RefreshHolidays extends HolidaysEvent {
  const RefreshHolidays();
}
