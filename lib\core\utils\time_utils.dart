class TimeUtils {
  /// Convert 24-hour time format to 12-hour format with Arabic AM/PM
  static String convertTo12HourFormat(String time24) {
    try {
      // Parse the time (format: HH:mm:ss or HH:mm)
      final parts = time24.split(':');
      if (parts.isEmpty) return time24;
      
      final hour = int.parse(parts[0]);
      final minute = parts.length > 1 ? int.parse(parts[1]) : 0;
      
      // Convert to 12-hour format
      String period;
      int displayHour;
      
      if (hour == 0) {
        displayHour = 12;
        period = 'ص'; // صباحاً
      } else if (hour < 12) {
        displayHour = hour;
        period = 'ص'; // صباحاً
      } else if (hour == 12) {
        displayHour = 12;
        period = 'م'; // مساءً
      } else {
        displayHour = hour - 12;
        period = 'م'; // مساءً
      }
      
      // Format the result
      final minuteStr = minute.toString().padLeft(2, '0');
      return '$displayHour:$minuteStr $period';
    } catch (e) {
      // Return original time if parsing fails
      return time24;
    }
  }
  
  /// Get Arabic day name from weekday number
  static String getArabicDayName(int weekday) {
    switch (weekday) {
      case DateTime.sunday:
        return 'الأحد';
      case DateTime.monday:
        return 'الإثنين';
      case DateTime.tuesday:
        return 'الثلاثاء';
      case DateTime.wednesday:
        return 'الأربعاء';
      case DateTime.thursday:
        return 'الخميس';
      case DateTime.friday:
        return 'الجمعة';
      case DateTime.saturday:
        return 'السبت';
      default:
        return 'غير محدد';
    }
  }
  
  /// Get day of week number for our system (0=Sunday, 6=Saturday)
  static int getDayOfWeekNumber(int flutterWeekday) {
    switch (flutterWeekday) {
      case DateTime.sunday:
        return 0;
      case DateTime.monday:
        return 1;
      case DateTime.tuesday:
        return 2;
      case DateTime.wednesday:
        return 3;
      case DateTime.thursday:
        return 4;
      case DateTime.friday:
        return 5;
      case DateTime.saturday:
        return 6;
      default:
        return 0;
    }
  }
  
  /// Format time range in 12-hour format
  static String formatTimeRange(String startTime, String endTime) {
    final start12 = convertTo12HourFormat(startTime);
    final end12 = convertTo12HourFormat(endTime);
    return '$start12 - $end12';
  }
}
