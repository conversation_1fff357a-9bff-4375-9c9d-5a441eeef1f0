import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../bloc/employees_bloc.dart';
import 'add_employee_dialog.dart';

class EmployeeDetailsDialog extends StatelessWidget {
  final EmployeeModel employee;

  const EmployeeDetailsDialog({super.key, required this.employee});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: employee.specialization?.color != null 
                      ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
                      : AppColors.primary,
                  child: Text(
                    employee.name.isNotEmpty ? employee.name[0] : 'م',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        employee.displayRole,
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      if (employee.specialization != null) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF'))).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            employee.specialization!.name,
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF'))),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            
            // Details
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailSection('المعلومات الأساسية', [
                      _buildDetailRow('البريد الإلكتروني', employee.email, Icons.email),
                      if (employee.phone != null)
                        _buildDetailRow('رقم الهاتف', employee.phone!, Icons.phone),
                      if (employee.address != null)
                        _buildDetailRow('العنوان', employee.address!, Icons.location_on),
                      _buildDetailRow('الحالة', employee.isActive ? 'نشط' : 'غير نشط', 
                        employee.isActive ? Icons.check_circle : Icons.cancel,
                        valueColor: employee.isActive ? Colors.green : Colors.red),
                    ]),
                    
                    const SizedBox(height: 24),
                    
                    _buildDetailSection('معلومات العمل', [
                      _buildDetailRow('نوع الموظف', employee.displayRole, Icons.work),
                      if (employee.hireDate != null)
                        _buildDetailRow('تاريخ التوظيف', 
                          '${employee.hireDate!.day}/${employee.hireDate!.month}/${employee.hireDate!.year}', 
                          Icons.calendar_today),
                      if (employee.salary != null)
                        _buildDetailRow('الراتب', '${employee.salary!.toStringAsFixed(0)} ج.م', Icons.attach_money),
                    ]),
                    
                    if (employee.notes != null && employee.notes!.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildDetailSection('ملاحظات', [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.border),
                          ),
                          child: Text(
                            employee.notes!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textPrimary,
                              height: 1.5,
                            ),
                          ),
                        ),
                      ]),
                    ],
                    
                    const SizedBox(height: 24),
                    
                    _buildDetailSection('معلومات النظام', [
                      _buildDetailRow('تاريخ الإنشاء', 
                        '${employee.createdAt.day}/${employee.createdAt.month}/${employee.createdAt.year}', 
                        Icons.add_circle),
                      _buildDetailRow('آخر تحديث', 
                        '${employee.updatedAt.day}/${employee.updatedAt.month}/${employee.updatedAt.year}', 
                        Icons.update),
                      if (employee.lastLogin != null)
                        _buildDetailRow('آخر تسجيل دخول', 
                          '${employee.lastLogin!.day}/${employee.lastLogin!.month}/${employee.lastLogin!.year}', 
                          Icons.login),
                    ]),
                  ],
                ),
              ),
            ),
            
            // Actions
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      final result = await showDialog<bool>(
                        context: context,
                        builder: (context) => AddEmployeeDialog(employee: employee),
                      );
                      if (result == true && context.mounted) {
                        context.read<EmployeesBloc>().add(LoadAllEmployees());
                      }
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      context.read<EmployeesBloc>().add(
                        ToggleEmployeeStatus(employee.id, !employee.isActive),
                      );
                    },
                    icon: Icon(employee.isActive ? Icons.pause : Icons.play_arrow),
                    label: Text(employee.isActive ? 'إيقاف' : 'تفعيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: employee.isActive ? Colors.orange : Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
