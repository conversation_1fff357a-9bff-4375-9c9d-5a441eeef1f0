import 'package:equatable/equatable.dart';
import '../enums/user_role.dart';

class AdminUserModel extends Equatable {
  final String id;
  final String name;
  final String email;
  final String role; // 'manager', 'specialist', 'receptionist'
  final bool isActive;
  final String? phone;
  final String? profileImageUrl;
  final DateTime? lastLogin;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AdminUserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.isActive = true,
    this.phone,
    this.profileImageUrl,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AdminUserModel.fromJson(Map<String, dynamic> json) {
    return AdminUserModel(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      // استخدام employee_type أولاً، ثم role كبديل
      role: json['employee_type']?.toString() ?? json['role']?.toString() ?? 'receptionist',
      isActive: json['is_active'] as bool? ?? true,
      phone: json['phone']?.toString(),
      profileImageUrl: json['profile_image_url']?.toString(),
      lastLogin: json['last_login'] != null 
          ? DateTime.tryParse(json['last_login'].toString())
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'].toString()) ?? DateTime.now()
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'is_active': isActive,
      'phone': phone,
      'profile_image_url': profileImageUrl,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  AdminUserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    bool? isActive,
    String? phone,
    String? profileImageUrl,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminUserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  UserRole get userRole => UserRole.fromString(role);
  
  bool get isManager => role == 'manager';
  bool get isSpecialist => role == 'specialist';
  bool get isReceptionist => role == 'receptionist';

  String get displayRole {
    switch (role) {
      case 'manager':
        return 'مدير';
      case 'specialist':
        return 'أخصائي';
      case 'receptionist':
        return 'ريسبشنست';
      default:
        return 'موظف';
    }
  }

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    role,
    isActive,
    phone,
    profileImageUrl,
    lastLogin,
    createdAt,
    updatedAt,
  ];

  @override
  String toString() {
    return 'AdminUserModel(id: $id, name: $name, role: $role, isActive: $isActive)';
  }
}