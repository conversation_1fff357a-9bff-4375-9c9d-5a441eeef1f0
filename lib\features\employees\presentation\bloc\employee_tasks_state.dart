import 'package:equatable/equatable.dart';
import '../../../../core/models/employee_task_model.dart';

abstract class EmployeeTasksState extends Equatable {
  const EmployeeTasksState();

  @override
  List<Object?> get props => [];
}

class EmployeeTasksInitial extends EmployeeTasksState {
  const EmployeeTasksInitial();
}

class EmployeeTasksLoading extends EmployeeTasksState {
  const EmployeeTasksLoading();
}

class EmployeeTasksLoaded extends EmployeeTasksState {
  final List<EmployeeTaskModel> tasks;

  const EmployeeTasksLoaded(this.tasks);

  @override
  List<Object?> get props => [tasks];
}

class EmployeeTasksError extends EmployeeTasksState {
  final String message;

  const EmployeeTasksError(this.message);

  @override
  List<Object?> get props => [message];
}

class TaskAdded extends EmployeeTasksState {
  final EmployeeTaskModel task;

  const TaskAdded(this.task);

  @override
  List<Object?> get props => [task];
}

class TaskUpdated extends EmployeeTasksState {
  final EmployeeTaskModel task;

  const TaskUpdated(this.task);

  @override
  List<Object?> get props => [task];
}

class TaskDeleted extends EmployeeTasksState {
  final String taskId;

  const TaskDeleted(this.taskId);

  @override
  List<Object?> get props => [taskId];
}

class TaskStatusUpdated extends EmployeeTasksState {
  final EmployeeTaskModel task;

  const TaskStatusUpdated(this.task);

  @override
  List<Object?> get props => [task];
}

class TaskOperationSuccess extends EmployeeTasksState {
  final String message;
  final List<EmployeeTaskModel> tasks;

  const TaskOperationSuccess(this.message, this.tasks);

  @override
  List<Object?> get props => [message, tasks];
}
