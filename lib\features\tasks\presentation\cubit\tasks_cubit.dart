import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../data/repositories/tasks_repository.dart';

// States
abstract class TasksState extends Equatable {
  const TasksState();

  @override
  List<Object> get props => [];
}

class TasksInitial extends TasksState {}

class TasksLoading extends TasksState {}

class TasksLoaded extends TasksState {
  final List<Map<String, dynamic>> tasks;

  const TasksLoaded(this.tasks);

  @override
  List<Object> get props => [tasks];
}

class TasksError extends TasksState {
  final String message;

  const TasksError(this.message);

  @override
  List<Object> get props => [message];
}

// Cubit
class TasksCubit extends Cubit<TasksState> {
  final TasksRepository _repository;

  TasksCubit(this._repository) : super(TasksInitial());

  // Load tasks for a specific employee
  Future<void> loadEmployeeTasks(String employeeId) async {
    try {
      emit(TasksLoading());
      final tasks = await _repository.getEmployeeTasks(employeeId);
      emit(TasksLoaded(tasks));
    } catch (e) {
      emit(TasksError(e.toString()));
    }
  }

  // Load all tasks (for admin)
  Future<void> loadAllTasks() async {
    try {
      emit(TasksLoading());
      final tasks = await _repository.getAllTasks();
      emit(TasksLoaded(tasks));
    } catch (e) {
      emit(TasksError(e.toString()));
    }
  }

  // Update task status
  Future<void> updateTaskStatus(String taskId, String status) async {
    try {
      await _repository.updateTaskStatus(taskId, status);
      
      // Reload tasks after update
      if (state is TasksLoaded) {
        final currentTasks = (state as TasksLoaded).tasks;
        final updatedTasks = currentTasks.map((task) {
          if (task['id'] == taskId) {
            return {...task, 'status': status};
          }
          return task;
        }).toList();
        
        emit(TasksLoaded(updatedTasks));
      }
    } catch (e) {
      emit(TasksError('Failed to update task: $e'));
    }
  }

  // Refresh tasks
  Future<void> refreshTasks(String? employeeId) async {
    if (employeeId != null) {
      await loadEmployeeTasks(employeeId);
    } else {
      await loadAllTasks();
    }
  }
}