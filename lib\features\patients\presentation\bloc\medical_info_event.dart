import 'package:equatable/equatable.dart';
import '../../../../core/models/medical_info_model.dart';

abstract class MedicalInfoEvent extends Equatable {
  const MedicalInfoEvent();

  @override
  List<Object?> get props => [];
}

class LoadMedicalInfoByPatientId extends MedicalInfoEvent {
  final String patientId;

  const LoadMedicalInfoByPatientId({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadMedicalInfoByType extends MedicalInfoEvent {
  final String patientId;
  final String infoType;

  const LoadMedicalInfoByType({
    required this.patientId,
    required this.infoType,
  });

  @override
  List<Object?> get props => [patientId, infoType];
}

class AddMedicalInfo extends MedicalInfoEvent {
  final MedicalInfoModel medicalInfo;

  const AddMedicalInfo({required this.medicalInfo});

  @override
  List<Object?> get props => [medicalInfo];
}

class UpdateMedicalInfo extends MedicalInfoEvent {
  final MedicalInfoModel medicalInfo;

  const UpdateMedicalInfo({required this.medicalInfo});

  @override
  List<Object?> get props => [medicalInfo];
}

class DeleteMedicalInfo extends MedicalInfoEvent {
  final String medicalInfoId;
  final String patientId;

  const DeleteMedicalInfo({
    required this.medicalInfoId,
    required this.patientId,
  });

  @override
  List<Object?> get props => [medicalInfoId, patientId];
}

class ToggleMedicalInfoStatus extends MedicalInfoEvent {
  final String medicalInfoId;
  final bool isActive;
  final String patientId;

  const ToggleMedicalInfoStatus({
    required this.medicalInfoId,
    required this.isActive,
    required this.patientId,
  });

  @override
  List<Object?> get props => [medicalInfoId, isActive, patientId];
}

class SearchMedicalInfo extends MedicalInfoEvent {
  final String patientId;
  final String query;

  const SearchMedicalInfo({
    required this.patientId,
    required this.query,
  });

  @override
  List<Object?> get props => [patientId, query];
}

class RefreshMedicalInfo extends MedicalInfoEvent {
  final String patientId;

  const RefreshMedicalInfo({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}
