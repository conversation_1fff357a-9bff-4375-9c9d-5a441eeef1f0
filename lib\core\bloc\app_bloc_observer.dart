import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

class AppBlocObserver extends BlocObserver {
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    _logger.d('🟢 Bloc Created: ${bloc.runtimeType}');
  }

  @override
  void onEvent(BlocBase bloc, Object? event) {
    if (bloc is Bloc) {
      super.onEvent(bloc, event);
      _logger.i('📥 Event: ${bloc.runtimeType} | $event');
    }
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    _logger.i('🔄 State Change: ${bloc.runtimeType}');
    _logger.i('   Current: ${change.currentState.runtimeType}');
    _logger.i('   Next: ${change.nextState.runtimeType}');
  }

  @override
  void onTransition(BlocBase bloc, Transition transition) {
    if (bloc is Bloc) {
      super.onTransition(bloc, transition);
      _logger.i('🔀 Transition: ${bloc.runtimeType}');
      _logger.i('   Event: ${transition.event.runtimeType}');
      _logger.i('   Current: ${transition.currentState.runtimeType}');
      _logger.i('   Next: ${transition.nextState.runtimeType}');
    }
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    _logger.e('❌ Bloc Error: ${bloc.runtimeType}');
    _logger.e('   Error: $error');
    _logger.e('   StackTrace: $stackTrace');
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    _logger.d('🔴 Bloc Closed: ${bloc.runtimeType}');
  }
}
