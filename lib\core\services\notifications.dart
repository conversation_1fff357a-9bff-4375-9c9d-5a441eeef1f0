// تصدير جميع خدمات الإشعارات
export 'firebase_messaging_service.dart';
export 'notification_service.dart';
export 'notification_manager.dart';
export 'appointment_notification_service.dart';
export 'task_notification_service.dart';

// استيراد مجمع لجميع خدمات الإشعارات
import 'firebase_messaging_service.dart';
import 'notification_service.dart';
import 'notification_manager.dart';
import 'appointment_notification_service.dart';
import 'task_notification_service.dart';

/// فئة مساعدة للوصول السريع لجميع خدمات الإشعارات
class NotificationServices {
  // Singleton instances
  static final FirebaseMessagingService messaging = FirebaseMessagingService();
  static final NotificationService notification = NotificationService();
  static final NotificationManager manager = NotificationManager();
  static final AppointmentNotificationService appointments = AppointmentNotificationService();
  static final TaskNotificationService tasks = TaskNotificationService();

  /// تهيئة جميع خدمات الإشعارات
  static Future<void> initializeAll() async {
    await messaging.initialize();
  }

  /// تنظيف جميع خدمات الإشعارات عند تسجيل الخروج
  static Future<void> cleanup() async {
    await messaging.clearToken();
  }

  /// تحديث معرف المستخدم عند تسجيل الدخول
  static Future<void> updateUser(String userId) async {
    await messaging.updateUserId(userId);
  }
}