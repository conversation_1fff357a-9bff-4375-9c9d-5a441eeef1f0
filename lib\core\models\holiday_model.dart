import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class HolidayModel extends Equatable {
  final String id;
  final DateTime holidayDate;
  final String occasionName;
  final String holidayType;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const HolidayModel({
    required this.id,
    required this.holidayDate,
    required this.occasionName,
    this.holidayType = 'official',
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get holiday type display name in Arabic
  String get holidayTypeDisplayName {
    switch (holidayType) {
      case 'official':
        return 'عطلة رسمية';
      case 'emergency':
        return 'إجازة طارئة';
      case 'personal':
        return 'إجازة شخصية';
      case 'maintenance':
        return 'صيانة العيادة';
      default:
        return 'عطلة رسمية';
    }
  }

  // Get formatted date string
  String get formattedDate {
    return '${holidayDate.day}/${holidayDate.month}/${holidayDate.year}';
  }

  // Check if holiday is today
  bool get isToday {
    final today = DateTime.now();
    return holidayDate.year == today.year &&
           holidayDate.month == today.month &&
           holidayDate.day == today.day;
  }

  // Check if this holiday applies to a given date
  bool appliesToDate(DateTime checkDate) {
    return holidayDate.year == checkDate.year &&
           holidayDate.month == checkDate.month &&
           holidayDate.day == checkDate.day;
  }

  // Check if holiday is in the future
  bool get isFuture {
    final today = DateTime.now();
    return holidayDate.isAfter(DateTime(today.year, today.month, today.day));
  }

  factory HolidayModel.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('🔄 HolidayModel.fromJson: Parsing JSON: $json');
      
      final id = json['id'] as String;
      final holidayDate = DateTime.parse(json['date'] as String);
      final occasionName = json['name'] as String;
      final holidayType = json['holiday_type'] as String? ?? 'official';
      final isActive = json['is_active'] as bool? ?? true;
      final notes = json['description'] as String?;
      final createdAt = DateTime.parse(json['created_at'] as String);
      final updatedAt = DateTime.parse(json['updated_at'] as String);
      
      debugPrint('✅ HolidayModel.fromJson: Successfully parsed holiday $id');
      
      return HolidayModel(
        id: id,
        holidayDate: holidayDate,
        occasionName: occasionName,
        holidayType: holidayType,
        isActive: isActive,
        notes: notes,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e, stackTrace) {
      debugPrint('❌ HolidayModel.fromJson: Error parsing JSON: $e');
      debugPrint('📍 HolidayModel.fromJson: JSON was: $json');
      debugPrint('📍 HolidayModel.fromJson: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': holidayDate.toIso8601String().split('T')[0],
      'name': occasionName,
      'description': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  HolidayModel copyWith({
    String? id,
    DateTime? holidayDate,
    String? occasionName,
    String? holidayType,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HolidayModel(
      id: id ?? this.id,
      holidayDate: holidayDate ?? this.holidayDate,
      occasionName: occasionName ?? this.occasionName,
      holidayType: holidayType ?? this.holidayType,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        holidayDate,
        occasionName,
        holidayType,
        isActive,
        notes,
        createdAt,
        updatedAt,
      ];
}
