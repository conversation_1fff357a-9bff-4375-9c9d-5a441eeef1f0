import 'package:equatable/equatable.dart';
import '../../../../core/models/sales_invoice_model.dart';

abstract class SalesState extends Equatable {
  const SalesState();

  @override
  List<Object?> get props => [];
}

class SalesInitial extends SalesState {}

class SalesLoading extends SalesState {}

class SalesLoaded extends SalesState {
  final List<SalesInvoiceModel> invoices;
  final bool hasReachedMax;
  final int currentPage;
  final String? searchQuery;
  final PaymentStatus? paymentStatusFilter;
  final InvoiceStatus? statusFilter;
  final PaymentType? paymentTypeFilter;

  const SalesLoaded({
    required this.invoices,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.searchQuery,
    this.paymentStatusFilter,
    this.statusFilter,
    this.paymentTypeFilter,
  });

  SalesLoaded copyWith({
    List<SalesInvoiceModel>? invoices,
    bool? hasReachedMax,
    int? currentPage,
    String? searchQuery,
    PaymentStatus? paymentStatusFilter,
    InvoiceStatus? statusFilter,
    bool clearSearchQuery = false,
    bool clearPaymentStatusFilter = false,
    bool clearStatusFilter = false,
  }) {
    return SalesLoaded(
      invoices: invoices ?? this.invoices,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      paymentStatusFilter: clearPaymentStatusFilter ? null : (paymentStatusFilter ?? this.paymentStatusFilter),
      statusFilter: clearStatusFilter ? null : (statusFilter ?? this.statusFilter),
    );
  }

  @override
  List<Object?> get props => [
        invoices,
        hasReachedMax,
        currentPage,
        searchQuery,
        paymentStatusFilter,
        statusFilter,
        paymentTypeFilter,
      ];
}

class InvoiceDetailsLoaded extends SalesState {
  final SalesInvoiceModel invoice;

  const InvoiceDetailsLoaded(this.invoice);

  @override
  List<Object?> get props => [invoice];
}

class InvoiceCreated extends SalesState {
  final SalesInvoiceModel invoice;

  const InvoiceCreated(this.invoice);

  @override
  List<Object?> get props => [invoice];
}

class InvoiceUpdated extends SalesState {
  final SalesInvoiceModel invoice;

  const InvoiceUpdated(this.invoice);

  @override
  List<Object?> get props => [invoice];
}

class PaymentAdded extends SalesState {
  final SalesInvoiceModel updatedInvoice;

  const PaymentAdded(this.updatedInvoice);

  @override
  List<Object?> get props => [updatedInvoice];
}

class InvoiceDeleted extends SalesState {
  final String deletedInvoiceId;

  const InvoiceDeleted(this.deletedInvoiceId);

  @override
  List<Object?> get props => [deletedInvoiceId];
}

class NumberGenerated extends SalesState {
  final String generatedNumber;
  final String type; // 'invoice' or 'payment'

  const NumberGenerated({
    required this.generatedNumber,
    required this.type,
  });

  @override
  List<Object?> get props => [generatedNumber, type];
}

class SalesError extends SalesState {
  final String message;

  const SalesError(this.message);

  @override
  List<Object?> get props => [message];
}
