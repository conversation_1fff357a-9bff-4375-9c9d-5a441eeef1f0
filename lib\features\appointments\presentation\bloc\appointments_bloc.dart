import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/appointments_repository.dart';
import 'appointments_event.dart';
import 'appointments_state.dart';
import '../../../../core/services/appointment_notification_service.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/admin_model.dart';

class AppointmentsBloc extends Bloc<AppointmentsEvent, AppointmentsState> {
  final AppointmentsRepository _appointmentsRepository;
  final AppointmentNotificationService _notificationService = AppointmentNotificationService();

  AppointmentsBloc({required AppointmentsRepository appointmentsRepository})
      : _appointmentsRepository = appointmentsRepository,
        super(AppointmentsInitial()) {
    on<LoadTodayAppointments>(_onLoadTodayAppointments);
    on<LoadAppointmentsByDate>(_onLoadAppointmentsByDate);
    on<LoadSpecialistAppointmentsByDate>(_onLoadSpecialistAppointmentsByDate);
    on<LoadPatientAppointments>(_onLoadPatientAppointments);
    on<LoadUpcomingAppointments>(_onLoadUpcomingAppointments);
    on<CreateAppointment>(_onCreateAppointment);
    on<UpdateAppointment>(_onUpdateAppointment);
    on<UpdateAppointmentStatus>(_onUpdateAppointmentStatus);
    on<DeleteAppointment>(_onDeleteAppointment);
    on<RefreshAppointments>(_onRefreshAppointments);
    on<ChangeSelectedDate>(_onChangeSelectedDate);
  }

  Future<void> _onLoadTodayAppointments(
    LoadTodayAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    debugPrint('🔄 AppointmentsBloc: Starting to load today appointments...');
    emit(AppointmentsLoading());
    try {
      final today = DateTime.now();
      debugPrint('📅 AppointmentsBloc: Today date: ${today.toIso8601String().split('T')[0]}');

      debugPrint('📞 AppointmentsBloc: Calling getAppointmentsByDate...');
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(today);
      debugPrint('📊 AppointmentsBloc: Received ${todayAppointments.length} today appointments');

      debugPrint('📞 AppointmentsBloc: Calling getUpcomingAppointments...');
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();
      debugPrint('📊 AppointmentsBloc: Received ${upcomingAppointments.length} upcoming appointments');

      emit(AppointmentsLoaded(
        appointments: todayAppointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: today,
      ));
      debugPrint('✅ AppointmentsBloc: Successfully emitted AppointmentsLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentsBloc Error: $e');
      debugPrint('📍 AppointmentsBloc Stack trace: $stackTrace');
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadAppointmentsByDate(
    LoadAppointmentsByDate event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final appointments = await _appointmentsRepository.getAppointmentsByDate(event.date);
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(DateTime.now());
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();

      emit(AppointmentsLoaded(
        appointments: appointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: event.date,
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadSpecialistAppointmentsByDate(
    LoadSpecialistAppointmentsByDate event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final appointments = await _appointmentsRepository.getSpecialistAppointmentsByDate(
        event.date,
        event.specialistId,
      );
      final todayAppointments = await _appointmentsRepository.getSpecialistAppointmentsByDate(
        DateTime.now(),
        event.specialistId,
      );
      final upcomingAppointments = await _appointmentsRepository.getSpecialistUpcomingAppointments(
        event.specialistId,
      );

      emit(AppointmentsLoaded(
        appointments: appointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: event.date,
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadPatientAppointments(
    LoadPatientAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final appointments = await _appointmentsRepository.getPatientAppointments(event.patientId);
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(DateTime.now());
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();

      emit(AppointmentsLoaded(
        appointments: appointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: DateTime.now(),
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadUpcomingAppointments(
    LoadUpcomingAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(DateTime.now());

      emit(AppointmentsLoaded(
        appointments: upcomingAppointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: DateTime.now(),
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onCreateAppointment(
    CreateAppointment event,
    Emitter<AppointmentsState> emit,
  ) async {
    try {
      final createdAppointment = await _appointmentsRepository.createAppointment(event.appointment);
      emit(AppointmentCreated(appointment: createdAppointment));

      // Refresh appointments
      add(LoadTodayAppointments());
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateAppointment(
    UpdateAppointment event,
    Emitter<AppointmentsState> emit,
  ) async {
    try {
      final updatedAppointment = await _appointmentsRepository.updateAppointment(event.appointment);
      emit(AppointmentUpdated(appointment: updatedAppointment));

      // Refresh appointments
      add(LoadTodayAppointments());
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateAppointmentStatus(
    UpdateAppointmentStatus event,
    Emitter<AppointmentsState> emit,
  ) async {
    debugPrint('🔄 AppointmentsBloc: Starting _onUpdateAppointmentStatus...');
    debugPrint('📋 AppointmentsBloc: Appointment ID: ${event.appointmentId}');
    debugPrint('📋 AppointmentsBloc: New Status: ${event.status}');

    try {
      debugPrint('🚀 AppointmentsBloc: Calling repository updateAppointmentStatus...');

      final updatedAppointment = await _appointmentsRepository.updateAppointmentStatus(
        event.appointmentId,
        event.status,
      );

      debugPrint('✅ AppointmentsBloc: Repository call successful');
      debugPrint('📋 AppointmentsBloc: Updated appointment: ${updatedAppointment.id}');

      // Send notification after successful status update
      debugPrint('📧 AppointmentsBloc: Sending notification for status change...');
      await _sendStatusChangeNotification(updatedAppointment, event.status);

      emit(AppointmentStatusUpdated(appointment: updatedAppointment));
      debugPrint('✅ AppointmentsBloc: AppointmentStatusUpdated state emitted');

      // Refresh appointments
      debugPrint('🔄 AppointmentsBloc: Adding LoadTodayAppointments event...');
      add(LoadTodayAppointments());
      debugPrint('✅ AppointmentsBloc: LoadTodayAppointments event added');

    } catch (e) {
      debugPrint('❌ AppointmentsBloc: Error in _onUpdateAppointmentStatus: $e');
      debugPrint('❌ AppointmentsBloc: Error type: ${e.runtimeType}');
      debugPrint('❌ AppointmentsBloc: Stack trace: ${StackTrace.current}');

      emit(AppointmentsError(message: e.toString()));
      debugPrint('❌ AppointmentsBloc: AppointmentsError state emitted');
    }
  }

  Future<void> _sendStatusChangeNotification(
    dynamic updatedAppointment,
    String newStatus,
  ) async {
    try {
      debugPrint('📧 AppointmentsBloc: Starting notification process...');
      
      // Get patient details
      if (updatedAppointment.patientId == null) {
        debugPrint('⚠️ AppointmentsBloc: No patient ID found, skipping notification');
        return;
      }

      debugPrint('🔍 AppointmentsBloc: Getting patient details for ID: ${updatedAppointment.patientId}');
      final patientResponse = await SupabaseConfig.patients
          .select('*')
          .eq('id', updatedAppointment.patientId)
          .maybeSingle();

      if (patientResponse == null) {
        debugPrint('⚠️ AppointmentsBloc: Patient not found, skipping notification');
        return;
      }

      final patient = PatientModel.fromJson(patientResponse);
      debugPrint('✅ AppointmentsBloc: Patient found: ${patient.name}');

      // Get employee details
      if (updatedAppointment.employeeId == null) {
        debugPrint('⚠️ AppointmentsBloc: No employee ID found, skipping notification');
        return;
      }

      debugPrint('🔍 AppointmentsBloc: Getting employee details for ID: ${updatedAppointment.employeeId}');
      final employeeResponse = await SupabaseConfig.admins
          .select('*')
          .eq('id', updatedAppointment.employeeId)
          .maybeSingle();

      if (employeeResponse == null) {
        debugPrint('⚠️ AppointmentsBloc: Employee not found, skipping notification');
        return;
      }

      final employee = EmployeeModel.fromJson(employeeResponse);
      debugPrint('✅ AppointmentsBloc: Employee found: ${employee.name}');

      // Send notification to patient
      if (patient.id.isNotEmpty) {
        debugPrint('📧 AppointmentsBloc: Sending notification to patient: ${patient.id}');

        // Send appropriate notification based on status
        switch (newStatus) {
          case 'cancelled':
            await _notificationService.notifyAppointmentCancelled(
              appointmentId: updatedAppointment.id,
              patientUserId: patient.id,
              patientName: patient.name,
              doctorName: employee.name,
              appointmentDate: updatedAppointment.appointmentDate,
            );
            break;
          case 'confirmed':
            await _notificationService.notifyAppointmentConfirmed(
              appointmentId: updatedAppointment.id,
              patientUserId: patient.id,
              patientName: patient.name,
              doctorName: employee.name,
              appointmentDate: updatedAppointment.appointmentDate,
            );
            break;
          default:
            await _notificationService.notifyAppointmentStatusChanged(
              appointmentId: updatedAppointment.id,
              patientUserId: patient.id,
              patientName: patient.name,
              doctorName: employee.name,
              appointmentDate: updatedAppointment.appointmentDate,
              newStatus: newStatus,
              statusDisplayName: _getStatusDisplayName(newStatus),
            );
        }

        debugPrint('✅ AppointmentsBloc: Patient notification sent successfully');
      }

      // Send notification to specialist for cancelled and no_show statuses
      if ((newStatus == 'cancelled' || newStatus == 'no_show') && employee.id.isNotEmpty) {
        debugPrint('📧 AppointmentsBloc: Sending notification to specialist: ${employee.id}');
        
        await _notificationService.notifySpecialistAppointmentStatusChanged(
          appointmentId: updatedAppointment.id,
          specialistUserId: employee.id,
          specialistName: employee.name,
          patientName: patient.name,
          appointmentDate: updatedAppointment.appointmentDate,
          newStatus: newStatus,
          statusDisplayName: _getStatusDisplayName(newStatus),
        );

        debugPrint('✅ AppointmentsBloc: Specialist notification sent successfully');
      }

    } catch (e) {
      debugPrint('❌ AppointmentsBloc: Error sending notification: $e');
      // Don't throw error, just log it - notification failure shouldn't break the flow
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'عدم حضور';
      default:
        return status;
    }
  }

  Future<void> _onDeleteAppointment(
    DeleteAppointment event,
    Emitter<AppointmentsState> emit,
  ) async {
    try {
      await _appointmentsRepository.deleteAppointment(event.appointmentId);
      emit(AppointmentDeleted(appointmentId: event.appointmentId));

      // Refresh appointments
      add(LoadTodayAppointments());
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshAppointments(
    RefreshAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    add(LoadTodayAppointments());
  }

  Future<void> _onChangeSelectedDate(
    ChangeSelectedDate event,
    Emitter<AppointmentsState> emit,
  ) async {
    add(LoadAppointmentsByDate(date: event.date));
  }
}