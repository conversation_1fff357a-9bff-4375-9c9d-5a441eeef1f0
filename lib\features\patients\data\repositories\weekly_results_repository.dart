import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/weekly_result_model.dart';

class WeeklyResultsRepository {
  // Get all weekly results for a patient
  Future<List<WeeklyResultModel>> getWeeklyResultsByPatientId(String patientId) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading weekly results for patient: $patientId');
      final response = await SupabaseConfig.weeklyResults
          .select()
          .eq('patient_id', patientId)
          .order('recorded_date', ascending: false);

      debugPrint('📊 WeeklyResultsRepository: Raw response: $response');
      debugPrint('📊 WeeklyResultsRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ WeeklyResultsRepository: No weekly results found for patient: $patientId');
        return [];
      }

      final weeklyResults = response.map((json) => WeeklyResultModel.fromJson(json)).toList();
      debugPrint('✅ WeeklyResultsRepository: Successfully parsed ${weeklyResults.length} weekly results');

      return weeklyResults;
    } catch (e, stackTrace) {
      debugPrint('❌ WeeklyResultsRepository: Error loading weekly results: $e');
      debugPrint('📍 WeeklyResultsRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب النتائج الأسبوعية: ${e.toString()}');
    }
  }

  // Get weekly result by ID
  Future<WeeklyResultModel?> getWeeklyResultById(String resultId) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading weekly result: $resultId');
      final response = await SupabaseConfig.weeklyResults
          .select()
          .eq('id', resultId)
          .single();

      final weeklyResult = WeeklyResultModel.fromJson(response);
      debugPrint('✅ WeeklyResultsRepository: Successfully loaded weekly result');

      return weeklyResult;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error loading weekly result: $e');
      return null;
    }
  }

  // Add new weekly result
  Future<WeeklyResultModel> addWeeklyResult(WeeklyResultModel weeklyResult) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Adding new weekly result for patient: ${weeklyResult.patientId}');

      // Don't include ID when inserting new record - let Supabase generate it
      final dataToInsert = weeklyResult.toJson(includeId: false);
      debugPrint('📤 WeeklyResultsRepository: Data to insert: $dataToInsert');

      final response = await SupabaseConfig.weeklyResults
          .insert(dataToInsert)
          .select()
          .single();

      final newWeeklyResult = WeeklyResultModel.fromJson(response);
      debugPrint('✅ WeeklyResultsRepository: Successfully added weekly result: ${newWeeklyResult.id}');

      return newWeeklyResult;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error adding weekly result: $e');
      throw Exception('فشل في إضافة النتيجة الأسبوعية: ${e.toString()}');
    }
  }

  // Update weekly result
  Future<WeeklyResultModel> updateWeeklyResult(WeeklyResultModel weeklyResult) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Updating weekly result: ${weeklyResult.id}');

      final response = await SupabaseConfig.weeklyResults
          .update(weeklyResult.toJson())
          .eq('id', weeklyResult.id)
          .select()
          .single();

      final updatedWeeklyResult = WeeklyResultModel.fromJson(response);
      debugPrint('✅ WeeklyResultsRepository: Successfully updated weekly result: ${updatedWeeklyResult.id}');

      return updatedWeeklyResult;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error updating weekly result: $e');
      throw Exception('فشل في تحديث النتيجة الأسبوعية: ${e.toString()}');
    }
  }

  // Delete weekly result
  Future<void> deleteWeeklyResult(String resultId) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Deleting weekly result: $resultId');

      await SupabaseConfig.weeklyResults
          .delete()
          .eq('id', resultId);

      debugPrint('✅ WeeklyResultsRepository: Successfully deleted weekly result: $resultId');
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error deleting weekly result: $e');
      throw Exception('فشل في حذف النتيجة الأسبوعية: ${e.toString()}');
    }
  }

  // Get weekly results by date range
  Future<List<WeeklyResultModel>> getWeeklyResultsByDateRange(
    String patientId,
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading weekly results by date range for patient: $patientId');

      final response = await SupabaseConfig.weeklyResults
          .select()
          .eq('patient_id', patientId)
          .gte('recorded_date', startDate.toIso8601String().split('T')[0])
          .lte('recorded_date', endDate.toIso8601String().split('T')[0])
          .order('recorded_date', ascending: false);

      final weeklyResults = response.map((json) => WeeklyResultModel.fromJson(json)).toList();
      debugPrint('✅ WeeklyResultsRepository: Found ${weeklyResults.length} weekly results in date range');

      return weeklyResults;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error loading weekly results by date range: $e');
      throw Exception('فشل في جلب النتائج حسب التاريخ: ${e.toString()}');
    }
  }

  // Get latest weekly result for patient
  Future<WeeklyResultModel?> getLatestWeeklyResult(String patientId) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading latest weekly result for patient: $patientId');

      final response = await SupabaseConfig.weeklyResults
          .select()
          .eq('patient_id', patientId)
          .order('recorded_date', ascending: false)
          .limit(1);

      if (response.isEmpty) {
        debugPrint('⚠️ WeeklyResultsRepository: No weekly results found for patient: $patientId');
        return null;
      }

      final latestResult = WeeklyResultModel.fromJson(response.first);
      debugPrint('✅ WeeklyResultsRepository: Successfully loaded latest weekly result');

      return latestResult;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error loading latest weekly result: $e');
      return null;
    }
  }

  // Get weekly results for last N weeks
  Future<List<WeeklyResultModel>> getRecentWeeklyResults(String patientId, {int weeks = 12}) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading recent weekly results for patient: $patientId (last $weeks weeks)');

      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: weeks * 7));

      return await getWeeklyResultsByDateRange(patientId, startDate, endDate);
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error loading recent weekly results: $e');
      throw Exception('فشل في جلب النتائج الحديثة: ${e.toString()}');
    }
  }

  // Check if result exists for specific date
  Future<bool> hasResultForDate(String patientId, DateTime date) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Checking if result exists for date: ${date.toIso8601String().split('T')[0]}');

      final response = await SupabaseConfig.weeklyResults
          .select('id')
          .eq('patient_id', patientId)
          .eq('recorded_date', date.toIso8601String().split('T')[0])
          .limit(1);

      final exists = response.isNotEmpty;
      debugPrint('✅ WeeklyResultsRepository: Result exists for date: $exists');

      return exists;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error checking result for date: $e');
      return false;
    }
  }

  // Get weight progress (weight changes over time)
  Future<List<Map<String, dynamic>>> getWeightProgress(String patientId, {int months = 6}) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading weight progress for patient: $patientId');

      final endDate = DateTime.now();
      final startDate = DateTime(endDate.year, endDate.month - months, endDate.day);

      final results = await getWeeklyResultsByDateRange(patientId, startDate, endDate);

      final progress = results
          .where((result) => result.weight != null)
          .map((result) => {
            'date': result.recordedDate,
            'weight': result.weight,
          })
          .toList();

      debugPrint('✅ WeeklyResultsRepository: Successfully loaded weight progress: ${progress.length} points');
      return progress;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error loading weight progress: $e');
      throw Exception('فشل في جلب تقدم الوزن: ${e.toString()}');
    }
  }

  // Get body composition progress
  Future<List<Map<String, dynamic>>> getBodyCompositionProgress(String patientId, {int months = 6}) async {
    try {
      debugPrint('🔍 WeeklyResultsRepository: Loading body composition progress for patient: $patientId');

      final endDate = DateTime.now();
      final startDate = DateTime(endDate.year, endDate.month - months, endDate.day);

      final results = await getWeeklyResultsByDateRange(patientId, startDate, endDate);

      final progress = results.map((result) => {
        'date': result.recordedDate,
        'weight': result.weight,
        'bodyFat': result.bodyFat,
        'visceralFat': result.visceralFat,
        'waterPercentage': result.waterPercentage,
        'muscleMass': result.muscleMass,
      }).toList();

      debugPrint('✅ WeeklyResultsRepository: Successfully loaded body composition progress: ${progress.length} points');
      return progress;
    } catch (e) {
      debugPrint('❌ WeeklyResultsRepository: Error loading body composition progress: $e');
      throw Exception('فشل في جلب تقدم تركيب الجسم: ${e.toString()}');
    }
  }
}
