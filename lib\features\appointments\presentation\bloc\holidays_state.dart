import 'package:equatable/equatable.dart';
import '../../../../core/models/holiday_model.dart';

abstract class HolidaysState extends Equatable {
  const HolidaysState();

  @override
  List<Object?> get props => [];
}

class HolidaysInitial extends HolidaysState {
  const HolidaysInitial();
}

class HolidaysLoading extends HolidaysState {
  const HolidaysLoading();
}

class HolidaysLoaded extends HolidaysState {
  final List<HolidayModel> holidays;
  final List<HolidayModel> activeHolidays;
  final List<HolidayModel> searchResults;
  final String searchQuery;

  const HolidaysLoaded({
    required this.holidays,
    this.activeHolidays = const [],
    this.searchResults = const [],
    this.searchQuery = '',
  });

  // Get holidays grouped by month
  Map<String, List<HolidayModel>> get holidaysByMonth {
    final Map<String, List<HolidayModel>> grouped = {};
    
    for (final holiday in holidays) {
      final monthKey = '${holiday.holidayDate.year}-${holiday.holidayDate.month.toString().padLeft(2, '0')}';
      if (!grouped.containsKey(monthKey)) {
        grouped[monthKey] = [];
      }
      grouped[monthKey]!.add(holiday);
    }
    
    return grouped;
  }

  // Get upcoming holidays (next 30 days)
  List<HolidayModel> get upcomingHolidays {
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));
    
    return activeHolidays.where((holiday) {
      return holiday.holidayDate.isAfter(now) && 
             holiday.holidayDate.isBefore(thirtyDaysFromNow);
    }).toList();
  }

  // Get today's holidays
  List<HolidayModel> get todaysHolidays {
    final today = DateTime.now();
    return activeHolidays.where((holiday) {
      return holiday.holidayDate.year == today.year &&
             holiday.holidayDate.month == today.month &&
             holiday.holidayDate.day == today.day;
    }).toList();
  }

  HolidaysLoaded copyWith({
    List<HolidayModel>? holidays,
    List<HolidayModel>? activeHolidays,
    List<HolidayModel>? searchResults,
    String? searchQuery,
  }) {
    return HolidaysLoaded(
      holidays: holidays ?? this.holidays,
      activeHolidays: activeHolidays ?? this.activeHolidays,
      searchResults: searchResults ?? this.searchResults,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [holidays, activeHolidays, searchResults, searchQuery];
}

class HolidayCheckResult extends HolidaysState {
  final DateTime checkedDate;
  final HolidayModel? holiday;
  final bool isHoliday;

  const HolidayCheckResult({
    required this.checkedDate,
    this.holiday,
    required this.isHoliday,
  });

  @override
  List<Object?> get props => [checkedDate, holiday, isHoliday];
}

class HolidayCreated extends HolidaysState {
  final HolidayModel holiday;

  const HolidayCreated({required this.holiday});

  @override
  List<Object?> get props => [holiday];
}

class HolidayUpdated extends HolidaysState {
  final HolidayModel holiday;

  const HolidayUpdated({required this.holiday});

  @override
  List<Object?> get props => [holiday];
}

class HolidayDeleted extends HolidaysState {
  final String holidayId;

  const HolidayDeleted({required this.holidayId});

  @override
  List<Object?> get props => [holidayId];
}

class HolidayStatusToggled extends HolidaysState {
  final HolidayModel holiday;

  const HolidayStatusToggled({required this.holiday});

  @override
  List<Object?> get props => [holiday];
}

class HolidaysError extends HolidaysState {
  final String message;

  const HolidaysError({required this.message});

  @override
  List<Object?> get props => [message];
}
