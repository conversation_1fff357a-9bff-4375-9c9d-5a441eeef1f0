import 'package:equatable/equatable.dart';
import '../../../../core/models/time_slot_model.dart';

abstract class TimeSlotsState extends Equatable {
  const TimeSlotsState();

  @override
  List<Object?> get props => [];
}

class TimeSlotsInitial extends TimeSlotsState {}

class TimeSlotsLoading extends TimeSlotsState {}

class TimeSlotsLoaded extends TimeSlotsState {
  final List<TimeSlotModel> timeSlots;
  final Map<int, List<TimeSlotModel>> timeSlotsByDay;

  const TimeSlotsLoaded({
    required this.timeSlots,
    required this.timeSlotsByDay,
  });

  @override
  List<Object?> get props => [timeSlots, timeSlotsByDay];

  TimeSlotsLoaded copyWith({
    List<TimeSlotModel>? timeSlots,
    Map<int, List<TimeSlotModel>>? timeSlotsByDay,
  }) {
    return TimeSlotsLoaded(
      timeSlots: timeSlots ?? this.timeSlots,
      timeSlotsByDay: timeSlotsByDay ?? this.timeSlotsByDay,
    );
  }
}

class TimeSlotsError extends TimeSlotsState {
  final String message;

  const TimeSlotsError({required this.message});

  @override
  List<Object?> get props => [message];
}

class TimeSlotCreated extends TimeSlotsState {
  final TimeSlotModel timeSlot;

  const TimeSlotCreated({required this.timeSlot});

  @override
  List<Object?> get props => [timeSlot];
}

class TimeSlotUpdated extends TimeSlotsState {
  final TimeSlotModel timeSlot;

  const TimeSlotUpdated({required this.timeSlot});

  @override
  List<Object?> get props => [timeSlot];
}

class TimeSlotDeleted extends TimeSlotsState {
  final String timeSlotId;

  const TimeSlotDeleted({required this.timeSlotId});

  @override
  List<Object?> get props => [timeSlotId];
}

class AppointmentsGenerated extends TimeSlotsState {
  final int weeksGenerated;

  const AppointmentsGenerated({required this.weeksGenerated});

  @override
  List<Object?> get props => [weeksGenerated];
}
