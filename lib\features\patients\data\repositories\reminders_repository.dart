import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/reminder_model.dart';

class RemindersRepository {
  // Get all reminders for a patient
  Future<List<ReminderModel>> getRemindersByPatientId(String patientId) async {
    try {
      debugPrint('🔍 RemindersRepository: Loading reminders for patient: $patientId');
      final response = await SupabaseConfig.reminders
          .select()
          .eq('patient_id', patientId)
          .order('reminder_time', ascending: true);

      debugPrint('📊 RemindersRepository: Raw response: $response');
      debugPrint('📊 RemindersRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ RemindersRepository: No reminders found for patient: $patientId');
        return [];
      }

      final reminders = response.map((json) => ReminderModel.fromJson(json)).toList();
      debugPrint('✅ RemindersRepository: Successfully parsed ${reminders.length} reminders');
      
      return reminders;
    } catch (e, stackTrace) {
      debugPrint('❌ RemindersRepository: Error loading reminders: $e');
      debugPrint('📍 RemindersRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب التذكيرات: ${e.toString()}');
    }
  }

  // Get reminders by type
  Future<List<ReminderModel>> getRemindersByType(String patientId, String reminderType) async {
    try {
      debugPrint('🔍 RemindersRepository: Loading reminders by type: $reminderType for patient: $patientId');
      final response = await SupabaseConfig.reminders
          .select()
          .eq('patient_id', patientId)
          .eq('reminder_type', reminderType)
          .order('reminder_time', ascending: true);

      final reminders = response.map((json) => ReminderModel.fromJson(json)).toList();
      debugPrint('✅ RemindersRepository: Found ${reminders.length} reminders of type: $reminderType');
      
      return reminders;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error loading reminders by type: $e');
      throw Exception('فشل في جلب التذكيرات حسب النوع: ${e.toString()}');
    }
  }

  // Get active reminders
  Future<List<ReminderModel>> getActiveReminders(String patientId) async {
    try {
      debugPrint('🔍 RemindersRepository: Loading active reminders for patient: $patientId');
      final response = await SupabaseConfig.reminders
          .select()
          .eq('patient_id', patientId)
          .eq('is_active', true)
          .order('reminder_time', ascending: true);

      final reminders = response.map((json) => ReminderModel.fromJson(json)).toList();
      debugPrint('✅ RemindersRepository: Found ${reminders.length} active reminders');
      
      return reminders;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error loading active reminders: $e');
      throw Exception('فشل في جلب التذكيرات النشطة: ${e.toString()}');
    }
  }

  // Add new reminder
  Future<ReminderModel> addReminder(ReminderModel reminder) async {
    try {
      debugPrint('🔍 RemindersRepository: Adding new reminder: ${reminder.title}');
      
      final response = await SupabaseConfig.reminders
          .insert(reminder.toJson())
          .select()
          .single();

      final newReminder = ReminderModel.fromJson(response);
      debugPrint('✅ RemindersRepository: Successfully added reminder: ${newReminder.id}');
      
      return newReminder;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error adding reminder: $e');
      throw Exception('فشل في إضافة التذكير: ${e.toString()}');
    }
  }

  // Update reminder
  Future<ReminderModel> updateReminder(ReminderModel reminder) async {
    try {
      debugPrint('🔍 RemindersRepository: Updating reminder: ${reminder.id}');
      
      final response = await SupabaseConfig.reminders
          .update(reminder.toJson())
          .eq('id', reminder.id)
          .select()
          .single();

      final updatedReminder = ReminderModel.fromJson(response);
      debugPrint('✅ RemindersRepository: Successfully updated reminder: ${updatedReminder.id}');
      
      return updatedReminder;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error updating reminder: $e');
      throw Exception('فشل في تحديث التذكير: ${e.toString()}');
    }
  }

  // Delete reminder
  Future<void> deleteReminder(String reminderId) async {
    try {
      debugPrint('🔍 RemindersRepository: Deleting reminder: $reminderId');
      
      await SupabaseConfig.reminders
          .delete()
          .eq('id', reminderId);

      debugPrint('✅ RemindersRepository: Successfully deleted reminder: $reminderId');
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error deleting reminder: $e');
      throw Exception('فشل في حذف التذكير: ${e.toString()}');
    }
  }

  // Toggle reminder status
  Future<ReminderModel> toggleReminderStatus(String reminderId, bool isActive) async {
    try {
      debugPrint('🔍 RemindersRepository: Toggling reminder status: $reminderId to $isActive');
      
      final response = await SupabaseConfig.reminders
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', reminderId)
          .select()
          .single();

      final updatedReminder = ReminderModel.fromJson(response);
      debugPrint('✅ RemindersRepository: Successfully toggled reminder status');
      
      return updatedReminder;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error toggling reminder status: $e');
      throw Exception('فشل في تغيير حالة التذكير: ${e.toString()}');
    }
  }

  // Search reminders
  Future<List<ReminderModel>> searchReminders(String patientId, String query) async {
    try {
      debugPrint('🔍 RemindersRepository: Searching reminders for patient: $patientId with query: $query');
      
      final response = await SupabaseConfig.reminders
          .select()
          .eq('patient_id', patientId)
          .or('title.ilike.%$query%,description.ilike.%$query%')
          .order('reminder_time', ascending: true);

      final reminders = response.map((json) => ReminderModel.fromJson(json)).toList();
      debugPrint('✅ RemindersRepository: Found ${reminders.length} reminders matching query');
      
      return reminders;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error searching reminders: $e');
      throw Exception('فشل في البحث عن التذكيرات: ${e.toString()}');
    }
  }

  // Get reminders for today
  Future<List<ReminderModel>> getTodayReminders(String patientId) async {
    try {
      debugPrint('🔍 RemindersRepository: Loading today reminders for patient: $patientId');
      
      final today = DateTime.now().weekday; // 1=Monday, 7=Sunday
      
      final response = await SupabaseConfig.reminders
          .select()
          .eq('patient_id', patientId)
          .eq('is_active', true)
          .order('reminder_time', ascending: true);

      final allReminders = response.map((json) => ReminderModel.fromJson(json)).toList();
      
      // Filter reminders for today
      final todayReminders = allReminders.where((reminder) {
        return reminder.daysOfWeek.contains(today);
      }).toList();
      
      debugPrint('✅ RemindersRepository: Found ${todayReminders.length} reminders for today');
      
      return todayReminders;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error loading today reminders: $e');
      throw Exception('فشل في جلب تذكيرات اليوم: ${e.toString()}');
    }
  }

  // Get reminders grouped by type
  Future<Map<String, List<ReminderModel>>> getRemindersGroupedByType(String patientId) async {
    try {
      debugPrint('🔍 RemindersRepository: Loading reminders grouped by type for patient: $patientId');
      
      final allReminders = await getRemindersByPatientId(patientId);
      final Map<String, List<ReminderModel>> grouped = {};
      
      for (final reminder in allReminders) {
        if (!grouped.containsKey(reminder.reminderType)) {
          grouped[reminder.reminderType] = [];
        }
        grouped[reminder.reminderType]!.add(reminder);
      }
      
      debugPrint('✅ RemindersRepository: Successfully grouped reminders by ${grouped.keys.length} types');
      return grouped;
    } catch (e) {
      debugPrint('❌ RemindersRepository: Error grouping reminders: $e');
      throw Exception('فشل في تجميع التذكيرات: ${e.toString()}');
    }
  }
}
