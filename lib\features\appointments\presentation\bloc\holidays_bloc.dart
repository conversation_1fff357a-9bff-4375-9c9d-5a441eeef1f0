import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/holidays_repository.dart';
import 'holidays_event.dart';
import 'holidays_state.dart';

class HolidaysBloc extends Bloc<HolidaysEvent, HolidaysState> {
  final HolidaysRepository _holidaysRepository;

  HolidaysBloc({required HolidaysRepository holidaysRepository})
      : _holidaysRepository = holidaysRepository,
        super(const HolidaysInitial()) {
    on<LoadAllHolidays>(_onLoadAllHolidays);
    on<LoadActiveHolidays>(_onLoadActiveHolidays);
    on<CheckHolidayByDate>(_onCheckHolidayByDate);
    on<LoadHolidaysInRange>(_onLoadHolidaysInRange);
    on<AddHoliday>(_onAddHoliday);
    on<UpdateHoliday>(_onUpdateHoliday);
    on<DeleteHoliday>(_onDeleteHoliday);
    on<ToggleHolidayStatus>(_onToggleHolidayStatus);
    on<SearchHolidays>(_onSearchHolidays);
    on<RefreshHolidays>(_onRefreshHolidays);
  }

  Future<void> _onLoadAllHolidays(
    LoadAllHolidays event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Starting to load all holidays...');
    emit(const HolidaysLoading());
    try {
      final holidays = await _holidaysRepository.getAllHolidays();
      final activeHolidays = holidays.where((h) => h.isActive).toList();
      
      debugPrint('📊 HolidaysBloc: Loaded ${holidays.length} holidays (${activeHolidays.length} active)');
      
      emit(HolidaysLoaded(
        holidays: holidays,
        activeHolidays: activeHolidays,
      ));
      debugPrint('✅ HolidaysBloc: Successfully emitted HolidaysLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ HolidaysBloc Error: $e');
      debugPrint('📍 HolidaysBloc Stack trace: $stackTrace');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onLoadActiveHolidays(
    LoadActiveHolidays event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Loading active holidays...');
    emit(const HolidaysLoading());
    try {
      final activeHolidays = await _holidaysRepository.getActiveHolidays();
      debugPrint('📊 HolidaysBloc: Loaded ${activeHolidays.length} active holidays');
      
      emit(HolidaysLoaded(
        holidays: activeHolidays,
        activeHolidays: activeHolidays,
      ));
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error loading active holidays: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onCheckHolidayByDate(
    CheckHolidayByDate event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Checking holiday for date: ${event.date}');
    try {
      final holiday = await _holidaysRepository.getHolidayByDate(event.date);
      debugPrint('📊 HolidaysBloc: Holiday check result: ${holiday?.occasionName ?? 'No holiday'}');
      
      emit(HolidayCheckResult(
        checkedDate: event.date,
        holiday: holiday,
        isHoliday: holiday != null,
      ));
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error checking holiday: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onLoadHolidaysInRange(
    LoadHolidaysInRange event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Loading holidays in range...');
    try {
      final holidays = await _holidaysRepository.getHolidaysInRange(
        event.startDate,
        event.endDate,
      );
      debugPrint('📊 HolidaysBloc: Found ${holidays.length} holidays in range');
      
      emit(HolidaysLoaded(
        holidays: holidays,
        activeHolidays: holidays,
      ));
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error loading holidays in range: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onAddHoliday(
    AddHoliday event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Adding new holiday...');
    try {
      final newHoliday = await _holidaysRepository.addHoliday(event.holiday);
      debugPrint('✅ HolidaysBloc: Successfully added holiday: ${newHoliday.occasionName}');
      
      emit(HolidayCreated(holiday: newHoliday));
      
      // Reload all holidays
      add(const LoadAllHolidays());
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error adding holiday: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onUpdateHoliday(
    UpdateHoliday event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Updating holiday...');
    try {
      final updatedHoliday = await _holidaysRepository.updateHoliday(event.holiday);
      debugPrint('✅ HolidaysBloc: Successfully updated holiday: ${updatedHoliday.occasionName}');
      
      emit(HolidayUpdated(holiday: updatedHoliday));
      
      // Reload all holidays
      add(const LoadAllHolidays());
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error updating holiday: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onDeleteHoliday(
    DeleteHoliday event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Deleting holiday...');
    try {
      await _holidaysRepository.deleteHoliday(event.holidayId);
      debugPrint('✅ HolidaysBloc: Successfully deleted holiday: ${event.holidayId}');
      
      emit(HolidayDeleted(holidayId: event.holidayId));
      
      // Reload all holidays
      add(const LoadAllHolidays());
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error deleting holiday: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onToggleHolidayStatus(
    ToggleHolidayStatus event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Toggling holiday status...');
    try {
      final updatedHoliday = await _holidaysRepository.toggleHolidayStatus(
        event.holidayId,
        event.isActive,
      );
      debugPrint('✅ HolidaysBloc: Successfully toggled holiday status');
      
      emit(HolidayStatusToggled(holiday: updatedHoliday));
      
      // Reload all holidays
      add(const LoadAllHolidays());
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error toggling holiday status: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onSearchHolidays(
    SearchHolidays event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Searching holidays with query: ${event.query}');
    try {
      if (event.query.isEmpty) {
        // If query is empty, reload all holidays
        add(const LoadAllHolidays());
        return;
      }

      final searchResults = await _holidaysRepository.searchHolidays(event.query);
      debugPrint('📊 HolidaysBloc: Found ${searchResults.length} holidays matching query');
      
      if (state is HolidaysLoaded) {
        final currentState = state as HolidaysLoaded;
        emit(currentState.copyWith(
          searchResults: searchResults,
          searchQuery: event.query,
        ));
      } else {
        emit(HolidaysLoaded(
          holidays: searchResults,
          searchResults: searchResults,
          searchQuery: event.query,
        ));
      }
    } catch (e) {
      debugPrint('❌ HolidaysBloc Error searching holidays: $e');
      emit(HolidaysError(message: e.toString()));
    }
  }

  Future<void> _onRefreshHolidays(
    RefreshHolidays event,
    Emitter<HolidaysState> emit,
  ) async {
    debugPrint('🔄 HolidaysBloc: Refreshing holidays...');
    add(const LoadAllHolidays());
  }
}
