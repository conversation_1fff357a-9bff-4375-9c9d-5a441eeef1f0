import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/models/medical_info_model.dart';
import '../../data/repositories/medical_info_repository.dart';
import 'medical_info_event.dart';
import 'medical_info_state.dart';

class MedicalInfoBloc extends Bloc<MedicalInfoEvent, MedicalInfoState> {
  final MedicalInfoRepository _medicalInfoRepository;

  MedicalInfoBloc({required MedicalInfoRepository medicalInfoRepository})
      : _medicalInfoRepository = medicalInfoRepository,
        super(const MedicalInfoInitial()) {
    on<LoadMedicalInfoByPatientId>(_onLoadMedicalInfoByPatientId);
    on<LoadMedicalInfoByType>(_onLoadMedicalInfoByType);
    on<AddMedicalInfo>(_onAddMedicalInfo);
    on<UpdateMedicalInfo>(_onUpdateMedicalInfo);
    on<DeleteMedicalInfo>(_onDeleteMedicalInfo);
    on<ToggleMedicalInfoStatus>(_onToggleMedicalInfoStatus);
    on<SearchMedicalInfo>(_onSearchMedicalInfo);
    on<RefreshMedicalInfo>(_onRefreshMedicalInfo);
  }

  Future<void> _onLoadMedicalInfoByPatientId(
    LoadMedicalInfoByPatientId event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Loading medical info for patient: ${event.patientId}');
    emit(const MedicalInfoLoading());
    try {
      final medicalInfoList = await _medicalInfoRepository.getMedicalInfoByPatientId(event.patientId);

      // Group by type
      final groupedByType = <String, List<MedicalInfoModel>>{};
      for (final info in medicalInfoList) {
        final type = info.infoType;
        if (!groupedByType.containsKey(type)) {
          groupedByType[type] = [];
        }
        groupedByType[type]!.add(info);
      }

      debugPrint('📊 MedicalInfoBloc: Loaded ${medicalInfoList.length} medical info items');

      emit(MedicalInfoLoaded(
        medicalInfoList: medicalInfoList,
        patientId: event.patientId,
        groupedByType: groupedByType,
      ));
      debugPrint('✅ MedicalInfoBloc: Successfully emitted MedicalInfoLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ MedicalInfoBloc Error: $e');
      debugPrint('📍 MedicalInfoBloc Stack trace: $stackTrace');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onLoadMedicalInfoByType(
    LoadMedicalInfoByType event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Loading medical info by type: ${event.infoType}');
    emit(const MedicalInfoLoading());
    try {
      final medicalInfoList = await _medicalInfoRepository.getMedicalInfoByType(
        event.patientId,
        event.infoType,
      );

      debugPrint('📊 MedicalInfoBloc: Loaded ${medicalInfoList.length} medical info items of type: ${event.infoType}');

      emit(MedicalInfoByTypeLoaded(
        medicalInfoList: medicalInfoList,
        patientId: event.patientId,
        infoType: event.infoType,
      ));
    } catch (e) {
      debugPrint('❌ MedicalInfoBloc Error loading by type: $e');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onAddMedicalInfo(
    AddMedicalInfo event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Adding new medical info');
    try {
      final newMedicalInfo = await _medicalInfoRepository.addMedicalInfo(event.medicalInfo);
      debugPrint('✅ MedicalInfoBloc: Successfully added medical info: ${newMedicalInfo.id}');

      emit(MedicalInfoCreated(medicalInfo: newMedicalInfo));

      // Reload medical info
      add(LoadMedicalInfoByPatientId(patientId: event.medicalInfo.patientId));
    } catch (e) {
      debugPrint('❌ MedicalInfoBloc Error adding medical info: $e');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onUpdateMedicalInfo(
    UpdateMedicalInfo event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Updating medical info');
    try {
      final updatedMedicalInfo = await _medicalInfoRepository.updateMedicalInfo(event.medicalInfo);
      debugPrint('✅ MedicalInfoBloc: Successfully updated medical info: ${updatedMedicalInfo.id}');

      emit(MedicalInfoUpdated(medicalInfo: updatedMedicalInfo));

      // Reload medical info
      add(LoadMedicalInfoByPatientId(patientId: event.medicalInfo.patientId));
    } catch (e) {
      debugPrint('❌ MedicalInfoBloc Error updating medical info: $e');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onDeleteMedicalInfo(
    DeleteMedicalInfo event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Deleting medical info');
    try {
      // Don't emit loading here as it's handled by UI
      await _medicalInfoRepository.deleteMedicalInfo(event.medicalInfoId);
      debugPrint('✅ MedicalInfoBloc: Successfully deleted medical info: ${event.medicalInfoId}');

      emit(MedicalInfoDeleted(medicalInfoId: event.medicalInfoId));

      // Reload medical info using patientId from event
      debugPrint('🔄 MedicalInfoBloc: Reloading data after deletion for patient: ${event.patientId}');
      add(LoadMedicalInfoByPatientId(patientId: event.patientId));
    } catch (e) {
      debugPrint('❌ MedicalInfoBloc Error deleting medical info: $e');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onToggleMedicalInfoStatus(
    ToggleMedicalInfoStatus event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Toggling medical info status');
    try {
      // Don't emit loading here as it's handled by UI
      await _medicalInfoRepository.toggleMedicalInfoStatus(
        event.medicalInfoId,
        event.isActive,
      );
      debugPrint('✅ MedicalInfoBloc: Successfully toggled medical info status');

      emit(MedicalInfoStatusToggled(
        medicalInfoId: event.medicalInfoId,
        isActive: event.isActive,
      ));

      // Reload medical info using patientId from event
      debugPrint('🔄 MedicalInfoBloc: Reloading data after status toggle for patient: ${event.patientId}');
      add(LoadMedicalInfoByPatientId(patientId: event.patientId));
    } catch (e) {
      debugPrint('❌ MedicalInfoBloc Error toggling status: $e');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onSearchMedicalInfo(
    SearchMedicalInfo event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Searching medical info with query: ${event.query}');
    try {
      final searchResults = await _medicalInfoRepository.searchMedicalInfo(
        event.patientId,
        event.query,
      );

      debugPrint('📊 MedicalInfoBloc: Found ${searchResults.length} medical info items matching query');

      if (state is MedicalInfoLoaded) {
        final currentState = state as MedicalInfoLoaded;
        // For search, we can update the current state or emit a new search state
        // Here we'll just reload with search results
        final groupedByType = <String, List<MedicalInfoModel>>{};
        for (final info in searchResults) {
          final type = info.infoType;
          if (!groupedByType.containsKey(type)) {
            groupedByType[type] = [];
          }
          groupedByType[type]!.add(info);
        }

        emit(currentState.copyWith(
          medicalInfoList: searchResults,
          groupedByType: groupedByType,
        ));
      }
    } catch (e) {
      debugPrint('❌ MedicalInfoBloc Error searching medical info: $e');
      emit(MedicalInfoError(message: e.toString()));
    }
  }

  Future<void> _onRefreshMedicalInfo(
    RefreshMedicalInfo event,
    Emitter<MedicalInfoState> emit,
  ) async {
    debugPrint('🔄 MedicalInfoBloc: Refreshing medical info');
    add(LoadMedicalInfoByPatientId(patientId: event.patientId));
  }
}
