import 'package:equatable/equatable.dart';
import '../../../../core/models/appointment_model.dart';

abstract class AppointmentBookingEvent extends Equatable {
  const AppointmentBookingEvent();

  @override
  List<Object?> get props => [];
}

class LoadAvailableSlotsForDate extends AppointmentBookingEvent {
  final DateTime date;

  const LoadAvailableSlotsForDate({required this.date});

  @override
  List<Object?> get props => [date];
}

class LoadPatientUpcomingAppointments extends AppointmentBookingEvent {
  final String patientId;

  const LoadPatientUpcomingAppointments({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadPatientPastAppointments extends AppointmentBookingEvent {
  final String patientId;

  const LoadPatientPastAppointments({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class BookAppointmentSlot extends AppointmentBookingEvent {
  final AppointmentModel appointment;

  const BookAppointmentSlot({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class RefreshBookingData extends AppointmentBookingEvent {
  final DateTime selectedDate;
  final String patientId;

  const RefreshBookingData({
    required this.selectedDate,
    required this.patientId,
  });

  @override
  List<Object?> get props => [selectedDate, patientId];
}
