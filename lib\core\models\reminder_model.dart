import 'package:equatable/equatable.dart';

class ReminderModel extends Equatable {
  final String id;
  final String patientId;
  final String reminderType;
  final String title;
  final String? description;
  final String reminderTime; // Time in HH:mm format
  final List<int> daysOfWeek; // 1=Monday, 7=Sunday
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ReminderModel({
    required this.id,
    required this.patientId,
    required this.reminderType,
    required this.title,
    this.description,
    required this.reminderTime,
    required this.daysOfWeek,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReminderModel.fromJson(Map<String, dynamic> json) {
    return ReminderModel(
      id: json['id'] as String,
      patientId: json['patient_id'] as String,
      reminderType: json['reminder_type'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      reminderTime: json['reminder_time'] as String,
      daysOfWeek: List<int>.from(json['days_of_week'] as List),
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'patient_id': patientId,
      'reminder_type': reminderType,
      'title': title,
      'description': description,
      'reminder_time': reminderTime,
      'days_of_week': daysOfWeek,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
    
    // Only include ID if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }
    
    return json;
  }

  ReminderModel copyWith({
    String? id,
    String? patientId,
    String? reminderType,
    String? title,
    String? description,
    String? reminderTime,
    List<int>? daysOfWeek,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReminderModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      reminderType: reminderType ?? this.reminderType,
      title: title ?? this.title,
      description: description ?? this.description,
      reminderTime: reminderTime ?? this.reminderTime,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get reminderTypeDisplayName {
    switch (reminderType.toLowerCase()) {
      case 'meal':
        return 'وجبة';
      case 'exercise':
        return 'نشاط بدني';
      case 'medication':
        return 'دواء';
      case 'water':
        return 'شرب الماء';
      default:
        return reminderType;
    }
  }

  String get daysOfWeekText {
    if (daysOfWeek.length == 7) {
      return 'يومياً';
    }
    
    final dayNames = {
      1: 'الإثنين',
      2: 'الثلاثاء',
      3: 'الأربعاء',
      4: 'الخميس',
      5: 'الجمعة',
      6: 'السبت',
      7: 'الأحد',
    };
    
    return daysOfWeek.map((day) => dayNames[day] ?? '').join(', ');
  }

  String get formattedTime {
    try {
      final parts = reminderTime.split(':');
      if (parts.length >= 2) {
        int hour = int.parse(parts[0]);
        int minute = int.parse(parts[1]);
        
        String period = hour >= 12 ? 'م' : 'ص';
        
        if (hour == 0) {
          hour = 12;
        } else if (hour > 12) {
          hour = hour - 12;
        }
        
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
      }
    } catch (e) {
      // Return original if parsing fails
    }
    return reminderTime;
  }

  bool get hasDescription => description != null && description!.isNotEmpty;

  @override
  List<Object?> get props => [
        id,
        patientId,
        reminderType,
        title,
        description,
        reminderTime,
        daysOfWeek,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ReminderModel(id: $id, patientId: $patientId, type: $reminderType, '
        'title: $title, time: $reminderTime, isActive: $isActive)';
  }
}
