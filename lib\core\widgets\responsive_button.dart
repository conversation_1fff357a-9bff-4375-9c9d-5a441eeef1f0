import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';

/// Widget للأزرار المتجاوبة مع أحجام الشاشات المختلفة
/// مصمم ليعمل بشكل مثالي على جميع أحجام الشاشات
class ResponsiveButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String text;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool isOutlined;
  final bool showLoadingState;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const ResponsiveButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.isOutlined = false,
    this.showLoadingState = true,
    this.height,
    this.padding,
    this.borderRadius,
  });

  @override
  State<ResponsiveButton> createState() => _ResponsiveButtonState();
}

class _ResponsiveButtonState extends State<ResponsiveButton> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان الافتراضية
    final backgroundColor = widget.backgroundColor ?? AppColors.primary;
    final foregroundColor =
        widget.foregroundColor ??
        (widget.isOutlined ? AppColors.primary : AppColors.white);

    // تحديد الارتفاع المناسب للشاشة
    final buttonHeight = widget.height ?? _getResponsiveHeight();

    // تحديد حجم الخط المناسب
    final fontSize = _getResponsiveFontSize();

    // تحديد حجم الأيقونة المناسب
    final iconSize = _getResponsiveIconSize();

    if (widget.isOutlined) {
      return SizedBox(
        height: buttonHeight,
        child: OutlinedButton(
          onPressed:
              widget.showLoadingState && _isLoading ? null : _handlePress,
          style: OutlinedButton.styleFrom(
            foregroundColor: foregroundColor,
            side: BorderSide(color: backgroundColor),
            padding:
                widget.padding ??
                EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
            shape: RoundedRectangleBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8.r),
            ),
          ),
          child: _buildButtonContent(iconSize, fontSize, foregroundColor),
        ),
      );
    } else {
      return SizedBox(
        height: buttonHeight,
        child: ElevatedButton(
          onPressed:
              widget.showLoadingState && _isLoading ? null : _handlePress,
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            padding:
                widget.padding ??
                EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
            shape: RoundedRectangleBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8.r),
            ),
          ),
          child: _buildButtonContent(iconSize, fontSize, foregroundColor),
        ),
      );
    }
  }

  Widget _buildIcon(double iconSize, Color color) {
    if (widget.showLoadingState && _isLoading) {
      return SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      );
    }

    if (widget.icon != null) {
      return Icon(widget.icon, size: iconSize);
    }

    return const SizedBox.shrink();
  }

  /// بناء محتوى الزر مع محاذاة مثالية للأيقونة والنص
  Widget _buildButtonContent(double iconSize, double fontSize, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center, // محاذاة عمودية في المنتصف
      children: [
        if (widget.icon != null || (widget.showLoadingState && _isLoading)) ...[
          _buildIcon(iconSize, color),
          SizedBox(width: 8.w), // مسافة بين الأيقونة والنص
        ],
        _buildCenteredLabel(fontSize, color),
      ],
    );
  }

  /// نص محاذي في المنتصف تماماً
  Widget _buildCenteredLabel(double fontSize, Color color) {
    return Text(
      widget.showLoadingState && _isLoading ? 'جاري التحميل...' : widget.text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w500,
        color: color,
        height: 1.0, // ارتفاع سطر ثابت
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      textAlign: TextAlign.center,
    );
  }

  Future<void> _handlePress() async {
    if (widget.onPressed == null) return;

    if (widget.showLoadingState) {
      setState(() => _isLoading = true);

      // تأخير قصير لإظهار loading state
      await Future.delayed(const Duration(milliseconds: 100));
    }

    widget.onPressed!();

    if (widget.showLoadingState && mounted) {
      // إعادة تعيين الحالة بعد فترة قصيرة
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// تحديد الارتفاع المناسب بناءً على حجم الشاشة
  double _getResponsiveHeight() {
    final screenHeight = MediaQuery.of(context).size.height;

    if (screenHeight > 800) {
      // شاشات كبيرة (تابلت)
      return 48.h;
    } else if (screenHeight > 600) {
      // شاشات متوسطة
      return 42.h;
    } else {
      // شاشات صغيرة
      return 38.h;
    }
  }

  /// تحديد حجم الخط المناسب بناءً على حجم الشاشة
  double _getResponsiveFontSize() {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 600) {
      // شاشات كبيرة (تابلت)
      return 16.sp;
    } else if (screenWidth > 400) {
      // شاشات متوسطة
      return 14.sp;
    } else {
      // شاشات صغيرة
      return 12.sp;
    }
  }

  /// تحديد حجم الأيقونة المناسب بناءً على حجم الشاشة
  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 600) {
      // شاشات كبيرة (تابلت)
      return 20.w;
    } else if (screenWidth > 400) {
      // شاشات متوسطة
      return 18.w;
    } else {
      // شاشات صغيرة
      return 16.w;
    }
  }
}

/// Widget مخصص لزر صفحة المريض (نسخة محسنة من الموجود)
class PatientPageButton extends StatelessWidget {
  final VoidCallback onPressed;

  const PatientPageButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ResponsiveButton(
      onPressed: onPressed,
      text: 'صفحة المريض',
      icon: Icons.person,
      isOutlined: true,
      showLoadingState: true,
    );
  }
}

/// Widget مخصص لزر حجز الاستشارة
class BookAppointmentButton extends StatelessWidget {
  final VoidCallback onPressed;

  const BookAppointmentButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ResponsiveButton(
      onPressed: onPressed,
      text: 'حجز استشارة',
      icon: Icons.calendar_today,
      showLoadingState: false,
    );
  }
}

/// Widget مخصص لزر إدارة الصلاحيات
class PermissionsButton extends StatelessWidget {
  final VoidCallback onPressed;

  const PermissionsButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ResponsiveButton(
      onPressed: onPressed,
      text: 'إدارة الصلاحيات',
      icon: Icons.admin_panel_settings,
      showLoadingState: false,
    );
  }
}

/// Widget مخصص لزر تسجيل الخروج
class LogoutButton extends StatelessWidget {
  final VoidCallback onPressed;

  const LogoutButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ResponsiveButton(
      onPressed: onPressed,
      text: 'تسجيل الخروج',
      icon: Icons.logout,
      backgroundColor: AppColors.error,
      showLoadingState: false,
    );
  }
}
