import 'package:equatable/equatable.dart';
import '../../../../core/models/lab_test_model.dart';

abstract class LabTestsState extends Equatable {
  const LabTestsState();

  @override
  List<Object?> get props => [];
}

class LabTestsInitial extends LabTestsState {
  const LabTestsInitial();
}

class LabTestsLoading extends LabTestsState {
  const LabTestsLoading();
}

class LabTestsLoaded extends LabTestsState {
  final List<LabTestModel> labTests;
  final String patientId;
  final List<LabTestModel>? searchResults;
  final String? searchQuery;

  const LabTestsLoaded({
    required this.labTests,
    required this.patientId,
    this.searchResults,
    this.searchQuery,
  });

  LabTestsLoaded copyWith({
    List<LabTestModel>? labTests,
    String? patientId,
    List<LabTestModel>? searchResults,
    String? searchQuery,
  }) {
    return LabTestsLoaded(
      labTests: labTests ?? this.labTests,
      patientId: patientId ?? this.patientId,
      searchResults: searchResults ?? this.searchResults,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [labTests, patientId, searchResults, searchQuery];
}

class LabTestsByTypeLoaded extends LabTestsState {
  final List<LabTestModel> labTests;
  final String patientId;
  final String testType;

  const LabTestsByTypeLoaded({
    required this.labTests,
    required this.patientId,
    required this.testType,
  });

  @override
  List<Object?> get props => [labTests, patientId, testType];
}

class LabTestsByDateRangeLoaded extends LabTestsState {
  final List<LabTestModel> labTests;
  final String patientId;
  final DateTime startDate;
  final DateTime endDate;

  const LabTestsByDateRangeLoaded({
    required this.labTests,
    required this.patientId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [labTests, patientId, startDate, endDate];
}

class LabTestCreated extends LabTestsState {
  final LabTestModel labTest;

  const LabTestCreated({required this.labTest});

  @override
  List<Object?> get props => [labTest];
}

class LabTestUpdated extends LabTestsState {
  final LabTestModel labTest;

  const LabTestUpdated({required this.labTest});

  @override
  List<Object?> get props => [labTest];
}

class LabTestDeleted extends LabTestsState {
  final String labTestId;

  const LabTestDeleted({required this.labTestId});

  @override
  List<Object?> get props => [labTestId];
}

class LabTestImageUploaded extends LabTestsState {
  final String imageUrl;

  const LabTestImageUploaded({required this.imageUrl});

  @override
  List<Object?> get props => [imageUrl];
}

class LabTestImageDeleted extends LabTestsState {
  final String imageUrl;

  const LabTestImageDeleted({required this.imageUrl});

  @override
  List<Object?> get props => [imageUrl];
}

class LabTestsError extends LabTestsState {
  final String message;

  const LabTestsError({required this.message});

  @override
  List<Object?> get props => [message];
}
