import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/models/financial_summary_model.dart';
import '../../data/repositories/financial_summary_repository.dart';

// States
abstract class InventoryState extends Equatable {
  const InventoryState();

  @override
  List<Object?> get props => [];
}

class InventoryInitial extends InventoryState {}

class InventoryLoading extends InventoryState {}

class InventoryLoaded extends InventoryState {
  final FinancialSummaryModel summary;
  final Map<String, double> salesByCategory;
  final Map<String, double> expensesByCategory;
  final Map<String, int> appointmentsByStatus;
  final List<Map<String, dynamic>> dailyStatistics;
  final DateTime startDate;
  final DateTime endDate;

  const InventoryLoaded({
    required this.summary,
    required this.salesByCategory,
    required this.expensesByCategory,
    required this.appointmentsByStatus,
    required this.dailyStatistics,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [
    summary,
    salesByCategory,
    expensesByCategory,
    appointmentsByStatus,
    dailyStatistics,
    startDate,
    endDate,
  ];
}

class InventoryError extends InventoryState {
  final String message;

  const InventoryError(this.message);

  @override
  List<Object?> get props => [message];
}

// Cubit
class InventoryCubit extends Cubit<InventoryState> {
  final FinancialSummaryRepository _repository;

  InventoryCubit(this._repository) : super(InventoryInitial());

  /// تحميل البيانات المالية للفترة المحددة
  Future<void> loadFinancialData({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      emit(InventoryLoading());
      
      debugPrint('🔄 InventoryCubit: Loading financial data...');
      
      // تحديد الفترة الزمنية (افتراضياً الشهر الحالي)
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? DateTime(now.year, now.month + 1, 0, 23, 59, 59);
      
      debugPrint('📅 Date range: ${start.toString()} to ${end.toString()}');

      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        _repository.getFinancialSummary(startDate: start, endDate: end),
        _repository.getSalesByCategory(startDate: start, endDate: end),
        _repository.getExpensesByCategory(startDate: start, endDate: end),
        _repository.getAppointmentsByStatus(startDate: start, endDate: end),
        _repository.getDailyStatistics(startDate: start, endDate: end),
      ]);

      final summary = results[0] as FinancialSummaryModel;
      final salesByCategory = results[1] as Map<String, double>;
      final expensesByCategory = results[2] as Map<String, double>;
      final appointmentsByStatus = results[3] as Map<String, int>;
      final dailyStatistics = results[4] as List<Map<String, dynamic>>;

      debugPrint('✅ InventoryCubit: Data loaded successfully');
      debugPrint('💰 Total income: ${summary.totalIncome}');
      debugPrint('💸 Total expenses: ${summary.totalExpenses}');
      debugPrint('📊 Net profit: ${summary.netProfit}');

      emit(InventoryLoaded(
        summary: summary,
        salesByCategory: salesByCategory,
        expensesByCategory: expensesByCategory,
        appointmentsByStatus: appointmentsByStatus,
        dailyStatistics: dailyStatistics,
        startDate: start,
        endDate: end,
      ));
      
    } catch (e, stackTrace) {
      debugPrint('❌ InventoryCubit: Error loading financial data: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      emit(InventoryError('حدث خطأ في تحميل البيانات المالية: ${e.toString()}'));
    }
  }

  /// تحديث الفترة الزمنية وإعادة تحميل البيانات
  Future<void> updateDateRange(DateTime startDate, DateTime endDate) async {
    await loadFinancialData(startDate: startDate, endDate: endDate);
  }

  /// تحديث البيانات (إعادة تحميل بنفس الفترة الحالية)
  Future<void> refreshData() async {
    if (state is InventoryLoaded) {
      final currentState = state as InventoryLoaded;
      await loadFinancialData(
        startDate: currentState.startDate,
        endDate: currentState.endDate,
      );
    } else {
      await loadFinancialData();
    }
  }

  /// تحميل بيانات الشهر الحالي
  Future<void> loadCurrentMonth() async {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, 1);
    final endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    
    await loadFinancialData(startDate: startDate, endDate: endDate);
  }

  /// تحميل بيانات الشهر الماضي
  Future<void> loadLastMonth() async {
    final now = DateTime.now();
    final lastMonth = DateTime(now.year, now.month - 1, 1);
    final startDate = DateTime(lastMonth.year, lastMonth.month, 1);
    final endDate = DateTime(lastMonth.year, lastMonth.month + 1, 0, 23, 59, 59);
    
    await loadFinancialData(startDate: startDate, endDate: endDate);
  }

  /// تحميل بيانات السنة الحالية
  Future<void> loadCurrentYear() async {
    final now = DateTime.now();
    final startDate = DateTime(now.year, 1, 1);
    final endDate = DateTime(now.year, 12, 31, 23, 59, 59);
    
    await loadFinancialData(startDate: startDate, endDate: endDate);
  }

  /// تحميل بيانات آخر 7 أيام
  Future<void> loadLastWeek() async {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day - 7);
    final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    
    await loadFinancialData(startDate: startDate, endDate: endDate);
  }

  /// تحميل بيانات آخر 30 يوم
  Future<void> loadLast30Days() async {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day - 30);
    final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    
    await loadFinancialData(startDate: startDate, endDate: endDate);
  }
}