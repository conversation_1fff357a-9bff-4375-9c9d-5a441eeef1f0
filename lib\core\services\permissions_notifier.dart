import 'package:flutter/foundation.dart';
import '../enums/user_role.dart';
import '../models/admin_user_model.dart';

/// خدمة إدارة الصلاحيات مع دعم الإشعارات
/// تستخدم ChangeNotifier لتحديث الواجهة عند تغيير الصلاحيات
class PermissionsNotifier extends ChangeNotifier {
  static final PermissionsNotifier _instance = PermissionsNotifier._internal();
  factory PermissionsNotifier() => _instance;
  PermissionsNotifier._internal();

  UserRole? _currentUserRole;
  AdminUserModel? _currentUser;

  // تهيئة الصلاحيات عند تسجيل الدخول
  void initializePermissions(AdminUserModel user) {
    _currentUser = user;
    _currentUserRole = UserRole.fromString(user.role);
    debugPrint('🔐 PermissionsNotifier: Permissions initialized for user: ${user.name}');
    debugPrint('🔐 PermissionsNotifier: User role string: ${user.role}');
    debugPrint('🔐 PermissionsNotifier: Parsed UserRole: ${_currentUserRole?.displayName}');
    debugPrint('🔐 PermissionsNotifier: Can access products: $canAccessProducts');
    debugPrint('🔐 PermissionsNotifier: Can access employees: $canAccessEmployees');
    debugPrint('🔐 PermissionsNotifier: Can access inventory: $canAccessInventory');
    debugPrint('🔐 PermissionsNotifier: Can access patients: $canAccessPatients');
    debugPrint('🔐 PermissionsNotifier: Can access sales: $canAccessSales');
    
    // إشعار المستمعين بالتغيير
    notifyListeners();
  }

  // مسح الصلاحيات عند تسجيل الخروج
  void clearPermissions() {
    _currentUser = null;
    _currentUserRole = null;
    debugPrint('🔐 PermissionsNotifier: Permissions cleared');
    
    // إشعار المستمعين بالتغيير
    notifyListeners();
  }

  // تحديث صلاحيات المستخدم
  void updateUserRole(UserRole newRole) {
    if (_currentUser != null) {
      _currentUserRole = newRole;
      debugPrint('🔐 PermissionsNotifier: User role updated to: ${newRole.displayName}');
      
      // إشعار المستمعين بالتغيير
      notifyListeners();
    }
  }

  // تحديث حالة نشاط الحساب
  void updateAccountStatus(bool isActive) {
    if (_currentUser != null) {
      _currentUser = _currentUser!.copyWith(isActive: isActive);
      debugPrint('🔐 PermissionsNotifier: Account status updated to: $isActive');
      
      // إشعار المستمعين بالتغيير
      notifyListeners();
    }
  }

  // الحصول على الدور الحالي
  UserRole? get currentUserRole => _currentUserRole;
  AdminUserModel? get currentUser => _currentUser;

  // التحقق من صحة المستخدم
  bool get isValidUser => _currentUser != null && _currentUserRole != null;

  // صلاحيات الوصول للصفحات
  bool get canAccessProducts => _currentUserRole?.canAccessProducts ?? false;
  bool get canAccessEmployees => _currentUserRole?.canAccessEmployees ?? false;
  bool get canAccessInventory => _currentUserRole?.canAccessInventory ?? false;
  bool get canAccessPatients => _currentUserRole?.canAccessPatients ?? false;
  bool get canAccessSales => _currentUserRole?.canAccessSales ?? false;
  bool get canAccessAppointments => _currentUserRole?.canAccessAppointments ?? false;
  bool get canAccessSettings => _currentUserRole?.canAccessSettings ?? false;
  bool get canAccessTasks => _currentUserRole?.canAccessTasks ?? false;

  // صلاحيات الإعدادات
  bool get canAccessPermissionsSettings => _currentUserRole?.canAccessPermissionsSettings ?? false;
  bool get canAccessAppSettings => _currentUserRole?.canAccessAppSettings ?? false;

  // صلاحي��ت الحجوزات
  bool get canEditAppointments => _currentUserRole?.canEditAppointments ?? false;
  bool get canPrintAppointments => _currentUserRole?.canPrintAppointments ?? false;
  bool get canChangeAppointmentStatus => _currentUserRole?.canChangeAppointmentStatus ?? false;
  bool get canViewAppointmentDetails => _currentUserRole?.canViewAppointmentDetails ?? false;

  // صلاحيات المرضى
  bool get canViewPatientPhone => _currentUserRole?.canViewPatientPhone ?? false;
  bool get canViewPatientEmail => _currentUserRole?.canViewPatientEmail ?? false;

  // التحقق من نشاط الحساب
  bool get isAccountActive => _currentUser?.isActive ?? false;

  // الحصول على قائمة الصفحات المسموحة للمستخدم
  List<String> getAllowedPages() {
    final List<String> allowedPages = [];

    if (canAccessAppointments) allowedPages.add('appointments');
    if (canAccessTasks) allowedPages.add('tasks');
    if (canAccessPatients) allowedPages.add('patients');
    if (canAccessSales) allowedPages.add('sales');
    if (canAccessProducts) allowedPages.add('products');
    if (canAccessEmployees) allowedPages.add('employees');
    if (canAccessInventory) allowedPages.add('inventory');
    if (canAccessSettings) allowedPages.add('settings');

    debugPrint('🔐 PermissionsNotifier: Allowed pages for ${_currentUserRole?.displayName}: $allowedPages');
    return allowedPages;
  }

  // التحقق من صلاحية الوصول لصفحة معينة
  bool canAccessPage(String pageName) {
    final hasAccess = getAllowedPages().contains(pageName);
    debugPrint('🔐 PermissionsNotifier: Can access $pageName: $hasAccess (Role: ${_currentUserRole?.displayName})');
    return hasAccess;
  }

  // رسائل الخطأ
  String get accessDeniedMessage {
    switch (_currentUserRole) {
      case UserRole.manager:
      case UserRole.admin:
      case UserRole.superAdmin:
        return 'ليس لديك صلاحية للوصول لهذه الصفحة';
      case UserRole.specialist:
        return 'هذه الصفحة متاحة للمديرين فقط';
      case UserRole.receptionist:
        return 'هذه الصفحة غير متاحة لموظفي الاستقبال';
      default:
        return 'ليس لديك صلاحية للوصول لهذه الصفحة';
    }
  }

  String get accountSuspendedMessage => 'حسابك موقوف. يرجى التواصل مع الإدارة';
  String get unauthorizedAccessMessage => 'ليس لديك صلاحيات الدخول. هذا التطبيق خاص بالعاملين في المركز';

  // دوال مساعدة للتحقق من الصلاحيات مع إشعارات
  bool hasPermission(String permission) {
    switch (permission.toLowerCase()) {
      case 'products':
        return canAccessProducts;
      case 'employees':
        return canAccessEmployees;
      case 'inventory':
        return canAccessInventory;
      case 'patients':
        return canAccessPatients;
      case 'sales':
        return canAccessSales;
      case 'appointments':
        return canAccessAppointments;
      case 'settings':
        return canAccessSettings;
      case 'tasks':
        return canAccessTasks;
      default:
        return false;
    }
  }

  // إعادة تحميل الصلاحيات
  void refreshPermissions() {
    if (_currentUser != null) {
      debugPrint('🔐 PermissionsNotifier: Refreshing permissions for user: ${_currentUser!.name}');
      notifyListeners();
    }
  }
}